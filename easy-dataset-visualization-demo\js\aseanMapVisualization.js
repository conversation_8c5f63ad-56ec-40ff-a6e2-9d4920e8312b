// 东盟十国地图可视化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化地图和事件处理的函数
    initializeAseanMap();
    
    // 初始化地图和事件处理的函数
    function initializeAseanMap() {
        // 初始化地图
        const aseanMapChart = echarts.init(document.getElementById('asean-map'));
        
        // 注册地图图表实例到响应式系统
        if (typeof registerChart === 'function') {
            registerChart(aseanMapChart);
        }
        
        // 定义东盟十国
        const aseanCountries = [
            'Indonesia', 'Malaysia', 'Philippines', 'Singapore', 'Thailand', 
            'Brunei', 'Vietnam', 'Lao PDR', 'Myanmar', 'Cambodia'
        ];
        
        // 国家名称中英文对照
        const countryNameMap = {
            'Indonesia': '印度尼西亚',
            'Malaysia': '马来西亚',
            'Philippines': '菲律宾',
            'Singapore': '新加坡',
            'Thailand': '泰国',
            'Brunei': '文莱',
            'Vietnam': '越南',
            'Lao PDR': '老挝',
            'Myanmar': '缅甸',
            'Cambodia': '柬埔寨'
        };
        
        // 模拟数据 - 实际项目中可以从API获取或使用真实数据
        const countryData = {
            'Indonesia': {
                // 按年份组织数据
                '2019': {
                    population: 270.6, // 单位：百万
                    gdp: 1119.2, // 单位：十亿美元
                    gdpGrowth: 5.0, // GDP增长率
                    trade: 338.5, // 单位：十亿美元
                    socialIndex: 70 // 社会发展指数（模拟数据）
                },
                '2020': {
                    population: 271.9,
                    gdp: 1059.6,
                    gdpGrowth: 5.2,
                    trade: 325.2,
                    socialIndex: 71
                },
                '2021': {
                    population: 272.5,
                    gdp: 1058.4,
                    gdpGrowth: -2.1,
                    trade: 330.6,
                    socialIndex: 71
                },
                '2022': {
                    population: 273.0,
                    gdp: 1186.5,
                    gdpGrowth: 3.7,
                    trade: 335.8,
                    socialIndex: 72
                },
                '2023': {
                    population: 273.5,
                    gdp: 1289.0,
                    gdpGrowth: 5.3,
                    trade: 338.5,
                    socialIndex: 72
                },
                // 不同数据类型的指标
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Malaysia': {
                '2019': {
                    population: 31.5,
                    gdp: 364.7,
                    gdpGrowth: 4.4,
                    trade: 443.1,
                    socialIndex: 76
                },
                '2020': {
                    population: 31.9,
                    gdp: 336.7,
                    gdpGrowth: 4.3,
                    trade: 428.6,
                    socialIndex: 77
                },
                '2021': {
                    population: 32.0,
                    gdp: 347.4,
                    gdpGrowth: -5.6,
                    trade: 435.2,
                    socialIndex: 77
                },
                '2022': {
                    population: 32.2,
                    gdp: 372.9,
                    gdpGrowth: 3.1,
                    trade: 440.8,
                    socialIndex: 78
                },
                '2023': {
                    population: 32.4,
                    gdp: 406.5,
                    gdpGrowth: 4.5,
                    trade: 443.1,
                    socialIndex: 78
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Philippines': {
                '2019': {
                    population: 107.3,
                    gdp: 376.8,
                    gdpGrowth: 6.0,
                    trade: 192.8,
                    socialIndex: 66
                },
                '2020': {
                    population: 108.1,
                    gdp: 361.5,
                    gdpGrowth: 5.9,
                    trade: 177.5,
                    socialIndex: 67
                },
                '2021': {
                    population: 108.7,
                    gdp: 362.2,
                    gdpGrowth: -9.5,
                    trade: 182.3,
                    socialIndex: 67
                },
                '2022': {
                    population: 109.2,
                    gdp: 394.1,
                    gdpGrowth: 5.7,
                    trade: 188.6,
                    socialIndex: 68
                },
                '2023': {
                    population: 109.6,
                    gdp: 404.3,
                    gdpGrowth: 6.4,
                    trade: 192.8,
                    socialIndex: 68
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Singapore': {
                '2019': {
                    population: 5.7,
                    gdp: 372.1,
                    gdpGrowth: 1.3,
                    trade: 783.4,
                    socialIndex: 90
                },
                '2020': {
                    population: 5.7,
                    gdp: 339.0,
                    gdpGrowth: 1.1,
                    trade: 740.2,
                    socialIndex: 91
                },
                '2021': {
                    population: 5.7,
                    gdp: 340.0,
                    gdpGrowth: -4.1,
                    trade: 752.5,
                    socialIndex: 91
                },
                '2022': {
                    population: 5.7,
                    gdp: 378.6,
                    gdpGrowth: 7.6,
                    trade: 770.1,
                    socialIndex: 92
                },
                '2023': {
                    population: 5.7,
                    gdp: 397.1,
                    gdpGrowth: 3.6,
                    trade: 783.4,
                    socialIndex: 92
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Thailand': {
                '2019': {
                    population: 69.5,
                    gdp: 543.7,
                    gdpGrowth: 2.3,
                    trade: 535.7,
                    socialIndex: 74
                },
                '2020': {
                    population: 69.6,
                    gdp: 501.8,
                    gdpGrowth: 2.2,
                    trade: 515.3,
                    socialIndex: 75
                },
                '2021': {
                    population: 69.7,
                    gdp: 505.9,
                    gdpGrowth: -6.1,
                    trade: 520.6,
                    socialIndex: 75
                },
                '2022': {
                    population: 69.8,
                    gdp: 495.3,
                    gdpGrowth: 1.5,
                    trade: 528.9,
                    socialIndex: 76
                },
                '2023': {
                    population: 69.8,
                    gdp: 512.2,
                    gdpGrowth: 2.6,
                    trade: 535.7,
                    socialIndex: 76
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Brunei': {
                '2019': {
                    population: 0.4,
                    gdp: 13.5,
                    gdpGrowth: 3.9,
                    trade: 12.9,
                    socialIndex: 78
                },
                '2020': {
                    population: 0.4,
                    gdp: 12.0,
                    gdpGrowth: 3.5,
                    trade: 11.8,
                    socialIndex: 79
                },
                '2021': {
                    population: 0.4,
                    gdp: 12.5,
                    gdpGrowth: 1.1,
                    trade: 12.2,
                    socialIndex: 79
                },
                '2022': {
                    population: 0.4,
                    gdp: 14.0,
                    gdpGrowth: -1.6,
                    trade: 12.6,
                    socialIndex: 80
                },
                '2023': {
                    population: 0.4,
                    gdp: 14.3,
                    gdpGrowth: 1.0,
                    trade: 12.9,
                    socialIndex: 80
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Vietnam': {
                '2019': {
                    population: 96.5,
                    gdp: 261.9,
                    gdpGrowth: 7.0,
                    trade: 668.5,
                    socialIndex: 68
                },
                '2020': {
                    population: 96.9,
                    gdp: 271.2,
                    gdpGrowth: 7.2,
                    trade: 545.3,
                    socialIndex: 69
                },
                '2021': {
                    population: 97.1,
                    gdp: 282.4,
                    gdpGrowth: 2.9,
                    trade: 600.2,
                    socialIndex: 69
                },
                '2022': {
                    population: 97.2,
                    gdp: 366.1,
                    gdpGrowth: 2.6,
                    trade: 650.8,
                    socialIndex: 70
                },
                '2023': {
                    population: 97.3,
                    gdp: 408.9,
                    gdpGrowth: 5.8,
                    trade: 668.5,
                    socialIndex: 70
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Lao PDR': {
                '2019': {
                    population: 7.1,
                    gdp: 18.8,
                    gdpGrowth: 4.7,
                    trade: 13.1,
                    socialIndex: 60
                },
                '2020': {
                    population: 7.1,
                    gdp: 19.1,
                    gdpGrowth: 5.5,
                    trade: 11.9,
                    socialIndex: 61
                },
                '2021': {
                    population: 7.2,
                    gdp: 19.2,
                    gdpGrowth: -0.5,
                    trade: 12.3,
                    socialIndex: 61
                },
                '2022': {
                    population: 7.2,
                    gdp: 14.0,
                    gdpGrowth: 2.5,
                    trade: 12.8,
                    socialIndex: 62
                },
                '2023': {
                    population: 7.2,
                    gdp: 14.5,
                    gdpGrowth: 3.7,
                    trade: 13.1,
                    socialIndex: 62
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Myanmar': {
                '2019': {
                    population: 54.0,
                    gdp: 65.7,
                    gdpGrowth: 6.8,
                    trade: 36.7,
                    socialIndex: 56
                },
                '2020': {
                    population: 54.2,
                    gdp: 76.2,
                    gdpGrowth: 6.5,
                    trade: 34.2,
                    socialIndex: 57
                },
                '2021': {
                    population: 54.3,
                    gdp: 65.1,
                    gdpGrowth: 3.2,
                    trade: 35.1,
                    socialIndex: 57
                },
                '2022': {
                    population: 54.4,
                    gdp: 53.7,
                    gdpGrowth: -18.0,
                    trade: 36.0,
                    socialIndex: 58
                },
                '2023': {
                    population: 54.4,
                    gdp: 55.8,
                    gdpGrowth: 2.0,
                    trade: 36.7,
                    socialIndex: 58
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            },
            'Cambodia': {
                '2019': {
                    population: 16.5,
                    gdp: 25.8,
                    gdpGrowth: 7.1,
                    trade: 44.2,
                    socialIndex: 62
                },
                '2020': {
                    population: 16.6,
                    gdp: 26.9,
                    gdpGrowth: 7.0,
                    trade: 40.1,
                    socialIndex: 63
                },
                '2021': {
                    population: 16.7,
                    gdp: 26.3,
                    gdpGrowth: -3.1,
                    trade: 41.5,
                    socialIndex: 63
                },
                '2022': {
                    population: 16.7,
                    gdp: 26.7,
                    gdpGrowth: 3.0,
                    trade: 43.1,
                    socialIndex: 64
                },
                '2023': {
                    population: 16.7,
                    gdp: 28.4,
                    gdpGrowth: 5.2,
                    trade: 44.2,
                    socialIndex: 64
                },
                'economic': {
                    indicators: ['gdp', 'gdpGrowth', 'trade'],
                    mainIndicator: 'gdp'
                },
                'population': {
                    indicators: ['population'],
                    mainIndicator: 'population'
                },
                'social': {
                    indicators: ['socialIndex'],
                    mainIndicator: 'socialIndex'
                }
            }
        };
        
        // 当前选择的年份和数据类型
        let currentYear = document.getElementById('year-filter').value || '2023';
        let currentDataType = document.getElementById('data-type').value || 'economic';
        
        // 准备地图数据
        const mapData = [];
        for (const country of aseanCountries) {
            if (countryData[country] && countryData[country][currentYear]) {
                // 获取当前数据类型的主要指标
                const mainIndicator = countryData[country][currentDataType]?.mainIndicator || 'socialIndex';
                const value = countryData[country][currentYear][mainIndicator];
                mapData.push({
                    name: country,
                    value: value
                });
            }
        }
        
        // 地图配置选项
        const option = {
            title: {
                text: `东盟十国地图 (${currentYear}年)`,
                left: 'center',
                textStyle: {
                    color: '#333',
                    fontSize: 18,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: function(params) {
                    const country = params.name;
                    const chineseName = countryNameMap[country] || country;
                    const data = countryData[country] && countryData[country][currentYear];
                    
                    if (!data) return chineseName;
                    
                    // 根据当前数据类型显示不同的指标
                    let tooltipContent = `<div style="font-weight:bold;margin-bottom:5px;">${chineseName} (${currentYear}年)</div>`;
                    
                    if (currentDataType === 'economic') {
                        tooltipContent += `
                            <div>GDP：${data.gdp} 十亿美元</div>
                            <div>GDP增长率：${data.gdpGrowth}%</div>
                            <div>贸易额：${data.trade} 十亿美元</div>
                        `;
                    } else if (currentDataType === 'population') {
                        tooltipContent += `<div>人口：${data.population} 百万</div>`;
                    } else if (currentDataType === 'social') {
                        tooltipContent += `<div>社会发展指数：${data.socialIndex}</div>`;
                    }
                    
                    return tooltipContent;
                }
            },
            // 工具箱
            toolbox: {
                show: true,
                orient: 'vertical',
                left: 'right',
                top: 'center',
                feature: {
                    dataView: { readOnly: false },
                    restore: {},
                    saveAsImage: {}
                }
            },
            // 视觉映射
            visualMap: {
                show: true,
                left: 20,
                bottom: 40,
                calculable: true,
                text: ['高', '低'],
                // 根据数据类型设置不同的范围
                min: currentDataType === 'economic' ? 0 : (currentDataType === 'population' ? 0 : 50),
                max: currentDataType === 'economic' ? 1300 : (currentDataType === 'population' ? 300 : 100),
                inRange: {
                    color: ['lightskyblue', 'yellow', 'orangered']
                }
            },
            // 地理坐标系
            geo: {
                map: 'world',
                roam: true,
                scaleLimit: {
                    min: 1,
                    max: 10
                },
                zoom: 5,
                center: [110, 10], // 东盟地区的大致中心位置
                label: {
                    show: true,
                    formatter: function(params) {
                        // 只显示东盟国家的名称
                        if (aseanCountries.includes(params.name)) {
                            return countryNameMap[params.name] || params.name;
                        }
                        return '';
                    },
                    color: 'black',
                    fontSize: 10
                },
                itemStyle: {
                    normal: {
                        shadowBlur: 20,
                        shadowColor: 'rgba(0, 0, 0, 0.3)',
                        borderColor: 'rgba(0, 0, 0, 0.2)'
                    },
                    emphasis: {
                        areaColor: '#f2d5ad',
                        shadowOffsetX: 0,
                        shadowOffsetY: 0,
                        borderWidth: 0
                    }
                }
            },
            series: [
                {
                    name: '东盟国家',
                    type: 'map',
                    geoIndex: 0,
                    data: mapData,
                    nameMap: countryNameMap,
                    selectedMode: 'single', // 只能选择一个国家
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 12,
                            fontWeight: 'bold'
                        },
                        itemStyle: {
                            areaColor: '#66ccff'
                        }
                    },
                    select: {
                        itemStyle: {
                            areaColor: '#1976d2',
                            borderWidth: 2
                        },
                        label: {
                            show: true,
                            color: '#fff',
                            fontWeight: 'bold'
                        }
                    }
                }
            ]
        };
        
        // 设置地图配置并渲染
        aseanMapChart.setOption(option);
        
        // 当前选中的国家
        let currentCountry = null;
        // 比较模式状态
        let isComparisonMode = false;
        // 比较模式中选择的国家
        let comparisonCountries = {
            first: null,
            second: null
        };
        
        // 点击事件处理
        aseanMapChart.on('click', function(params) {
            const countryName = params.name;
            
            // 检查是否为东盟国家
            if (!aseanCountries.includes(countryName)) {
                return;
            }
            
            // 比较模式下的处理
            if (isComparisonMode) {
                // 如果第一个国家未选择，或者点击的是已选择的第一个国家
                if (!comparisonCountries.first || comparisonCountries.first === countryName) {
                    comparisonCountries.first = countryName;
                    document.getElementById('country-1').value = countryName;
                    
                    // 更新地图选中状态
                    updateMapSelection();
                    
                    // 如果两个国家都已选择，进行比较
                    if (comparisonCountries.first && comparisonCountries.second) {
                        compareCountries(comparisonCountries.first, comparisonCountries.second);
                    }
                    
                    return;
                }
                
                // 如果第二个国家未选择，或者点击的是已选择的第二个国家
                if (!comparisonCountries.second || comparisonCountries.second === countryName) {
                    comparisonCountries.second = countryName;
                    document.getElementById('country-2').value = countryName;
                    
                    // 更新地图选中状态
                    updateMapSelection();
                    
                    // 如果两个国家都已选择，进行比较
                    if (comparisonCountries.first && comparisonCountries.second) {
                        compareCountries(comparisonCountries.first, comparisonCountries.second);
                    }
                    
                    return;
                }
                
                // 如果已经选择了两个国家，替换第二个国家
                comparisonCountries.second = countryName;
                document.getElementById('country-2').value = countryName;
                
                // 更新地图选中状态
                updateMapSelection();
                
                // 进行比较
                compareCountries(comparisonCountries.first, comparisonCountries.second);
                
                return;
            }
            
            // 非比较模式下的处理
            currentCountry = countryName;
            const chineseName = countryNameMap[countryName];
            const countryInfo = countryData[countryName] && countryData[countryName][currentYear];
            
            if (!countryInfo) return;
            
            // 显示数据面板
            const dataPanel = document.getElementById('data-panel');
            dataPanel.style.display = 'block';
            
            // 使用setTimeout确保display属性生效后再添加active类
            setTimeout(() => {
                dataPanel.classList.add('active');
            }, 10);
            
            // 更新国家信息
            document.getElementById('country-name').innerText = `${chineseName} (${currentYear}年)`;
            
            // 根据当前数据类型显示不同的指标
            let basicInfoHTML = '';
            
            // 始终显示基本信息
            basicInfoHTML += `<p>人口：${countryInfo.population} 百万</p>`;
            
            // 根据数据类型显示额外信息
            if (currentDataType === 'economic' || currentDataType === 'all') {
                basicInfoHTML += `
                    <p>GDP：${countryInfo.gdp} 十亿美元</p>
                    <p>GDP增长率：${countryInfo.gdpGrowth}%</p>
                    <p>贸易额：${countryInfo.trade} 十亿美元</p>
                `;
            }
            
            if (currentDataType === 'social' || currentDataType === 'all') {
                basicInfoHTML += `<p>社会发展指数：${countryInfo.socialIndex}</p>`;
            }
            
            document.getElementById('country-basic-info').innerHTML = basicInfoHTML;
            
            // 创建图表
            createCharts(countryName);
        });
        
        // 更新地图选中状态
        function updateMapSelection() {
            // 重置选中状态
            const newData = mapData.map(item => {
                return {
                    ...item,
                    itemStyle: {
                        opacity: 1
                    }
                };
            });
            
            // 在比较模式下，高亮选中的国家
            if (isComparisonMode) {
                for (let i = 0; i < newData.length; i++) {
                    if (newData[i].name === comparisonCountries.first) {
                        newData[i] = {
                            ...newData[i],
                            itemStyle: {
                                color: '#1976d2',
                                borderWidth: 2,
                                borderColor: '#fff'
                            }
                        };
                    } else if (newData[i].name === comparisonCountries.second) {
                        newData[i] = {
                            ...newData[i],
                            itemStyle: {
                                color: '#ff9800',
                                borderWidth: 2,
                                borderColor: '#fff'
                            }
                        };
                    }
                }
            } else if (currentCountry) {
                // 在非比较模式下，高亮当前选中的国家
                for (let i = 0; i < newData.length; i++) {
                    if (newData[i].name === currentCountry) {
                        newData[i] = {
                            ...newData[i],
                            itemStyle: {
                                color: '#1976d2',
                                borderWidth: 2,
                                borderColor: '#fff'
                            }
                        };
                    }
                }
            }
            
            // 创建一个新的选项对象，避免修改原始选项
            const newOption = {
                series: [{
                    data: newData
                }]
            };
            
            aseanMapChart.setOption(newOption);
        }
        
        // 创建各种图表
        function createCharts(countryName) {
            // 获取所有年份的数据
            const countryAllData = countryData[countryName];
            if (!countryAllData) return;
            
            // 获取当前年份的数据
            const currentData = countryAllData[currentYear];
            if (!currentData) return;
            
            // 获取所有年份的数据数组
            const years = ['2019', '2020', '2021', '2022', '2023'];
            const gdpGrowthData = years.map(year => countryAllData[year]?.gdpGrowth || 0);
            const gdpData = years.map(year => countryAllData[year]?.gdp || 0);
            const populationData = years.map(year => countryAllData[year]?.population || 0);
            const tradeData = years.map(year => countryAllData[year]?.trade || 0);
            const socialIndexData = years.map(year => countryAllData[year]?.socialIndex || 0);
            
            // GDP增长趋势图
            const gdpChart = echarts.init(document.getElementById('gdp-chart'));
            
            // 注册图表实例到响应式系统
            if (typeof registerChart === 'function') {
                registerChart(gdpChart);
            }
            
            // 根据当前数据类型决定显示哪些图表
            if (currentDataType === 'economic' || currentDataType === 'all') {
                // 显示GDP相关图表
                gdpChart.setOption({
                    title: {
                        text: 'GDP增长趋势 (2019-2023)',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            const year = params[0].axisValue;
                            let tooltipContent = `${year}年<br/>`;
                            
                            params.forEach(param => {
                                if (param.seriesName === 'GDP增长率') {
                                    tooltipContent += `${param.seriesName}: ${param.value}%<br/>`;
                                } else {
                                    tooltipContent += `${param.seriesName}: ${param.value} 十亿美元<br/>`;
                                }
                            });
                            
                            return tooltipContent;
                        }
                    },
                    legend: {
                        data: ['GDP增长率', 'GDP总量'],
                        bottom: 0
                    },
                    grid: {
                        top: 60,
                        bottom: 60
                    },
                    xAxis: {
                        type: 'category',
                        data: years
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '增长率(%)',
                            position: 'left',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        {
                            type: 'value',
                            name: 'GDP(十亿美元)',
                            position: 'right',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        }
                    ],
                    series: [
                        {
                            name: 'GDP增长率',
                            data: gdpGrowthData,
                            type: 'line',
                            smooth: true,
                            lineStyle: {
                                width: 3,
                                color: '#1976d2'
                            },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 0, y2: 1,
                                    colorStops: [{
                                        offset: 0, color: 'rgba(25, 118, 210, 0.5)'
                                    }, {
                                        offset: 1, color: 'rgba(25, 118, 210, 0.05)'
                                    }]
                                }
                            }
                        },
                        {
                            name: 'GDP总量',
                            data: gdpData,
                            type: 'bar',
                            yAxisIndex: 1,
                            itemStyle: {
                                color: '#ff9800'
                            }
                        }
                    ]
                });
                
                // 显示GDP图表容器
                document.querySelector('.chart-wrapper:nth-child(2)').style.display = 'block';
            } else {
                // 隐藏GDP图表容器
                document.querySelector('.chart-wrapper:nth-child(2)').style.display = 'none';
            }
            
            // 人口统计图
            const populationChart = echarts.init(document.getElementById('population-chart'));
            
            // 注册图表实例到响应式系统
            if (typeof registerChart === 'function') {
                registerChart(populationChart);
            }
            
            if (currentDataType === 'population' || currentDataType === 'all') {
                // 显示人口相关图表
                populationChart.setOption({
                    title: {
                        text: '人口统计趋势 (2019-2023)',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: '{b}年: {c} 百万'
                    },
                    grid: {
                        top: 60,
                        bottom: 60
                    },
                    xAxis: {
                        type: 'category',
                        data: years
                    },
                    yAxis: {
                        type: 'value',
                        name: '人口(百万)',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    },
                    series: [{
                        name: '人口',
                        data: populationData,
                        type: 'line',
                        smooth: true,
                        symbolSize: 10,
                        lineStyle: {
                            width: 4,
                            color: '#4caf50'
                        },
                        itemStyle: {
                            color: '#4caf50'
                        },
                        markPoint: {
                            data: [
                                { type: 'max', name: '最大值' },
                                { type: 'min', name: '最小值' }
                            ]
                        }
                    }]
                });
                
                // 显示人口图表容器
                document.querySelector('.chart-wrapper:nth-child(3)').style.display = 'block';
            } else {
                // 隐藏人口图表容器
                document.querySelector('.chart-wrapper:nth-child(3)').style.display = 'none';
            }
            
            // 贸易数据图
            const tradeChart = echarts.init(document.getElementById('trade-chart'));
            
            // 注册图表实例到响应式系统
            if (typeof registerChart === 'function') {
                registerChart(tradeChart);
            }
            
            if (currentDataType === 'economic' || currentDataType === 'social' || currentDataType === 'all') {
                // 显示贸易或社会发展相关图表
                let chartTitle, chartType, chartData, chartColors;
                
                if (currentDataType === 'economic') {
                    chartTitle = '贸易数据趋势 (2019-2023)';
                    chartType = 'line';
                    chartData = [
                        {
                            name: '贸易额',
                            data: tradeData,
                            type: 'line',
                            smooth: true,
                            lineStyle: { width: 3, color: '#9c27b0' },
                            itemStyle: { color: '#9c27b0' }
                        }
                    ];
                } else if (currentDataType === 'social') {
                    chartTitle = '社会发展指数趋势 (2019-2023)';
                    chartType = 'line';
                    chartData = [
                        {
                            name: '社会发展指数',
                            data: socialIndexData,
                            type: 'line',
                            smooth: true,
                            lineStyle: { width: 3, color: '#2196f3' },
                            itemStyle: { color: '#2196f3' }
                        }
                    ];
                } else {
                    // 综合数据
                    chartTitle = '综合指标对比 (2023)';
                    chartType = 'radar';
                    chartData = [
                        {
                            value: [
                                currentData.gdp / 13, // 缩放GDP以适应雷达图
                                currentData.population / 3, // 缩放人口以适应雷达图
                                currentData.trade / 8, // 缩放贸易额以适应雷达图
                                currentData.socialIndex,
                                currentData.gdpGrowth * 10 // 缩放GDP增长率以适应雷达图
                            ],
                            name: countryNameMap[countryName],
                            areaStyle: {}
                        }
                    ];
                }
                
                if (currentDataType === 'economic' || currentDataType === 'social') {
                    tradeChart.setOption({
                        title: {
                            text: chartTitle,
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        grid: {
                            top: 60,
                            bottom: 60
                        },
                        xAxis: {
                            type: 'category',
                            data: years
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: chartData
                    });
                } else {
                    tradeChart.setOption({
                        title: {
                            text: chartTitle,
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item'
                        },
                        radar: {
                            indicator: [
                                { name: 'GDP', max: 100 },
                                { name: '人口', max: 100 },
                                { name: '贸易额', max: 100 },
                                { name: '社会发展', max: 100 },
                                { name: 'GDP增长', max: 100 }
                            ]
                        },
                        series: [
                            {
                                type: 'radar',
                                data: chartData
                            }
                        ]
                    });
                }
                
                // 显示贸易/社会发展图表容器
                document.querySelector('.chart-wrapper:nth-child(4)').style.display = 'block';
            } else {
                // 隐藏贸易/社会发展图表容器
                document.querySelector('.chart-wrapper:nth-child(4)').style.display = 'none';
            }
            
            // 响应窗口大小变化
            window.addEventListener('resize', function() {
                gdpChart.resize();
                populationChart.resize();
                tradeChart.resize();
            });
        }
        
        // 响应窗口大小变化
        window.addEventListener('resize', function() {
            aseanMapChart.resize();
        });
        
        // 比较模式按钮事件
        document.getElementById('compare-btn').addEventListener('click', function() {
            const comparisonPanel = document.getElementById('comparison-panel');
            const dataPanel = document.getElementById('data-panel');
            
            // 切换比较模式状态
            isComparisonMode = !isComparisonMode;
            
            if (!isComparisonMode) {
                // 关闭比较模式
                comparisonPanel.classList.remove('active');
                
                // 重置比较国家选择
                comparisonCountries.first = null;
                comparisonCountries.second = null;
                document.getElementById('country-1').value = '';
                document.getElementById('country-2').value = '';
                
                // 使用setTimeout确保动画效果完成后再隐藏元素
                setTimeout(() => {
                    comparisonPanel.style.display = 'none';
                    this.textContent = '开启比较模式';
                    
                    // 如果有选中的国家，显示数据面板
                    if (currentCountry) {
                        dataPanel.style.display = 'block';
                        
                        // 添加active类触发动画
                        setTimeout(() => {
                            dataPanel.classList.add('active');
                        }, 10);
                    }
                }, 500); // 等待500毫秒，与CSS过渡时间匹配
                
                // 更新地图选中状态
                updateMapSelection();
            } else {
                // 开启比较模式
                
                // 如果当前有选中的国家，将其设为第一个比较国家
                if (currentCountry) {
                    comparisonCountries.first = currentCountry;
                    document.getElementById('country-1').value = currentCountry;
                }
                
                // 如果数据面板正在显示，先移除active类
                if (dataPanel.style.display === 'block') {
                    dataPanel.classList.remove('active');
                    
                    // 等待动画完成后隐藏数据面板
                    setTimeout(() => {
                        dataPanel.style.display = 'none';
                        
                        // 显示比较面板
                        comparisonPanel.style.display = 'block';
                        
                        // 添加active类触发动画
                        setTimeout(() => {
                            comparisonPanel.classList.add('active');
                        }, 10);
                    }, 500);
                } else {
                    // 直接显示比较面板
                    comparisonPanel.style.display = 'block';
                    
                    // 添加active类触发动画
                    setTimeout(() => {
                        comparisonPanel.classList.add('active');
                    }, 10);
                }
                
                this.textContent = '关闭比较模式';
                
                // 更新地图选中状态
                updateMapSelection();
                
                // 如果已经选择了两个国家，进行比较
                if (comparisonCountries.first && comparisonCountries.second) {
                    compareCountries(comparisonCountries.first, comparisonCountries.second);
                }
            }
        });
        
        // 国家选择下拉框事件
        document.getElementById('country-1').addEventListener('change', function() {
            comparisonCountries.first = this.value;
            updateMapSelection();
            
            // 如果两个国家都已选择，进行比较
            if (comparisonCountries.first && comparisonCountries.second) {
                compareCountries(comparisonCountries.first, comparisonCountries.second);
            }
        });
        
        document.getElementById('country-2').addEventListener('change', function() {
            comparisonCountries.second = this.value;
            updateMapSelection();
            
            // 如果两个国家都已选择，进行比较
            if (comparisonCountries.first && comparisonCountries.second) {
                compareCountries(comparisonCountries.first, comparisonCountries.second);
            }
        });
        
        // 比较国家按钮事件
        document.getElementById('compare-countries-btn').addEventListener('click', function() {
            const country1 = document.getElementById('country-1').value;
            const country2 = document.getElementById('country-2').value;
            
            if (!country1 || !country2 || country1 === country2) {
                alert('请选择两个不同的国家进行比较');
                return;
            }
            
            comparisonCountries.first = country1;
            comparisonCountries.second = country2;
            
            // 更新地图选中状态
            updateMapSelection();
            
            // 进行比较
            compareCountries(country1, country2);
        });
        
        // 比较两个国家的数据
        function compareCountries(country1, country2) {
            const data1 = countryData[country1];
            const data2 = countryData[country2];
            
            if (!data1 || !data2) return;
            
            const name1 = countryNameMap[country1];
            const name2 = countryNameMap[country2];
            
            // 获取年份数据
            const years = ['2019', '2020', '2021', '2022', '2023'];
            const gdpGrowth1 = years.map(year => data1[year]?.gdpGrowth || 0);
            const gdpGrowth2 = years.map(year => data2[year]?.gdpGrowth || 0);
            const gdp1 = years.map(year => data1[year]?.gdp || 0);
            const gdp2 = years.map(year => data2[year]?.gdp || 0);
            
            // 使用当前年份的数据
            const currentYearData1 = data1[currentYear];
            const currentYearData2 = data2[currentYear];
            
            // 清除旧图表实例
            const gdpComparisonChartElement = document.getElementById('gdp-comparison-chart');
            const populationComparisonChartElement = document.getElementById('population-comparison-chart');
            const tradeComparisonChartElement = document.getElementById('trade-comparison-chart');
            
            // 销毁旧实例
            const oldGdpChart = echarts.getInstanceByDom(gdpComparisonChartElement);
            const oldPopulationChart = echarts.getInstanceByDom(populationComparisonChartElement);
            const oldTradeChart = echarts.getInstanceByDom(tradeComparisonChartElement);
            
            if (oldGdpChart) oldGdpChart.dispose();
            if (oldPopulationChart) oldPopulationChart.dispose();
            if (oldTradeChart) oldTradeChart.dispose();
            
            // GDP对比图表
            const gdpComparisonChart = echarts.init(gdpComparisonChartElement);
            gdpComparisonChart.setOption({
                title: {
                    text: 'GDP增长趋势对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const year = params[0].axisValue;
                        let tooltipContent = `${year}年<br/>`;
                        
                        params.forEach(param => {
                            if (param.seriesName === `${name1} 增长率`) {
                                tooltipContent += `${param.seriesName}: ${param.value}%<br/>`;
                            } else if (param.seriesName === `${name2} 增长率`) {
                                tooltipContent += `${param.seriesName}: ${param.value}%<br/>`;
                            } else {
                                tooltipContent += `${param.seriesName}: ${param.value} 十亿美元<br/>`;
                            }
                        });
                        
                        return tooltipContent;
                    }
                },
                legend: {
                    data: [`${name1} 增长率`, `${name2} 增长率`, `${name1} GDP`, `${name2} GDP`],
                    bottom: 0
                },
                grid: {
                    top: 60,
                    bottom: 60
                },
                xAxis: {
                    type: 'category',
                    data: years
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '增长率(%)',
                        position: 'left',
                        axisLabel: {
                            formatter: '{value}%'
                        }
                    },
                    {
                        type: 'value',
                        name: 'GDP(十亿美元)',
                        position: 'right',
                        axisLabel: {
                            formatter: '{value}'
                        }
                    }
                ],
                series: [
                    {
                        name: `${name1} 增长率`,
                        data: gdpGrowth1,
                        type: 'line',
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#1976d2'
                        }
                    },
                    {
                        name: `${name2} 增长率`,
                        data: gdpGrowth2,
                        type: 'line',
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#ff9800'
                        }
                    },
                    {
                        name: `${name1} GDP`,
                        data: gdp1,
                        type: 'bar',
                        yAxisIndex: 1,
                        itemStyle: {
                            color: 'rgba(25, 118, 210, 0.5)'
                        }
                    },
                    {
                        name: `${name2} GDP`,
                        data: gdp2,
                        type: 'bar',
                        yAxisIndex: 1,
                        itemStyle: {
                            color: 'rgba(255, 152, 0, 0.5)'
                        }
                    }
                ]
            });
            
            // 人口对比图表
            const populationComparisonChart = echarts.init(populationComparisonChartElement);
            const population1 = years.map(year => data1[year]?.population || 0);
            const population2 = years.map(year => data2[year]?.population || 0);
            
            populationComparisonChart.setOption({
                title: {
                    text: '人口趋势对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        const year = params[0].axisValue;
                        let tooltipContent = `${year}年<br/>`;
                        
                        params.forEach(param => {
                            tooltipContent += `${param.seriesName}: ${param.value} 百万<br/>`;
                        });
                        
                        return tooltipContent;
                    }
                },
                legend: {
                    data: [name1, name2],
                    bottom: 0
                },
                grid: {
                    top: 60,
                    bottom: 60
                },
                xAxis: {
                    type: 'category',
                    data: years
                },
                yAxis: {
                    type: 'value',
                    name: '人口(百万)'
                },
                series: [
                    {
                        name: name1,
                        data: population1,
                        type: 'line',
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#1976d2'
                        },
                        symbol: 'circle',
                        symbolSize: 8
                    },
                    {
                        name: name2,
                        data: population2,
                        type: 'line',
                        smooth: true,
                        lineStyle: {
                            width: 3,
                            color: '#ff9800'
                        },
                        symbol: 'circle',
                        symbolSize: 8
                    }
                ]
            });
            
            // 贸易数据对比图表
            const tradeComparisonChart = echarts.init(tradeComparisonChartElement);
            
            // 获取当前年份的数据进行对比
            const trade1 = years.map(year => data1[year]?.trade || 0);
            const trade2 = years.map(year => data2[year]?.trade || 0);
            const socialIndex1 = years.map(year => data1[year]?.socialIndex || 0);
            const socialIndex2 = years.map(year => data2[year]?.socialIndex || 0);
            
            tradeComparisonChart.setOption({
                title: {
                    text: '综合指标对比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    data: [name1, name2],
                    bottom: 0
                },
                radar: {
                    indicator: [
                        { name: 'GDP(十亿美元)', max: Math.max(currentYearData1.gdp, currentYearData2.gdp) * 1.2 },
                        { name: '贸易额(十亿美元)', max: Math.max(currentYearData1.trade, currentYearData2.trade) * 1.2 },
                        { name: '社会发展指数', max: 100 },
                        { name: 'GDP增长率(%)', max: Math.max(Math.abs(currentYearData1.gdpGrowth), Math.abs(currentYearData2.gdpGrowth)) * 1.5 },
                        { name: '人口(百万)', max: Math.max(currentYearData1.population, currentYearData2.population) * 1.2 }
                    ]
                },
                series: [{
                    type: 'radar',
                    data: [
                        {
                            value: [
                                currentYearData1.gdp,
                                currentYearData1.trade,
                                currentYearData1.socialIndex,
                                currentYearData1.gdpGrowth,
                                currentYearData1.population
                            ],
                            name: name1,
                            areaStyle: {
                                color: 'rgba(25, 118, 210, 0.4)'
                            },
                            lineStyle: {
                                color: '#1976d2'
                            }
                        },
                        {
                            value: [
                                currentYearData2.gdp,
                                currentYearData2.trade,
                                currentYearData2.socialIndex,
                                currentYearData2.gdpGrowth,
                                currentYearData2.population
                            ],
                            name: name2,
                            areaStyle: {
                                color: 'rgba(255, 152, 0, 0.4)'
                            },
                            lineStyle: {
                                color: '#ff9800'
                            }
                        }
                    ]
                }]
            });
            
            // 注册图表以实现响应式
            if (typeof registerChart === 'function') {
                registerChart(gdpComparisonChart);
                registerChart(populationComparisonChart);
                registerChart(tradeComparisonChart);
            }
        }
        
        // 导出图片按钮事件
        document.getElementById('export-img-btn').addEventListener('click', function() {
            if (currentCountry) {
                const imgData = aseanMapChart.getDataURL({
                    pixelRatio: 2,
                    backgroundColor: '#fff'
                });
                
                const link = document.createElement('a');
                link.download = `东盟地图-${countryNameMap[currentCountry]}.png`;
                link.href = imgData;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } else {
                alert('请先选择一个国家');
            }
        });
        
        // 导出CSV按钮事件
        document.getElementById('export-csv-btn').addEventListener('click', function() {
            if (!currentCountry) {
                alert('请先选择一个国家');
                return;
            }
            
            const data = countryData[currentCountry];
            const chineseName = countryNameMap[currentCountry];
            
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "指标,数值\n";
            csvContent += `国家,${chineseName}\n`;
            csvContent += `人口(百万),${data.population}\n`;
            csvContent += `GDP(十亿美元),${data.gdp}\n`;
            csvContent += `贸易额(十亿美元),${data.trade}\n`;
            csvContent += `社会发展指数,${data.socialIndex}\n`;
            csvContent += `\n`;
            csvContent += `年份,GDP增长率(%)\n`;
            data.gdpGrowth.forEach((growth, index) => {
                csvContent += `${2019 + index},${growth}\n`;
            });
            
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `${chineseName}-数据.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
        
        // 年份筛选器事件
        document.getElementById('year-filter').addEventListener('change', function() {
            // 更新当前年份
            currentYear = this.value;
            
            // 更新地图标题
            option.title.text = `东盟十国地图 (${currentYear}年)`;
            
            // 更新地图数据
            const newMapData = [];
            for (const country of aseanCountries) {
                if (countryData[country] && countryData[country][currentYear]) {
                    // 获取当前数据类型的主要指标
                    const mainIndicator = countryData[country][currentDataType]?.mainIndicator || 'socialIndex';
                    const value = countryData[country][currentYear][mainIndicator];
                    newMapData.push({
                        name: country,
                        value: value
                    });
                }
            }
            
            // 创建一个新的选项对象，避免修改原始选项
            const newOption = {
                title: option.title,
                series: [{
                    data: newMapData
                }]
            };
            
            // 重新设置地图配置
            aseanMapChart.setOption(newOption);
            
            // 更新原始选项中的数据
            option.series[0].data = newMapData;
            
            // 如果有选中的国家，更新图表
            if (currentCountry) {
                // 更新国家名称显示
                document.getElementById('country-name').innerText = `${countryNameMap[currentCountry]} (${currentYear}年)`;
                
                // 更新国家基本信息
                const countryInfo = countryData[currentCountry][currentYear];
                
                // 根据当前数据类型显示不同的指标
                let basicInfoHTML = '';
                
                // 始终显示基本信息
                basicInfoHTML += `<p>人口：${countryInfo.population} 百万</p>`;
                
                // 根据数据类型显示额外信息
                if (currentDataType === 'economic' || currentDataType === 'all') {
                    basicInfoHTML += `
                        <p>GDP：${countryInfo.gdp} 十亿美元</p>
                        <p>GDP增长率：${countryInfo.gdpGrowth}%</p>
                        <p>贸易额：${countryInfo.trade} 十亿美元</p>
                    `;
                }
                
                if (currentDataType === 'social' || currentDataType === 'all') {
                    basicInfoHTML += `<p>社会发展指数：${countryInfo.socialIndex}</p>`;
                }
                
                document.getElementById('country-basic-info').innerHTML = basicInfoHTML;
                
                // 更新图表
                createCharts(currentCountry);
            }
        });
        
        // 数据类型筛选器事件
        document.getElementById('data-type').addEventListener('change', function() {
            // 更新当前数据类型
            currentDataType = this.value;
            
            // 更新visualMap配置
            let visualMapConfig = {
                show: true,
                left: 20,
                bottom: 40,
                calculable: true,
                text: ['高', '低'],
                inRange: {
                    color: ['lightskyblue', 'yellow', 'orangered']
                }
            };
            
            // 根据数据类型设置不同的范围
            if (currentDataType === 'economic') {
                visualMapConfig.min = 0;
                visualMapConfig.max = 1300; // 最大GDP值
                visualMapConfig.text = ['GDP高', 'GDP低'];
            } else if (currentDataType === 'population') {
                visualMapConfig.min = 0;
                visualMapConfig.max = 300; // 最大人口值
                visualMapConfig.text = ['人口多', '人口少'];
            } else if (currentDataType === 'social') {
                visualMapConfig.min = 50;
                visualMapConfig.max = 100;
                visualMapConfig.text = ['发展高', '发展低'];
            }
            
            // 更新地图数据
            const newMapData = [];
            for (const country of aseanCountries) {
                if (countryData[country] && countryData[country][currentYear]) {
                    // 获取当前数据类型的主要指标
                    const mainIndicator = countryData[country][currentDataType]?.mainIndicator || 'socialIndex';
                    const value = countryData[country][currentYear][mainIndicator];
                    newMapData.push({
                        name: country,
                        value: value
                    });
                }
            }
            
            // 创建一个新的选项对象，避免修改原始选项
            const newOption = {
                visualMap: visualMapConfig,
                series: [{
                    data: newMapData
                }]
            };
            
            // 重新设置地图配置
            aseanMapChart.setOption(newOption);
            
            // 更新原始选项中的数据
            option.visualMap = visualMapConfig;
            option.series[0].data = newMapData;
            
            // 如果有选中的国家，更新图表
            if (currentCountry) {
                // 更新国家基本信息
                const countryInfo = countryData[currentCountry][currentYear];
                
                // 根据当前数据类型显示不同的指标
                let basicInfoHTML = '';
                
                // 始终显示基本信息
                basicInfoHTML += `<p>人口：${countryInfo.population} 百万</p>`;
                
                // 根据数据类型显示额外信息
                if (currentDataType === 'economic' || currentDataType === 'all') {
                    basicInfoHTML += `
                        <p>GDP：${countryInfo.gdp} 十亿美元</p>
                        <p>GDP增长率：${countryInfo.gdpGrowth}%</p>
                        <p>贸易额：${countryInfo.trade} 十亿美元</p>
                    `;
                }
                
                if (currentDataType === 'social' || currentDataType === 'all') {
                    basicInfoHTML += `<p>社会发展指数：${countryInfo.socialIndex}</p>`;
                }
                
                document.getElementById('country-basic-info').innerHTML = basicInfoHTML;
                
                // 更新图表
                createCharts(currentCountry);
            }
        });
    }
}); 