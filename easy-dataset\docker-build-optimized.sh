#!/bin/bash

# Docker构建优化脚本
# 使用BuildKit和多种缓存策略来加速构建

set -e

# 配置变量
IMAGE_NAME=${1:-"easy-dataset"}
TAG=${2:-"latest"}
REGISTRY=${3:-""}
PLATFORM=${4:-"linux/amd64"}

echo "🚀 开始优化构建 Docker 镜像..."
echo "镜像名称: $IMAGE_NAME"
echo "标签: $TAG"
echo "平台: $PLATFORM"

# 启用 BuildKit
export DOCKER_BUILDKIT=1

# 构建参数
BUILD_ARGS=(
    "--platform=$PLATFORM"
    "--target=runner"
    "--build-arg=BUILDKIT_INLINE_CACHE=1"
    "--progress=plain"
)

# 如果指定了注册表，添加缓存配置
if [ -n "$REGISTRY" ]; then
    FULL_IMAGE_NAME="$REGISTRY/$IMAGE_NAME:$TAG"
    BUILD_ARGS+=(
        "--cache-from=$REGISTRY/$IMAGE_NAME:cache"
        "--cache-to=$REGISTRY/$IMAGE_NAME:cache,mode=max"
        "--tag=$FULL_IMAGE_NAME"
    )
else
    FULL_IMAGE_NAME="$IMAGE_NAME:$TAG"
    BUILD_ARGS+=("--tag=$FULL_IMAGE_NAME")
fi

echo "🔧 构建参数: ${BUILD_ARGS[*]}"

# 执行构建
echo "⏳ 开始构建镜像..."
start_time=$(date +%s)

docker build "${BUILD_ARGS[@]}" -f Dockerfile.prod .

end_time=$(date +%s)
build_duration=$((end_time - start_time))

echo "✅ 构建完成！"
echo "⏱️  构建耗时: ${build_duration}秒"

# 显示镜像信息
echo "📊 镜像信息:"
docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 如果指定了注册表，推送镜像
if [ -n "$REGISTRY" ]; then
    echo "📤 推送镜像到注册表..."
    push_start_time=$(date +%s)
    
    docker push "$FULL_IMAGE_NAME"
    
    push_end_time=$(date +%s)
    push_duration=$((push_end_time - push_start_time))
    
    echo "✅ 推送完成！"
    echo "⏱️  推送耗时: ${push_duration}秒"
    echo "🎯 总耗时: $((build_duration + push_duration))秒"
else
    echo "🎯 总耗时: ${build_duration}秒"
fi

echo "🎉 所有操作完成！"
