import React, { PureComponent } from 'react';
import Chart from '../../../utils/chart';
import { mapOptions } from './options';

class Map extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      renderer: 'canvas',
      mapOption: null,
      loading: true,
    };
  }

  async componentDidMount() {
    await this.loadMapOptions();
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.mapData !== this.props.mapData || prevProps.selectedCountry !== this.props.selectedCountry) {
      await this.loadMapOptions();
    }
  }

  async loadMapOptions() {
    const { mapData, selectedCountry, globalStatistics } = this.props;
    if (!mapData) {
      this.setState({ mapOption: null, loading: false });
      return;
    }

    try {
      this.setState({ loading: true });
      const option = await mapOptions(mapData, selectedCountry, globalStatistics);
      this.setState({ mapOption: option, loading: false });
    } catch (error) {
      console.error('加载地图配置失败:', error);
      this.setState({ mapOption: null, loading: false });
    }
  }

  handleChartClick = (params) => {
    if (this.props.onCountryClick && params.name) {
      this.props.onCountryClick(params.name);
    }
  };

  render() {
    const { renderer, mapOption, loading } = this.state;
    const { mapData } = this.props;

    // 统一的容器样式
    const containerStyle = {
      width: '10.625rem',
      height: '8.125rem',
      position: 'relative',
      backgroundColor: 'transparent', // 移除背景色蒙层
      // borderRadius: '8px', // 移除圆角
      // border: '1px solid rgba(52, 63, 75, 0.5)', // 移除边框
    };

    const loadingStyle = {
      ...containerStyle,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#fff',
      fontSize: '0.2rem', // 使用rem单位保持一致性
      textAlign: 'center',
    };

    if (!mapData) {
      return (
        <div style={loadingStyle}>
          <div>
            <div style={{ marginBottom: '0.1rem' }}>📍</div>
            <div>暂无地图数据</div>
          </div>
        </div>
      );
    }

    if (loading) {
      return (
        <div style={loadingStyle}>
          <div>
            <div style={{ marginBottom: '0.1rem' }}>🌍</div>
            <div>正在加载地图数据...</div>
          </div>
        </div>
      );
    }

    if (!mapOption) {
      return (
        <div style={loadingStyle}>
          <div>
            <div style={{ marginBottom: '0.1rem' }}>⚠️</div>
            <div>地图数据加载失败</div>
          </div>
        </div>
      );
    }

    return (
      <div style={containerStyle}>
        <Chart
          renderer={renderer}
          option={mapOption}
          onChartClick={this.handleChartClick}
        />
      </div>
    );
  }
}

export default Map;
