/**
 * 领域树处理模块
 * 用于处理领域树的生成、修订和管理
 */
const LLMClient = require('../llm/core/index');
const getLabelPrompt = require('../llm/prompts/label');
const getLabelEnPrompt = require('../llm/prompts/labelEn');
const getLabelRevisePrompt = require('../llm/prompts/labelRevise');
const getLabelReviseEnPrompt = require('../llm/prompts/labelReviseEn');
const { getProjectTocs } = require('../file/text-splitter');
const { getTags, batchSaveTags, createTag } = require('../db/tags');
const { extractJsonFromLLMOutput } = require('../llm/common/util');
const { filterDomainTree } = require('./file');

/**
 * 处理领域树生成或更新
 * @param {Object} options - 配置选项
 * @param {string} options.projectId - 项目ID
 * @param {string} options.action - 操作类型: 'rebuild', 'revise', 'keep'
 * @param {string} options.toc - 所有文档的目录结构
 * @param {Object} options.model - 使用的模型信息
 * @param {string} options.language - 语言: 'en' 或 '中文'
 * @param {string} options.fileName - 文件名（用于新增文件时获取内容）
 * @param {string} options.deletedContent - 被删除的文件内容（用于删除文件时）
 * @param {Object} options.project - 项目信息，包含 globalPrompt 和 domainTreePrompt
 * @returns {Promise<Array>} 生成的领域树标签
 */
async function handleDomainTree({
  projectId,
  action = 'rebuild',
  allToc,
  newToc,
  model,
  language = '中文',
  deleteToc = null,
  project
}) {
  // 如果是保持不变，直接返回现有标签
  if (action === 'keep') {
    console.log(`[${projectId}] Using existing domain tree`);
    return await getTags(projectId);
  }

  const isEnglish = language === 'en';
  const { globalPrompt, domainTreePrompt } = project || {};

  try {
    if (!allToc) {
      allToc = await getProjectTocs(projectId);
    }

    const llmClient = new LLMClient(model);
    let tags, prompt, response;
    // 重建领域树
    if (action === 'rebuild') {
      console.log(`[${projectId}] Rebuilding domain tree`);
      const promptFunc = isEnglish ? getLabelEnPrompt : getLabelPrompt;
      prompt = promptFunc({ text: allToc, globalPrompt, domainTreePrompt });
      console.log('rebuild', prompt);
      response = await llmClient.getResponse(prompt);
      tags = extractJsonFromLLMOutput(response);
      console.log('rebuild tags', tags);
    }
    // 修订领域树
    else if (action === 'revise') {
      console.log(`[${projectId}] Revising domain tree`);
      // 获取现有的领域树
      const existingTags = await getTags(projectId);

      if (!existingTags || existingTags.length === 0) {
        // 如果没有现有领域树，就像重建一样处理
        const promptFunc = isEnglish ? getLabelEnPrompt : getLabelPrompt;
        prompt = promptFunc({ text: allToc, globalPrompt, domainTreePrompt });
      } else {
        // 增量更新领域树的逻辑
        const promptFunc = isEnglish ? getLabelReviseEnPrompt : getLabelRevisePrompt;

        prompt = promptFunc({
          text: allToc,
          existingTags: filterDomainTree(existingTags),
          newContent: newToc,
          deletedContent: deleteToc,
          globalPrompt,
          domainTreePrompt
        });
      }

      // console.log('revise', prompt);

      response = await llmClient.getResponse(prompt);
      tags = extractJsonFromLLMOutput(response);

      // console.log('revise tags', tags);
    }

    // 保存领域树标签（如果生成成功）
    if (tags && tags.length > 0 && action !== 'keep') {
      if (action === 'rebuild') {
        // 重建模式：删除所有现有标签后重新创建
        await batchSaveTags(projectId, tags);
      } else if (action === 'revise') {
        // 修订模式：保留现有标签，只更新变化的部分
        await updateTagsSelectively(projectId, tags);
      }
    } else if (!tags && action !== 'keep') {
      console.error(`[${projectId}] Failed to generate domain tree tags`);
    }

    return tags;
  } catch (error) {
    console.error(`[${projectId}] Error handling domain tree: ${error.message}`);
    throw error;
  }
}

/**
 * 选择性更新标签树（修订模式专用）
 * 保留现有标签，只添加新的标签，不删除现有的
 * @param {string} projectId - 项目ID
 * @param {Array} newTags - 新生成的标签树
 */
async function updateTagsSelectively(projectId, newTags) {
  try {
    // 获取现有标签
    const existingTags = await getTags(projectId);
    
    // 创建现有标签的映射，用于快速查找
    const existingTagsMap = new Map();
    
    function buildTagMap(tags, parentPath = '') {
      for (const tag of tags) {
        const tagPath = parentPath ? `${parentPath}/${tag.label}` : tag.label;
        existingTagsMap.set(tagPath, tag);
        if (tag.child && tag.child.length > 0) {
          buildTagMap(tag.child, tagPath);
        }
      }
    }
    
    buildTagMap(existingTags);
    
    // 递归添加新标签（只添加不存在的）
    async function addNewTags(tags, parentId = null, parentPath = '') {
      for (const tag of tags) {
        const tagPath = parentPath ? `${parentPath}/${tag.label}` : tag.label;
        
        // 如果标签不存在，则创建
        if (!existingTagsMap.has(tagPath)) {
          console.log(`[${projectId}] Adding new tag: ${tagPath}`);
          const createdTag = await createTag(projectId, tag.label, parentId);
          
          // 如果有子标签，递归添加
          if (tag.child && tag.child.length > 0) {
            await addNewTags(tag.child, createdTag.id, tagPath);
          }
        } else {
          // 标签已存在，检查其子标签
          const existingTag = existingTagsMap.get(tagPath);
          if (tag.child && tag.child.length > 0) {
            await addNewTags(tag.child, existingTag.id, tagPath);
          }
        }
      }
    }
    
    await addNewTags(newTags);
    console.log(`[${projectId}] Selective tag update completed`);
  } catch (error) {
    console.error(`[${projectId}] Error in selective tag update: ${error.message}`);
    throw error;
  }
}

module.exports = {
  handleDomainTree
};
