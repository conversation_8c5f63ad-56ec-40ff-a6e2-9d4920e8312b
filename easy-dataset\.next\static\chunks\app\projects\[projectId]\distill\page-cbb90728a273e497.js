(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[508],{48337:function(e,t,a){Promise.resolve().then(a.bind(a,72909))},72909:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ec}});var s=a(57437),l=a(2265),n=a(43949),i=a(99376),o=a(92964),r=a(66257),c=a(19978),d=a(15735),u=a(96207),g=a(1899),h=a(5002),x=a(89051),p=a(59832),m=a(94013),j=a(35389),f=a(41554),Z=a(97288),b=a(15273),y=a(83464),v=a(14314),C=a(73261),T=a(11741),S=a(53431),w=a(67051),q=a(67571),k=a(17162),P=a(5050),I=a(53980),B=a(23208),L=a(61140),z=a(30396),E=a(33114),W=a(99115),D=a(89938);function Q(e){let{question:t,level:a,onDelete:l,onEdit:i,onGenerateDataset:o,processing:r=!1}=e,{t:c}=(0,n.$G)();return(0,s.jsxs)(C.ZP,{sx:{pl:(a+1)*2,py:.75,borderLeft:"1px dashed rgba(0, 0, 0, 0.1)",ml:2,borderBottom:"1px solid",borderColor:"divider","&:hover":{bgcolor:"action.hover"}},secondaryAction:(0,s.jsxs)(g.Z,{sx:{display:"flex",alignItems:"center",gap:.5},children:[(0,s.jsx)(x.Z,{title:c("datasets.generateDataset"),children:(0,s.jsx)(p.Z,{size:"small",color:"primary",onClick:e=>o(e),disabled:r,children:r?(0,s.jsx)(j.Z,{size:16}):(0,s.jsx)(Z.Z,{fontSize:"small"})})}),(0,s.jsx)(x.Z,{title:c("domain.editQuestion"),children:(0,s.jsx)(p.Z,{size:"small",color:"secondary",onClick:e=>i(e),disabled:r,children:(0,s.jsx)(D.Z,{fontSize:"small"})})}),(0,s.jsx)(x.Z,{title:c("common.delete"),children:(0,s.jsx)(p.Z,{size:"small",color:"error",onClick:e=>l(e),disabled:r,children:(0,s.jsx)(W.Z,{fontSize:"small"})})})]}),children:[(0,s.jsx)(S.Z,{sx:{minWidth:32,color:"secondary.main"},children:(0,s.jsx)(E.Z,{fontSize:"small"})}),(0,s.jsx)(w.Z,{primary:(0,s.jsxs)(g.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,s.jsx)(h.Z,{variant:"body2",sx:{whiteSpace:"normal",wordBreak:"break-word",paddingRight:"28px"},children:t.question}),t.answered&&(0,s.jsx)(q.Z,{size:"small",label:c("datasets.answered"),color:"success",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}})]})})]})}function F(e){let{tag:t,level:a=0,expanded:l=!1,onToggle:i,onMenuOpen:o,onGenerateQuestions:r,onGenerateSubTags:c,questions:d=[],loadingQuestions:u=!1,processingQuestions:m={},onDeleteQuestion:Z,onEditQuestion:y,onGenerateDataset:v,allQuestions:E=[],tagQuestions:W={},children:D}=e,{t:F}=(0,n.$G)(),N=e=>{let t=e.length;return e.forEach(e=>{e.children&&e.children.length>0&&(t+=N(e.children))}),t},M=e=>{let t=0;return e.forEach(e=>{W[e.id]&&W[e.id].length>0?t+=W[e.id].length:t+=E.filter(t=>t.label===e.label).length,e.children&&e.children.length>0&&(t+=M(e.children))}),t},R=(W[t.id]&&W[t.id].length>0?W[t.id].length:E.filter(e=>e.label===t.label).length)+(t.children?M(t.children||[]):0);return(0,s.jsxs)(g.Z,{sx:{my:.5},children:[(0,s.jsx)(C.ZP,{disablePadding:!0,sx:{pl:2*a,borderLeft:a>0?"1px dashed rgba(0, 0, 0, 0.1)":"none",ml:a>0?2:0},children:(0,s.jsxs)(T.Z,{onClick:()=>i(t.id),sx:{borderRadius:1,py:.5},children:[(0,s.jsx)(S.Z,{sx:{minWidth:36},children:(0,s.jsx)(P.Z,{color:"primary",fontSize:"small"})}),(0,s.jsx)(w.Z,{primary:(0,s.jsxs)(g.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,s.jsx)(h.Z,{sx:{fontWeight:"medium"},children:t.displayLabel||t.label}),t.children&&t.children.length>0&&(0,s.jsx)(q.Z,{size:"small",label:"".concat(N(t.children)," ").concat(F("distill.subTags")),color:"primary",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}}),R>0&&(0,s.jsx)(q.Z,{size:"small",label:"".concat(R," ").concat(F("distill.questions")),color:"secondary",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}})]}),primaryTypographyProps:{component:"div"}}),(0,s.jsxs)(g.Z,{sx:{display:"flex",alignItems:"center",gap:.5},children:[(0,s.jsx)(x.Z,{title:F("distill.generateQuestions"),children:(0,s.jsx)(p.Z,{size:"small",onClick:e=>{e.stopPropagation(),r(t)},children:(0,s.jsx)(L.Z,{fontSize:"small"})})}),(0,s.jsx)(x.Z,{title:F("distill.addChildTag"),children:(0,s.jsx)(p.Z,{size:"small",onClick:e=>{e.stopPropagation(),c(t)},children:(0,s.jsx)(f.Z,{fontSize:"small"})})}),(0,s.jsx)(p.Z,{size:"small",onClick:e=>o(e,t),children:(0,s.jsx)(z.Z,{fontSize:"small"})}),t.children&&t.children.length>0?l?(0,s.jsx)(B.Z,{fontSize:"small"}):(0,s.jsx)(I.Z,{fontSize:"small"}):null]})]})}),t.children&&t.children.length>0&&(0,s.jsx)(k.Z,{in:l,timeout:"auto",unmountOnExit:!0,children:D}),l&&(0,s.jsx)(k.Z,{in:l,timeout:"auto",unmountOnExit:!0,children:(0,s.jsx)(b.Z,{disablePadding:!0,sx:{mt:.5,mb:1},children:u?(0,s.jsxs)(C.ZP,{sx:{pl:(a+1)*2,py:.75},children:[(0,s.jsx)(j.Z,{size:20}),(0,s.jsx)(h.Z,{variant:"body2",sx:{ml:2},children:F("common.loading")})]}):d&&d.length>0?d.map(e=>(0,s.jsx)(Q,{question:e,level:a,processing:m[e.id],onDelete:t=>Z(e.id,t),onEdit:t=>y(e,t),onGenerateDataset:t=>v(e.id,e.question,t)},e.id)):(0,s.jsx)(C.ZP,{sx:{pl:(a+1)*2,py:1},children:(0,s.jsx)(h.Z,{variant:"body2",color:"text.secondary",children:F("distill.noQuestions")})})})})]},t.id)}var N=a(98534),M=a(42187);function R(e){let{anchorEl:t,open:a,onClose:l,onEdit:i,onDelete:o}=e,{t:r}=(0,n.$G)();return(0,s.jsxs)(N.Z,{anchorEl:t,open:a,onClose:l,children:[(0,s.jsxs)(M.Z,{onClick:i,children:[(0,s.jsx)(S.Z,{children:(0,s.jsx)(D.Z,{fontSize:"small"})}),(0,s.jsx)(w.Z,{children:r("textSplit.editTag")})]}),(0,s.jsxs)(M.Z,{onClick:o,children:[(0,s.jsx)(S.Z,{children:(0,s.jsx)(W.Z,{fontSize:"small"})}),(0,s.jsx)(w.Z,{children:r("common.delete")})]})]})}var G=a(53392),A=a(79507),O=a(9026);function $(e){let{open:t,onClose:a,onConfirm:l,title:n,cancelText:i="取消",confirmText:o="确认",confirmColor:r="error"}=e;return(0,s.jsxs)(G.Z,{open:t,onClose:a,"aria-labelledby":"confirm-dialog-title",children:[(0,s.jsx)(A.Z,{id:"confirm-dialog-title",children:n}),(0,s.jsxs)(O.Z,{children:[(0,s.jsx)(m.Z,{onClick:a,color:"primary",children:i}),(0,s.jsx)(m.Z,{onClick:l,color:r,autoFocus:!0,children:o})]})]})}var H=a(77468),_=a(12713);function U(e){let{open:t,onClose:a,tag:i,projectId:o,onSuccess:r}=e,{t:c}=(0,n.$G)(),[d,u]=(0,l.useState)(""),[x,p]=(0,l.useState)(!1),[j,f]=(0,l.useState)("");(0,l.useEffect)(()=>{i&&u(i.label||"")},[i]);let Z=()=>{u(""),f(""),p(!1)},b=()=>{Z(),a()},v=async()=>{if(!d.trim()){f(c("distill.tagNameRequired"));return}if(d.trim()===(null==i?void 0:i.label)){b();return}try{p(!0),f("");let e=await y.Z.put("/api/projects/".concat(o,"/tags"),{tags:{id:i.id,label:d.trim(),parentId:i.parentId}});e.data&&(r&&r(e.data.tags),b())}catch(a){var e,t;console.error("编辑标签失败:",a),f((null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||c("distill.editTagError"))}finally{p(!1)}};return(0,s.jsxs)(G.Z,{open:t,onClose:b,maxWidth:"sm",fullWidth:!0,children:[(0,s.jsx)(A.Z,{children:c("textSplit.editTag")}),(0,s.jsx)(H.Z,{children:(0,s.jsxs)(g.Z,{sx:{pt:1},children:[(0,s.jsx)(_.Z,{autoFocus:!0,fullWidth:!0,label:c("domain.tagName"),value:d,onChange:e=>u(e.target.value),onKeyPress:e=>{"Enter"===e.key&&v()},error:!!j,helperText:j,disabled:x,placeholder:c("domain.inputEditTagName")}),i&&(0,s.jsxs)(h.Z,{variant:"body2",color:"text.secondary",sx:{mt:2},children:[c("domain.originalTagName"),": ",i.label]})]})}),(0,s.jsxs)(O.Z,{children:[(0,s.jsx)(m.Z,{onClick:b,disabled:x,children:c("common.cancel")}),(0,s.jsx)(m.Z,{onClick:v,variant:"contained",disabled:x||!d.trim(),children:x?c("common.saving"):c("common.save")})]})]})}var V=a(42147);function K(e){let{open:t,onClose:a,question:i,projectId:o,tags:r=[],onSuccess:c}=e,{t:d}=(0,n.$G)(),[u,x]=(0,l.useState)(""),[p,j]=(0,l.useState)([]),[f,Z]=(0,l.useState)(!1),[b,v]=(0,l.useState)("");(0,l.useEffect)(()=>{i&&(x(i.question||""),i.tags&&Array.isArray(i.tags)?j(i.tags):i.tag?j([i.tag]):j([]))},[i]);let C=()=>{x(""),j([]),v(""),Z(!1)},T=()=>{C(),a()},S=async()=>{if(!u.trim()){v(d("distill.questionRequired"));return}if(0===p.length){v(d("distill.tagRequired"));return}let e=u.trim()!==(null==i?void 0:i.question),t=JSON.stringify(p.map(e=>e.id).sort())!==JSON.stringify(((null==i?void 0:i.tags)||[null==i?void 0:i.tag].filter(Boolean)).map(e=>e.id).sort());if(!e&&!t){T();return}try{Z(!0),v("");let e={question:u.trim(),tagIds:p.map(e=>e.id)},t=await y.Z.put("/api/projects/".concat(o,"/questions/").concat(i.id),e);t.data&&(c&&c(t.data),T())}catch(e){var a,s;console.error("编辑问题失败:",e),v((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.error)||d("distill.editQuestionError"))}finally{Z(!1)}};return(0,s.jsxs)(G.Z,{open:t,onClose:T,maxWidth:"md",fullWidth:!0,children:[(0,s.jsx)(A.Z,{children:d("domain.editQuestion")}),(0,s.jsx)(H.Z,{children:(0,s.jsxs)(g.Z,{sx:{pt:1},children:[(0,s.jsx)(_.Z,{autoFocus:!0,fullWidth:!0,label:d("domain.questionContent"),value:u,onChange:e=>x(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),S())},multiline:!0,rows:4,error:!!b&&b.includes(d("domain.questionRequired")),disabled:f,placeholder:d("domain.inputEditQuestionContent"),sx:{mb:2}}),(0,s.jsx)(V.Z,{multiple:!0,options:r.filter(e=>e.id&&e.label),getOptionLabel:e=>e.label||"",value:p,onChange:(e,t)=>{j(t)},isOptionEqualToValue:(e,t)=>e.id===t.id,renderTags:(e,t)=>e.map((e,a)=>(0,l.createElement)(q.Z,{variant:"outlined",label:e.label,...t({index:a}),key:e.id})),renderInput:e=>(0,s.jsx)(_.Z,{...e,label:d("domain.selectTags"),placeholder:d("domain.selectTagsPlaceholder"),error:!!b&&b.includes(d("domain.tagRequired")),disabled:f}),disabled:f}),b&&(0,s.jsx)(h.Z,{variant:"body2",color:"error",sx:{mt:1},children:b}),i&&(0,s.jsxs)(g.Z,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1},children:[(0,s.jsxs)(h.Z,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:[d("domain.originalQuestion"),":"]}),(0,s.jsx)(h.Z,{variant:"body2",sx:{mb:1},children:i.question}),(0,s.jsxs)(h.Z,{variant:"body2",color:"text.secondary",children:[d("domain.originalTags"),": ",(i.tags||[i.tag].filter(Boolean)).map(e=>null==e?void 0:e.label).join(", ")]})]})]})}),(0,s.jsxs)(O.Z,{children:[(0,s.jsx)(m.Z,{onClick:T,disabled:f,children:d("common.cancel")}),(0,s.jsx)(m.Z,{onClick:S,variant:"contained",disabled:f||!u.trim()||0===p.length,children:f?d("common.saving"):d("common.save")})]})]})}let J=e=>e.length>0&&e[0].displayLabel?e:[...e].sort((e,t)=>{let a=e=>{let t=e.match(/^([\d.]+)\s/);return t?t[1]:null},s=a(e.label),l=a(t.label);if(s&&l){let e=s.split(".").map(e=>parseInt(e,10)),t=l.split(".").map(e=>parseInt(e,10));for(let a=0;a<Math.min(e.length,t.length);a++)if(e[a]!==t[a])return e[a]-t[a];return e.length-t.length}return s?-1:l?1:e.label.localeCompare(t.label,"zh-CN")}),X=(0,l.forwardRef)(function(e,t){let{projectId:a,tags:i=[],onGenerateSubTags:o,onGenerateQuestions:r}=e,{t:c}=(0,n.$G)(),[d,u]=(0,l.useState)({}),[x,p]=(0,l.useState)({}),[m,j]=(0,l.useState)({}),[f,Z]=(0,l.useState)({}),[C,T]=(0,l.useState)(null),[S,w]=(0,l.useState)(null),[q,k]=(0,l.useState)([]),[P,I]=(0,l.useState)(!1),[B,L]=(0,l.useState)({}),[z,E]=(0,l.useState)(!1),[W,D]=(0,l.useState)(null),[Q,N]=(0,l.useState)(!1),[M,G]=(0,l.useState)(null),[A,O]=(0,l.useState)(!1),[H,_]=(0,l.useState)(null),[V,X]=(0,l.useState)(!1),[Y,ee]=(0,l.useState)(null),[et,ea]=(0,l.useState)(null),[es,el]=(0,l.useState)(""),{generateSingleDataset:en}=(0,v.l)(),ei=(0,l.useCallback)(async()=>{try{I(!0);let e=await y.Z.get("/api/projects/".concat(a,"/questions/tree?isDistill=true"));k(e.data),console.log("获取问题统计信息成功:",{totalQuestions:e.data.length})}catch(e){console.error("获取问题统计信息失败:",e)}finally{I(!1)}},[a]);(0,l.useImperativeHandle)(t,()=>({fetchQuestionsStats:ei}));let eo=(0,l.useCallback)(async e=>{try{Z(t=>({...t,[e]:!0}));let t=await y.Z.get("/api/projects/".concat(a,"/distill/questions/by-tag?tagId=").concat(e));p(a=>({...a,[e]:t.data}))}catch(e){console.error("获取标签问题失败:",e)}finally{Z(t=>({...t,[e]:!1}))}},[a]);(0,l.useEffect)(()=>{a&&y.Z.get("/api/projects/".concat(a)).then(e=>{ea(e.data),el(e.data.name||"")}).catch(e=>{console.error("获取项目信息失败:",e)})},[a]),(0,l.useEffect)(()=>{ei()},[ei]);let er=(0,l.useMemo)(()=>{if(!i||0===i.length)return[];let e={};i.forEach(t=>{e[t.id]={...t,children:[]}});let t=[];return i.forEach(a=>{a.parentId&&e[a.parentId]?e[a.parentId].children.push(e[a.id]):t.push(e[a.id])}),function e(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return Array.isArray(t)?t.map((t,l)=>{let n;if(0===a){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let a=Math.floor(e/10),s=e%10;return 1===a?0===s?"十":"十"+t[s]:t[a]+"十"+(0===s?"":t[s])}(l+1);n="".concat(e,"、").concat(t.label)}else n="".concat(s+1,".").concat(l+1," ").concat(t.label);let i={...t,displayLabel:n,displayName:t.label};return t.child&&t.child.length>0&&(i.child=e(t.child,a+1,l)),t.children&&t.children.length>0&&(i.children=e(t.children,a+1,l)),i}):[]}(t)},[i]),ec=(0,l.useCallback)(e=>{u(t=>({...t,[e]:!t[e]})),d[e]||x[e]||eo(e)},[d,x,eo]),ed=(e,t)=>{e.stopPropagation(),T(e.currentTarget),w(t)},eu=()=>{T(null),w(null)},eg=async e=>{console.log("标签编辑成功:",e),window.location.reload()},eh=(e,t)=>{t.stopPropagation(),console.log("打开问题编辑对话框",e),ee(e),X(!0)},ex=async e=>{console.log("问题编辑成功:",e),await ei(),Object.keys(d).forEach(e=>{d[e]&&eo(e)})},ep=()=>{N(!1)},em=(e,t)=>{t.stopPropagation(),D(e),E(!0)},ej=()=>{E(!1),D(null)},ef=async()=>{if(W)try{await y.Z.delete("/api/projects/".concat(a,"/questions/").concat(W)),p(e=>{let t={...e};return Object.keys(t).forEach(e=>{t[e]=t[e].filter(e=>e.id!==W)}),t}),ej()}catch(e){console.error("删除问题失败:",e)}},eZ=async(e,t,s)=>{s.stopPropagation(),L(t=>({...t,[e]:!0})),await en({projectId:a,questionId:e,questionInfo:t}),L(t=>({...t,[e]:!1}))},eb=(0,l.useCallback)(e=>{if(!e)return"";let t=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],s=[e.label,...a];if(!e.parentId)return es&&!s.includes(es)?[es,...s]:s;let l=i.find(t=>t.id===e.parentId);return l?t(l,s):es&&!s.includes(es)?[es,...s]:s},a=t(e);return es&&a.length>0&&a[0]!==es&&a.unshift(es),a.join(" > ")},[i,es]),ey=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=J(e);return(0,s.jsx)(b.Z,{disablePadding:!0,sx:{px:2},children:a.map(e=>(0,s.jsx)(F,{tag:e,level:t,expanded:d[e.id],onToggle:ec,onMenuOpen:ed,onGenerateQuestions:e=>{(async()=>{await r(e,eb(e)),await ei(),d[e.id]&&await eo(e.id)})()},onGenerateSubTags:e=>o(e,eb(e)),questions:x[e.id]||[],loadingQuestions:f[e.id],processingQuestions:B,onDeleteQuestion:em,onEditQuestion:eh,onGenerateDataset:eZ,allQuestions:q,tagQuestions:x,children:e.children&&e.children.length>0&&d[e.id]&&ey(e.children,t+1)},e.id))})};return(0,s.jsxs)(g.Z,{children:[er.length>0?ey(er):(0,s.jsx)(g.Z,{sx:{p:2,textAlign:"center"},children:(0,s.jsx)(h.Z,{variant:"body1",color:"text.secondary",children:c("distill.noTags")})}),(0,s.jsx)(R,{anchorEl:C,open:!!C,onClose:eu,onDelete:()=>{console.log("打开删除确认对话框",S),G(S),N(!0),eu()},onEdit:()=>{console.log("打开编辑对话框",S),_(S),O(!0),eu()}}),(0,s.jsx)($,{open:Q,onClose:ep,onConfirm:()=>{if(!M){console.log("没有要删除的标签信息");return}console.log("开始删除标签:",M.id,M.label),ep(),(async()=>{try{console.log("发送删除请求:","/api/projects/".concat(a,"/tags?id=").concat(M.id));let e=await y.Z.delete("/api/projects/".concat(a,"/tags?id=").concat(M.id));console.log("删除标签成功:",e.data),window.location.reload()}catch(e){console.error("删除标签失败:",e),console.error("错误详情:",e.response?e.response.data:"无响应数据"),alert("删除标签失败: ".concat(e.message))}})()},title:c("distill.deleteTagConfirmTitle"),cancelText:c("common.cancel"),confirmText:c("common.delete"),confirmColor:"error"}),(0,s.jsx)($,{open:z,onClose:ej,onConfirm:ef,title:c("questions.deleteConfirm"),cancelText:c("common.cancel"),confirmText:c("common.delete"),confirmColor:"error"}),(0,s.jsx)(U,{open:A,onClose:()=>{O(!1),_(null)},tag:H,projectId:a,onSuccess:eg}),(0,s.jsx)(K,{open:V,onClose:()=>{X(!1),ee(null)},question:Y,projectId:a,tags:i,onSuccess:ex})]})});var Y=a(81705),ee=a(10376);function et(e){let{open:t,onClose:a,onGenerated:i,projectId:o,parentTag:r,tagPath:c,model:x}=e,{t:f}=(0,n.$G)(),[Z,b]=(0,l.useState)(!1),[v,C]=(0,l.useState)(""),[T,S]=(0,l.useState)(5),[w,k]=(0,l.useState)([]),[P,I]=(0,l.useState)(""),[B,L]=(0,l.useState)(null);(0,l.useEffect)(()=>{o&&!r?y.Z.get("/api/projects/".concat(o)).then(e=>{L(e.data),I(e.data.name||"")}).catch(e=>{console.error("获取项目信息失败:",e)}):r&&I(r.label||"")},[o,r]);let z=async()=>{try{b(!0),C("");let e=await y.Z.post("/api/projects/".concat(o,"/distill/tags"),{parentTag:P,parentTagId:r?r.id:null,tagPath:c||P,count:T,model:x,language:ee.Z.language});k(e.data)}catch(a){var e,t;console.error("生成标签失败:",a),C((null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||f("distill.generateTagsError"))}finally{b(!1)}},E=async()=>{i&&i(w),W()},W=()=>{k([]),C(""),S(5),a&&a()};return(0,s.jsxs)(G.Z,{open:t,onClose:W,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2}},children:[(0,s.jsxs)(A.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,s.jsx)(h.Z,{variant:"h6",component:"div",children:r?f("distill.generateSubTagsTitle",{parentTag:r.label}):f("distill.generateRootTagsTitle")}),(0,s.jsx)(p.Z,{edge:"end",color:"inherit",onClick:W,"aria-label":"close",children:(0,s.jsx)(Y.Z,{})})]}),(0,s.jsxs)(H.Z,{dividers:!0,children:[v&&(0,s.jsx)(d.Z,{severity:"error",sx:{mb:2},children:v}),r&&c&&(0,s.jsxs)(g.Z,{sx:{mb:3},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.tagPath"),":"]}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:(0,s.jsx)(h.Z,{variant:"body1",children:c||r.label})})]}),(0,s.jsxs)(g.Z,{sx:{mb:3},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.parentTag"),":"]}),(0,s.jsx)(_.Z,{fullWidth:!0,variant:"outlined",value:P,onChange:e=>I(e.target.value),placeholder:f("distill.parentTagPlaceholder"),disabled:Z||!r,InputProps:{readOnly:!r},helperText:r?f("distill.parentTagHelp"):f("distill.rootTopicHelperText",{defaultValue:"使用项目名称作为顶级主题"})})]}),(0,s.jsxs)(g.Z,{sx:{mb:3},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.tagCount"),":"]}),(0,s.jsx)(_.Z,{fullWidth:!0,variant:"outlined",type:"number",value:T,onChange:e=>{let t=parseInt(e.target.value);!isNaN(t)&&t>=1&&t<=100&&S(t)},inputProps:{min:1,max:100},disabled:Z,helperText:f("distill.tagCountHelp")})]}),w.length>0&&(0,s.jsxs)(g.Z,{children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.generatedTags"),":"]}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:(0,s.jsx)(g.Z,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:w.map((e,t)=>(0,s.jsx)(q.Z,{label:e.label,color:"primary",variant:"outlined"},t))})})]})]}),(0,s.jsxs)(O.Z,{sx:{p:2},children:[(0,s.jsx)(m.Z,{onClick:W,color:"inherit",children:f("common.cancel")}),w.length>0?(0,s.jsx)(m.Z,{onClick:E,color:"primary",variant:"contained",children:f("common.complete")}):(0,s.jsx)(m.Z,{variant:"contained",color:"primary",onClick:z,disabled:Z||!P,startIcon:Z&&(0,s.jsx)(j.Z,{size:20,color:"inherit"}),children:Z?f("common.generating"):f("distill.generateTags")})]})]})}var ea=a(8350);function es(e){let{open:t,onClose:a,onGenerated:i,projectId:o,tag:r,tagPath:c,model:x}=e,{t:f}=(0,n.$G)(),[Z,v]=(0,l.useState)(!1),[T,S]=(0,l.useState)(""),[q,k]=(0,l.useState)(5),[P,I]=(0,l.useState)([]),B=async()=>{try{v(!0),S("");let e=await y.Z.post("/api/projects/".concat(o,"/distill/questions"),{tagPath:c,currentTag:r.label,tagId:r.id,count:q,model:x,language:ee.Z.language});I(e.data)}catch(a){var e,t;console.error("生成问题失败:",a),S((null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.error)||f("distill.generateQuestionsError"))}finally{v(!1)}},L=async()=>{i&&i(P),z()},z=()=>{I([]),S(""),k(5),a&&a()};return(0,s.jsxs)(G.Z,{open:t,onClose:z,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2}},children:[(0,s.jsxs)(A.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,s.jsx)(h.Z,{variant:"h6",component:"div",children:f("distill.generateQuestionsTitle",{tag:(null==r?void 0:r.label)||f("distill.unknownTag")})}),(0,s.jsx)(p.Z,{edge:"end",color:"inherit",onClick:z,"aria-label":"close",children:(0,s.jsx)(Y.Z,{})})]}),(0,s.jsxs)(H.Z,{dividers:!0,children:[T&&(0,s.jsx)(d.Z,{severity:"error",sx:{mb:2},children:T}),(0,s.jsxs)(g.Z,{sx:{mb:3},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.tagPath"),":"]}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:(0,s.jsx)(h.Z,{variant:"body1",children:c||(null==r?void 0:r.label)||f("distill.unknownTag")})})]}),(0,s.jsxs)(g.Z,{sx:{mb:3},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.questionCount"),":"]}),(0,s.jsx)(_.Z,{fullWidth:!0,variant:"outlined",type:"number",value:q,onChange:e=>{let t=parseInt(e.target.value);!isNaN(t)&&t>=1&&t<=100&&k(t)},inputProps:{min:1,max:100},disabled:Z,helperText:f("distill.questionCountHelp")})]}),P.length>0&&(0,s.jsxs)(g.Z,{children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:[f("distill.generatedQuestions"),":"]}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:0,borderRadius:1,backgroundColor:"background.paper"},children:(0,s.jsx)(b.Z,{disablePadding:!0,children:P.map((e,t)=>(0,s.jsxs)(l.Fragment,{children:[t>0&&(0,s.jsx)(ea.Z,{}),(0,s.jsx)(C.ZP,{children:(0,s.jsx)(w.Z,{primary:e.question,primaryTypographyProps:{style:{whiteSpace:"normal",wordBreak:"break-word"}}})})]},t))})})]})]}),(0,s.jsxs)(O.Z,{sx:{p:2},children:[(0,s.jsx)(m.Z,{onClick:z,color:"inherit",children:f("common.cancel")}),P.length>0?(0,s.jsx)(m.Z,{onClick:L,color:"primary",variant:"contained",children:f("common.complete")}):(0,s.jsx)(m.Z,{variant:"contained",color:"primary",onClick:B,disabled:Z,startIcon:Z&&(0,s.jsx)(j.Z,{size:20,color:"inherit"}),children:Z?f("common.generating"):f("distill.generateQuestions")})]})]})}function el(e){let{open:t,onClose:a,onStart:i,projectId:o,project:r,stats:c={}}=e,{t:x}=(0,n.$G)(),[p,j]=(0,l.useState)(""),[f,Z]=(0,l.useState)(2),[b,y]=(0,l.useState)(10),[v,C]=(0,l.useState)(10),[T,S]=(0,l.useState)(0),[w,q]=(0,l.useState)(0),[k,P]=(0,l.useState)(0),[I,B]=(0,l.useState)(0),[L,z]=(0,l.useState)(0),[E,W]=(0,l.useState)("");return(0,l.useEffect)(()=>{r&&r.name&&j(r.name)},[r]),(0,l.useEffect)(()=>{let e=Math.pow(b,f),t=e*v;q(e),S(e),P(t);let a=c.tagsCount||0,s=c.questionsCount||0;B(Math.max(0,e-a)),z(Math.max(0,t-s)),e<=a&&t<=s?W(x("distill.autoDistillInsufficientError")):W("")},[f,b,v,c,x]),(0,s.jsxs)(G.Z,{open:t,onClose:a,maxWidth:"md",fullWidth:!0,children:[(0,s.jsx)(A.Z,{children:x("distill.autoDistillTitle")}),(0,s.jsx)(H.Z,{children:(0,s.jsxs)(g.Z,{sx:{py:2,display:"flex",flexDirection:{xs:"column",md:"row"},gap:3},children:[(0,s.jsxs)(g.Z,{sx:{flex:1},children:[(0,s.jsx)(_.Z,{label:x("distill.distillTopic"),value:p,onChange:e=>j(e.target.value),fullWidth:!0,margin:"normal",required:!0,disabled:!0,helperText:x("distill.rootTopicHelperText")}),(0,s.jsxs)(g.Z,{sx:{mt:3,mb:2},children:[(0,s.jsx)(h.Z,{gutterBottom:!0,children:x("distill.tagLevels")}),(0,s.jsx)(_.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:5}},value:f,onChange:e=>{Z(Math.min(5,Math.max(1,Number(e.target.value))))},helperText:x("distill.tagLevelsHelper",{max:5})})]}),(0,s.jsxs)(g.Z,{sx:{mt:3,mb:2},children:[(0,s.jsx)(h.Z,{gutterBottom:!0,children:x("distill.tagsPerLevel")}),(0,s.jsx)(_.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:50}},value:b,onChange:e=>{y(Math.min(50,Math.max(1,Number(e.target.value))))},helperText:x("distill.tagsPerLevelHelper",{max:50})})]}),(0,s.jsxs)(g.Z,{sx:{mt:3,mb:2},children:[(0,s.jsx)(h.Z,{gutterBottom:!0,children:x("distill.questionsPerTag")}),(0,s.jsx)(_.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:50}},value:v,onChange:e=>{C(Math.min(50,Math.max(1,Number(e.target.value))))},helperText:x("distill.questionsPerTagHelper",{max:50})})]})]}),(0,s.jsxs)(g.Z,{sx:{flex:1,display:"flex",flexDirection:"column"},children:[(0,s.jsxs)(u.Z,{variant:"outlined",sx:{p:3,mt:1,borderRadius:2,flex:1,display:"flex",flexDirection:"column"},children:[(0,s.jsx)(h.Z,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:x("distill.estimationInfo")}),(0,s.jsxs)(g.Z,{sx:{flex:1,display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[(0,s.jsxs)(g.Z,{children:[(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2,mt:2},children:[(0,s.jsxs)(h.Z,{variant:"subtitle2",children:[x("distill.estimatedTags"),":"]}),(0,s.jsx)(h.Z,{variant:"subtitle1",fontWeight:"medium",children:T})]}),(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,s.jsxs)(h.Z,{variant:"subtitle2",children:[x("distill.estimatedQuestions"),":"]}),(0,s.jsx)(h.Z,{variant:"subtitle1",fontWeight:"medium",children:k})]}),(0,s.jsx)(ea.Z,{sx:{my:2}}),(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,s.jsxs)(h.Z,{variant:"subtitle2",children:[x("distill.currentTags"),":"]}),(0,s.jsx)(h.Z,{variant:"subtitle1",fontWeight:"medium",children:c.tagsCount||0})]}),(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,s.jsxs)(h.Z,{variant:"subtitle2",children:[x("distill.currentQuestions"),":"]}),(0,s.jsx)(h.Z,{variant:"subtitle1",fontWeight:"medium",children:c.questionsCount||0})]})]}),(0,s.jsxs)(g.Z,{sx:{pt:2,borderTop:"1px dashed",borderColor:"divider"},children:[(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",color:"primary",children:[x("distill.newTags"),":"]}),(0,s.jsx)(h.Z,{variant:"h6",fontWeight:"bold",color:"primary.main",children:I})]}),(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,s.jsxs)(h.Z,{variant:"subtitle1",color:"primary",children:[x("distill.newQuestions"),":"]}),(0,s.jsx)(h.Z,{variant:"h6",fontWeight:"bold",color:"primary.main",children:L})]})]})]})]}),E&&(0,s.jsx)(d.Z,{severity:"error",sx:{mt:2},children:E})]})]})}),(0,s.jsxs)(O.Z,{children:[(0,s.jsx)(m.Z,{onClick:a,children:x("common.cancel")}),(0,s.jsx)(m.Z,{onClick:()=>{E||i({topic:p,levels:f,tagsPerLevel:b,questionsPerTag:v,estimatedTags:T,estimatedQuestions:k})},color:"primary",variant:"contained",disabled:!!E||!p,children:x("distill.startAutoDistill")})]})]})}var en=a(59253);function ei(e){var t;let{open:a,onClose:i,progress:o={}}=e,{t:r}=(0,n.$G)(),c=(0,l.useRef)(null);(0,l.useEffect)(()=>{c.current&&(c.current.scrollTop=c.current.scrollHeight)},[o.logs]);let d=()=>{let{tagsBuilt:e,tagsTotal:t,questionsBuilt:a,questionsTotal:s,datasetsBuilt:l,datasetsTotal:n}=o;return Math.min(100,Math.round((t?e/t*30:0)+(s?a/s*35:0)+(n?l/n*35:0)))};return(0,s.jsxs)(G.Z,{open:a,onClose:"completed"!==o.stage&&o.stage?null:i,maxWidth:"md",fullWidth:!0,children:[(0,s.jsx)(A.Z,{children:(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r("distill.autoDistillProgress"),("completed"===o.stage||!o.stage)&&(0,s.jsx)(p.Z,{onClick:i,"aria-label":"close",children:(0,s.jsx)(Y.Z,{})})]})}),(0,s.jsx)(H.Z,{children:(0,s.jsxs)(g.Z,{sx:{py:1},children:[(0,s.jsxs)(g.Z,{sx:{mb:4},children:[(0,s.jsx)(h.Z,{variant:"h6",gutterBottom:!0,children:r("distill.overallProgress")}),(0,s.jsxs)(g.Z,{sx:{mb:2},children:[(0,s.jsx)(en.Z,{variant:"determinate",value:d(),sx:{height:10,borderRadius:5}}),(0,s.jsx)(g.Z,{sx:{display:"flex",justifyContent:"flex-end",mt:.5},children:(0,s.jsxs)(h.Z,{variant:"body2",color:"text.secondary",children:[d(),"%"]})})]}),(0,s.jsxs)(g.Z,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:[(0,s.jsxs)(u.Z,{variant:"outlined",sx:{p:2},children:[(0,s.jsx)(h.Z,{variant:"body2",color:"text.secondary",children:r("distill.tagsProgress")}),(0,s.jsxs)(h.Z,{variant:"h6",children:[o.tagsBuilt||0," / ",o.tagsTotal||0]})]}),(0,s.jsxs)(u.Z,{variant:"outlined",sx:{p:2},children:[(0,s.jsx)(h.Z,{variant:"body2",color:"text.secondary",children:r("distill.questionsProgress")}),(0,s.jsxs)(h.Z,{variant:"h6",children:[o.questionsBuilt||0," / ",o.questionsTotal||0]})]}),(0,s.jsxs)(u.Z,{variant:"outlined",sx:{p:2},children:[(0,s.jsx)(h.Z,{variant:"body2",color:"text.secondary",children:r("distill.datasetsProgress")}),(0,s.jsxs)(h.Z,{variant:"h6",children:[o.datasetsBuilt||0," / ",o.datasetsTotal||0]})]})]})]}),(0,s.jsxs)(g.Z,{sx:{mb:4},children:[(0,s.jsx)(h.Z,{variant:"h6",gutterBottom:!0,children:r("distill.currentStage")}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:2,bgcolor:"primary.light",color:"primary.contrastText"},children:(0,s.jsx)(h.Z,{variant:"h6",children:(()=>{let{stage:e}=o;switch(e){case"level1":return r("distill.stageBuildingLevel1");case"level2":return r("distill.stageBuildingLevel2");case"level3":return r("distill.stageBuildingLevel3");case"level4":return r("distill.stageBuildingLevel4");case"level5":return r("distill.stageBuildingLevel5");case"questions":return r("distill.stageBuildingQuestions");case"datasets":return r("distill.stageBuildingDatasets");case"completed":return r("distill.stageCompleted");default:return r("distill.stageInitializing")}})()})})]}),(0,s.jsxs)(g.Z,{sx:{mb:2},children:[(0,s.jsx)(h.Z,{variant:"h6",gutterBottom:!0,children:r("distill.realTimeLogs")}),(0,s.jsx)(u.Z,{variant:"outlined",sx:{p:2,maxHeight:250,overflow:"auto",bgcolor:"grey.900",color:"grey.100",fontFamily:"monospace",fontSize:"0.875rem"},ref:c,children:(null===(t=o.logs)||void 0===t?void 0:t.length)>0?o.logs.map((e,t)=>{let a="inherit";return(e.includes("成功")||e.includes("完成")||e.includes("Successfully"))&&(a="#4caf50"),(e.includes("失败")||e.toLowerCase().includes("error"))&&(a="#f44336"),(0,s.jsx)(g.Z,{sx:{mb:.5,color:a},children:e},t)}):(0,s.jsx)(h.Z,{variant:"body2",color:"grey.500",children:r("distill.waitingForLogs")})})]})]})})]})}class eo{async executeDistillTask(e){let{projectId:t,topic:a,levels:s,tagsPerLevel:l,questionsPerTag:n,model:i,language:o,concurrencyLimit:r=5,onProgress:c,onLog:d}=e;this.projectName="";try{c&&c({stage:"initializing",tagsTotal:0,tagsBuilt:0,questionsTotal:0,questionsBuilt:0,datasetsTotal:0,datasetsBuilt:0});try{let e=await y.Z.get("/api/projects/".concat(t));e&&e.data&&e.data.name?(this.projectName=e.data.name,this.addLog(d,'Using project name "'.concat(this.projectName,'" as the top-level tag'))):(this.projectName=a,this.addLog(d,'Could not find project name, using topic "'.concat(a,'" as the top-level tag')))}catch(e){this.projectName=a,this.addLog(d,'Failed to get project name, using topic "'.concat(a,'" instead: ').concat(e.message))}this.addLog(d,'Starting to build tag tree for "'.concat(a,'", number of levels: ').concat(s,", tags per level: ").concat(l,", questions per tag: ").concat(n)),await this.buildTagTree({projectId:t,topic:a,levels:s,tagsPerLevel:l,model:i,language:o,onProgress:c,onLog:d}),await this.generateQuestionsForTags({projectId:t,levels:s,questionsPerTag:n,model:i,language:o,concurrencyLimit:r,onProgress:c,onLog:d}),await this.generateDatasetsForQuestions({projectId:t,model:i,language:o,concurrencyLimit:r,onProgress:c,onLog:d}),c&&c({stage:"completed"}),this.addLog(d,"Auto distillation task completed")}catch(e){throw console.error("自动蒸馏任务执行失败:",e),this.addLog(d,"Task execution error: ".concat(e.message||"Unknown error")),e}}async buildTagTree(e){var t=this;let{projectId:a,topic:s,levels:l,tagsPerLevel:n,model:i,language:o,onProgress:r,onLog:c}=e,d=this.projectName||s,u=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,g=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(r&&r({stage:"level".concat(h)}),h>l)return;let x=[];try{let t=await y.Z.get("/api/projects/".concat(a,"/distill/tags/all"));x=e?t.data.filter(t=>t.parentId===e.id):t.data.filter(e=>!e.parentId)}catch(e){console.error("获取".concat(h,"级标签失败:"),e),t.addLog(c,"Failed to get ".concat(h," level tags: ").concat(e.message));return}let p=Math.max(0,n-x.length);if(p>0){let l;let u=1===h?s:(null==e?void 0:e.label)||"";t.addLog(c,"Tag tree level ".concat(h,": Creating ").concat(n,' subtags for "').concat(u,'"...')),l=1===h?d:g?g.startsWith(d)?g:"".concat(d," > ").concat(g):d;try{let s=await y.Z.post("/api/projects/".concat(a,"/distill/tags"),{parentTag:u,parentTagId:e?e.id:null,tagPath:l||u,count:p,model:i,language:o});r&&r({tagsBuilt:s.data.length,updateType:"increment"}),t.addLog(c,"Successfully created ".concat(s.data.length," tags: ").concat(s.data.map(e=>e.label).join(", "))),x=[...x,...s.data]}catch(e){console.error("创建".concat(h,"级标签失败:"),e),t.addLog(c,"Failed to create ".concat(h," level tags: ").concat(e.message||"Unknown error"))}}if(h<l)for(let e of x){let t;t=g?"".concat(g," > ").concat(e.label):"".concat(d," > ").concat(e.label),await u(e,t,h+1)}};r&&r({tagsTotal:Math.pow(n,l)}),await u()}async generateQuestionsForTags(e){let{projectId:t,levels:a,questionsPerTag:s,model:l,language:n,concurrencyLimit:i=5,onProgress:o,onLog:r}=e;o&&o({stage:"questions"}),this.addLog(r,"Tag tree built, starting to generate questions for leaf tags...");try{let e=(await y.Z.get("/api/projects/".concat(t,"/distill/tags/all"))).data,c=[],d={};e.forEach(e=>{e.parentId&&(d[e.parentId]||(d[e.parentId]=[]),d[e.parentId].push(e))}),e.forEach(t=>{d[t.id]||this.getTagDepth(t,e)!==a||c.push(t)}),this.addLog(r,"Found ".concat(c.length," leaf tags, starting to generate questions..."));let u=(await y.Z.get("/api/projects/".concat(t,"/questions/tree?isDistill=true"))).data,g=c.length*s;o&&o({questionsTotal:g});let h=[];for(let t of c){let a=this.getTagPath(t,e),l=u.filter(e=>e.label===t.label),n=Math.max(0,s-l.length);n>0?(h.push({tag:t,tagPath:a,needToCreate:n}),this.addLog(r,"Preparing to generate ".concat(n,' questions for tag "').concat(t.label,'"...'))):this.addLog(r,'Tag "'.concat(t.label,'" already has ').concat(l.length," questions, no need to generate new questions"))}this.addLog(r,"Total ".concat(h.length," tags need questions, concurrency limit: ").concat(i));for(let e=0;e<h.length;e+=i){let a=h.slice(e,e+i);await Promise.all(a.map(async e=>{let{tag:a,tagPath:s,needToCreate:i}=e;this.addLog(r,"Generating ".concat(i,' questions for tag "').concat(a.label,'"...'));try{let e=await y.Z.post("/api/projects/".concat(t,"/distill/questions"),{tagPath:s,currentTag:a.label,tagId:a.id,count:i,model:l,language:n});o&&o({questionsBuilt:e.data.length,updateType:"increment"}),e.data.map(e=>e.question||e.content).slice(0,3).join("\n"),this.addLog(r,"Successfully generated ".concat(e.data.length,' questions for tag "').concat(a.label,'"'))}catch(e){console.error('为标签 "'.concat(a.label,'" 生成问题失败:'),e),this.addLog(r,'Failed to generate questions for tag "'.concat(a.label,'": ').concat(e.message||"Unknown error"))}})),this.addLog(r,"Completed batch ".concat(Math.min(e+i,h.length),"/").concat(h.length," of question generation"))}}catch(e){console.error("获取标签失败:",e),this.addLog(r,"Failed to get tags: ".concat(e.message||"Unknown error"))}}async generateDatasetsForQuestions(e){let{projectId:t,model:a,language:s,concurrencyLimit:l=5,onProgress:n,onLog:i}=e;n&&n({stage:"datasets"}),this.addLog(i,"Question generation completed, starting to generate answers...");try{let e=(await y.Z.get("/api/projects/".concat(t,"/questions/tree?isDistill=true"))).data,o=e.filter(e=>!e.answered),r=e.filter(e=>e.answered);n&&n({datasetsTotal:e.length,datasetsBuilt:r.length}),this.addLog(i,"Found ".concat(o.length," unanswered questions, preparing to generate answers...")),this.addLog(i,"Dataset generation concurrency limit: ".concat(l));for(let e=0;e<o.length;e+=l){let r=o.slice(e,e+l);await Promise.all(r.map(async e=>{let l="".concat(e.label," 下的问题ID:").concat(e.id);this.addLog(i,'Generating answer for "'.concat(l,'"...'));try{await this.generateSingleDataset({projectId:t,questionId:e.id,questionInfo:e,model:a,language:s}),n&&n({datasetsBuilt:1,updateType:"increment"}),this.addLog(i,'Successfully generated answer for question "'.concat(l,'"'))}catch(t){console.error('Failed to generate dataset for question "'.concat(e.id,'":'),t),this.addLog(i,'Failed to generate answer for question "'.concat(l,'": ').concat(t.message||"Unknown error"))}})),this.addLog(i,"Completed batch ".concat(Math.min(e+l,o.length),"/").concat(o.length," of dataset generation"))}}catch(e){console.error("Failed to get questions:",e),this.addLog(i,"Failed to get questions: ".concat(e.message||"Unknown error"))}}async generateSingleDataset(e){let{projectId:t,questionId:a,questionInfo:s,model:l,language:n}=e;try{let e=s;return e||(e=(await y.Z.get("/api/projects/".concat(t,"/questions/").concat(a))).data),(await y.Z.post("/api/projects/".concat(t,"/datasets"),{projectId:t,questionId:a,model:l,language:n||"zh-CN"})).data}catch(e){throw console.error("Failed to generate dataset:",e),Error("Failed to generate dataset: ".concat(e.message))}}getTagDepth(e,t){let a=1,s=e;for(;s.parentId&&(a++,s=t.find(e=>e.id===s.parentId)););return a}getTagPath(e,t){let a=this.projectName||"",s=[],l=e;for(;l;)s.unshift(l.label),l=l.parentId?t.find(e=>e.id===l.parentId):null;return a&&s.length>0&&s[0]!==a&&s.unshift(a),s.join(" > ")}addLog(e,t){e&&"function"==typeof e&&e(t)}}let er=new eo;function ec(){let{t:e,i18n:t}=(0,n.$G)(),{projectId:a}=(0,i.useParams)(),b=(0,o.useAtomValue)(r._),[v,C]=(0,l.useState)(null),[T,S]=(0,l.useState)(!1),[w,q]=(0,l.useState)(""),[k,P]=(0,l.useState)([]),[I,B]=(0,l.useState)(!1),[L,z]=(0,l.useState)(!1),[W,D]=(0,l.useState)(null),[Q,F]=(0,l.useState)(""),[N,M]=(0,l.useState)(!1),[R,G]=(0,l.useState)(!1),[A,O]=(0,l.useState)(!1),[$,H]=(0,l.useState)({tagsCount:0,questionsCount:0,datasetsCount:0}),[_,U]=(0,l.useState)({stage:"initializing",tagsTotal:0,tagsBuilt:0,questionsTotal:0,questionsBuilt:0,datasetsTotal:0,datasetsBuilt:0,logs:[]}),V=(0,l.useRef)(null);(0,l.useEffect)(()=>{a&&(K(),J(),Y())},[a]);let K=async()=>{try{S(!0);let e=await y.Z.get("/api/projects/".concat(a));C(e.data)}catch(t){console.error("获取项目信息失败:",t),q(e("common.fetchError"))}finally{S(!1)}},J=async()=>{try{S(!0);let e=await y.Z.get("/api/projects/".concat(a,"/distill/tags/all"));P(e.data)}catch(t){console.error("获取标签列表失败:",t),q(e("common.fetchError"))}finally{S(!1)}},Y=async()=>{try{let e=(await y.Z.get("/api/projects/".concat(a,"/distill/tags/all"))).data.length,t=await y.Z.get("/api/projects/".concat(a,"/questions/tree?isDistill=true")),s=t.data.length,l=t.data.filter(e=>e.answered).length;H({tagsCount:e,questionsCount:s,datasetsCount:l})}catch(e){console.error("获取蒸馏统计信息失败:",e)}},ee=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!b||0===Object.keys(b).length){q(e("distill.selectModelFirst"));return}D(t),F(a),B(!0)},ea=async s=>{M(!1),G(!0),O(!0),U({stage:"initializing",tagsTotal:s.estimatedTags,tagsBuilt:$.tagsCount||0,questionsTotal:s.estimatedQuestions,questionsBuilt:$.questionsCount||0,datasetsTotal:s.estimatedQuestions,datasetsBuilt:$.datasetsCount||0,logs:[e("distill.autoDistillStarted",{time:new Date().toLocaleTimeString()})]});try{var l;if(!b||0===Object.keys(b).length){eo(e("distill.selectModelFirst")),O(!1);return}await er.executeDistillTask({projectId:a,topic:s.topic,levels:s.levels,tagsPerLevel:s.tagsPerLevel,questionsPerTag:s.questionsPerTag,model:b,language:t.language,concurrencyLimit:(null==v?void 0:null===(l=v.taskConfig)||void 0===l?void 0:l.concurrencyLimit)||5,onProgress:en,onLog:eo}),O(!1)}catch(e){console.error("自动蒸馏任务执行失败:",e),eo("任务执行出错: ".concat(e.message||"未知错误")),O(!1)}},en=e=>{U(t=>{let a={...t};return e.stage&&(a.stage=e.stage),e.tagsTotal&&(a.tagsTotal=e.tagsTotal),e.tagsBuilt&&("increment"===e.updateType?a.tagsBuilt+=e.tagsBuilt:a.tagsBuilt=e.tagsBuilt),e.questionsTotal&&(a.questionsTotal=e.questionsTotal),e.questionsBuilt&&("increment"===e.updateType?a.questionsBuilt+=e.questionsBuilt:a.questionsBuilt=e.questionsBuilt),e.datasetsTotal&&(a.datasetsTotal=e.datasetsTotal),e.datasetsBuilt&&("increment"===e.updateType?a.datasetsBuilt+=e.datasetsBuilt:a.datasetsBuilt=e.datasetsBuilt),a})},eo=e=>{U(t=>{let a=[...t.logs,e],s=a.length>200?a.slice(-200):a;return{...t,logs:s}})};return a?(0,s.jsxs)(c.Z,{maxWidth:"lg",sx:{mt:4,mb:8},children:[(0,s.jsxs)(u.Z,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[(0,s.jsxs)(g.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4,paddingLeft:"32px"},children:[(0,s.jsxs)(g.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,s.jsx)(h.Z,{variant:"h5",component:"h1",fontWeight:"bold",children:e("distill.title")}),(0,s.jsx)(x.Z,{title:e("common.help"),children:(0,s.jsx)(p.Z,{size:"small",onClick:()=>{let e="en"===t.language?"https://docs.easy-dataset.com/ed/en/advanced/images-and-media":"https://docs.easy-dataset.com/jin-jie-shi-yong/images-and-media";window.open(e,"_blank")},sx:{color:"text.secondary"},children:(0,s.jsx)(E.Z,{fontSize:"small"})})})]}),(0,s.jsxs)(g.Z,{sx:{display:"flex",gap:2},children:[(0,s.jsx)(m.Z,{variant:"outlined",color:"primary",size:"large",onClick:()=>{if(!b||0===Object.keys(b).length){q(e("distill.selectModelFirst"));return}M(!0)},disabled:!b,startIcon:(0,s.jsx)(Z.Z,{}),sx:{px:3,py:1},children:e("distill.autoDistillButton")}),(0,s.jsx)(m.Z,{variant:"contained",color:"primary",size:"large",onClick:()=>ee(null),disabled:!b,startIcon:(0,s.jsx)(f.Z,{}),sx:{px:3,py:1},children:e("distill.generateRootTags")})]})]}),w&&(0,s.jsx)(d.Z,{severity:"error",sx:{mb:4,px:3,py:2},onClose:()=>q(""),children:w}),T?(0,s.jsx)(g.Z,{sx:{display:"flex",justifyContent:"center",p:6},children:(0,s.jsx)(j.Z,{size:40})}):(0,s.jsx)(g.Z,{sx:{mt:2},children:(0,s.jsx)(X,{ref:V,projectId:a,tags:k,onGenerateSubTags:ee,onGenerateQuestions:(t,a)=>{if(!b||0===Object.keys(b).length){q(e("distill.selectModelFirst"));return}D(t),F(a),z(!0)}})})]}),I&&(0,s.jsx)(et,{open:I,onClose:()=>B(!1),onGenerated:()=>{J(),B(!1)},projectId:a,parentTag:W,tagPath:Q,model:b}),L&&(0,s.jsx)(es,{open:L,onClose:()=>z(!1),onGenerated:()=>{z(!1),J(),Y(),V.current&&"function"==typeof V.current.fetchQuestionsStats&&V.current.fetchQuestionsStats()},projectId:a,tag:W,tagPath:Q,model:b}),(0,s.jsx)(el,{open:N,onClose:()=>M(!1),onStart:ea,projectId:a,project:v,stats:$}),(0,s.jsx)(ei,{open:R,onClose:()=>{A?G(!1):(G(!1),J(),Y(),V.current&&"function"==typeof V.current.fetchQuestionsStats&&V.current.fetchQuestionsStats())},progress:_})]}):(0,s.jsx)(c.Z,{maxWidth:"lg",sx:{mt:4},children:(0,s.jsx)(d.Z,{severity:"error",children:e("common.projectIdRequired")})})}},14314:function(e,t,a){"use strict";a.d(t,{l:function(){return d}});var s=a(2265),l=a(14438),n=a(10376),i=a(83464),o=a(92964),r=a(66257),c=a(43949);function d(){let e=(0,o.useAtomValue)(r._),{t}=(0,c.$G)();return{generateSingleDataset:(0,s.useCallback)(async a=>{let{projectId:s,questionId:o,questionInfo:r}=a;if(!e)return l.A.error(t("models.configNotFound")),null;let c="zh-CN"===n.Z.language?"中文":"en";l.A.promise(i.Z.post("/api/projects/".concat(s,"/datasets"),{questionId:o,model:e,language:c}),{loading:t("datasets.generating"),description:"问题：【".concat(r,"】"),position:"top-right",success:e=>"生成数据集成功",error:e=>{var a,s;return t("datasets.generateFailed",{error:null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.error})}})},[e,t]),generateMultipleDataset:(0,s.useCallback)(async(t,a)=>{let s=0,o=a.length,r=l.A.loading("正在处理请求 (".concat(s,"/").concat(o,")..."),{position:"top-right"}),c=async a=>{try{let c=(await i.Z.post("/api/projects/".concat(t,"/datasets"),{questionId:a.id,model:e,language:"zh-CN"===n.Z.language?"中文":"en"})).data;return s++,l.A.success("".concat(a.question," 完成"),{position:"top-right"}),l.A.loading("正在处理请求 (".concat(s,"/").concat(o,")..."),{id:r}),c}catch(e){throw s++,l.A.error("".concat(a.question," 失败"),{description:e.message,position:"top-right"}),l.A.loading("正在处理请求 (".concat(s,"/").concat(o,")..."),{id:r}),e}};try{let e=await Promise.allSettled(a.map(e=>c(e)));return l.A.success("全部请求处理完成 (成功: ".concat(e.filter(e=>"fulfilled"===e.status).length,"/").concat(o,")"),{id:r,position:"top-right"}),e}catch(e){}},[e,t])}}},66257:function(e,t,a){"use strict";a.d(t,{V:function(){return l},_:function(){return n}});var s=a(5312);let l=(0,s.O4)("modelConfigList",[]),n=(0,s.O4)("selectedModelInfo",{})}},function(e){e.O(0,[9957,4438,6971,1739,3464,2964,8692,5312,618,2713,3050,613,9794,226,2147,841,376,2971,2117,1744],function(){return e(e.s=48337)}),_N_E=e.O()}]);