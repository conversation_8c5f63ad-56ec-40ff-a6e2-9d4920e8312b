import React, { Component } from 'react';
import { IndexPageStyle, IndexPageContent } from './style';
import TopPage from '../components/topPage';
import LeftPage from '../components/leftPage';
import CenterPage from '../components/centerPage';
import RightPage from '../components/rightPage';
import DataCacheService from '../services/DataCacheService';

const BackButton = () => (
  <button
    style={{
      position: 'absolute',
      left: '0.125rem',
      top: '0.25rem',
      zIndex: 1000,
      margin: '8px',
      backgroundColor: 'transparent',
      border: 'none',
      cursor: 'pointer'
    }}
    onClick={() => window.location.href = '/projects'}
  >
    <img
      src="https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/begin.png"
      alt="返回项目页面"
      style={{ height: '40px' }}
    />
  </button>
);

class IndexPage extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedCountry: null, // 当前选中的国家
      projects: [], // 项目列表
      countryProjectMap: {}, // 国家到项目ID的映射
      globalStatistics: {}, // 全局统计数据
      globalDashboard: {}, // 全局仪表板数据
      globalTimeline: {}, // 全局时间序列数据
      loading: true,
      dataReady: false // 数据是否准备就绪
    };
  }

  // 组件挂载后预加载所有数据
  async componentDidMount() {
    try {
      this.setState({ loading: true });

      // 使用数据缓存服务预加载所有数据
      const {
        projects,
        globalStats,
        globalDashboard,
        globalTimeline,
        countryProjectMap
      } = await DataCacheService.preloadAllData();

      this.setState({
        projects,
        countryProjectMap,
        globalStatistics: globalStats,
        globalDashboard,
        globalTimeline,
        loading: false,
        dataReady: true
      });


    } catch (error) {
      console.error('获取数据失败:', error);
      this.setState({
        loading: false,
        dataReady: true // 即使失败也显示页面，避免白屏
      });
    }
  }

  // 处理国家选中事件
  handleCountrySelect = (countryName) => {
    this.setState({ selectedCountry: countryName });
  }

  render() {
    const {
      selectedCountry,
      countryProjectMap,
      globalStatistics,
      globalDashboard,
      globalTimeline,
      loading,
      dataReady
    } = this.state;

    // 只有在数据未准备好时才显示加载界面
    if (loading || !dataReady) {
      return (
        <IndexPageStyle>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            color: '#fff',
            fontSize: '18px'
          }}>
            <div style={{ marginBottom: '20px' }}>
              正在加载...
            </div>
          </div>
        </IndexPageStyle>
      );
    }

    return (
      <IndexPageStyle>
        <BackButton />
        <TopPage />
        <IndexPageContent>
          {/* 左侧内容 */}
          <LeftPage
            selectedCountry={selectedCountry}
            countryProjectMap={countryProjectMap}
            dataCacheService={DataCacheService}
          />
          {/* 中间内容 */}
          <CenterPage
            className='center-page'
            onCountrySelect={this.handleCountrySelect}
            globalStatistics={globalStatistics}
            globalDashboard={globalDashboard}
            dataCacheService={DataCacheService}
          />
          {/* 右侧内容 */}
          <RightPage
            selectedCountry={selectedCountry}
            countryProjectMap={countryProjectMap}
            globalTimeline={globalTimeline}
            dataCacheService={DataCacheService}
          />
        </IndexPageContent>
      </IndexPageStyle>
    );
  }
}

export default IndexPage;
