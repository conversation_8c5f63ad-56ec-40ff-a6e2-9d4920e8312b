/**
 * 模型答案质量比较图表
 * 比较不同模型生成的答案质量
 */

// 生成虚拟数据：不同模型的答案质量评分
function generateModelQualityData() {
  // 定义模型列表
  const models = ['GPT-4', 'Claude 3', 'Llama 3', 'Mistral', 'GLM-4', 'Qwen'];
  
  // 定义评估维度
  const dimensions = ['准确性', '完整性', '相关性', '清晰度', '创新性'];
  
  // 为每个模型生成评分数据
  const modelData = models.map(model => {
    const scores = {};
    dimensions.forEach(dimension => {
      // 为不同模型设置略有差异的基础分数
      let baseScore;
      switch(model) {
        case 'GPT-4': baseScore = 8.5; break;
        case 'Claude 3': baseScore = 8.7; break;
        case 'Llama 3': baseScore = 8.2; break;
        case 'Mistral': baseScore = 7.9; break;
        case 'GLM-4': baseScore = 8.0; break;
        case 'Qwen': baseScore = 7.8; break;
        default: baseScore = 7.5;
      }
      
      // 为每个维度添加一些随机变化
      scores[dimension] = Math.min(10, Math.max(5, baseScore + (Math.random() - 0.5) * 2));
    });
    
    return {
      name: model,
      scores: scores
    };
  });
  
  return {
    models,
    dimensions,
    data: modelData
  };
}

// 初始化雷达图比较不同模型的答案质量
function initModelQualityRadarChart() {
  const { models, dimensions, data } = generateModelQualityData();
  
  // 准备雷达图数据系列
  const series = data.map(item => {
    return {
      name: item.name,
      type: 'radar',
      data: [{
        value: dimensions.map(dim => item.scores[dim]),
        name: item.name
      }]
    };
  });
  
  // 获取图表容器
  const chartDom = document.getElementById('modelQualityRadarChart');
  const chart = echarts.init(chartDom);
  
  // 配置雷达图选项
  const option = {
    title: {
      text: '模型答案质量多维度比较 - 雷达图 (radar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: models,
      bottom: 0
    },
    radar: {
      indicator: dimensions.map(dim => ({ name: dim, max: 10 })),
      center: ['50%', '50%'],
      radius: '65%'
    },
    series: series
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化条形图比较不同模型的总体答案质量
function initModelQualityBarChart() {
  const { models, dimensions, data } = generateModelQualityData();
  
  // 计算每个模型的平均分
  const averageScores = data.map(item => {
    const scores = dimensions.map(dim => item.scores[dim]);
    const avg = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    return {
      name: item.name,
      score: parseFloat(avg.toFixed(2))
    };
  });
  
  // 按平均分排序
  averageScores.sort((a, b) => b.score - a.score);
  
  // 获取图表容器
  const chartDom = document.getElementById('modelQualityBarChart');
  const chart = echarts.init(chartDom);
  
  // 配置条形图选项
  const option = {
    title: {
      text: '模型答案质量总体评分 - 柱状图 (bar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const data = params[0];
        return `${data.name}: ${data.value}分`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: averageScores.map(item => item.name),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '平均质量评分',
      min: 0,
      max: 10
    },
    series: [{
      name: '平均质量评分',
      type: 'bar',
      data: averageScores.map(item => item.score),
      itemStyle: {
        color: function(params) {
          // 为不同模型设置不同颜色
          const colorList = [
            '#5470c6', '#91cc75', '#fac858', 
            '#ee6666', '#73c0de', '#3ba272'
          ];
          return colorList[params.dataIndex % colorList.length];
        }
      },
      label: {
        show: true,
        position: 'top',
        formatter: '{c}'
      }
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化热力图比较不同模型在各维度的表现
function initModelQualityHeatmapChart() {
  const { models, dimensions, data } = generateModelQualityData();
  
  // 准备热力图数据
  const heatmapData = [];
  data.forEach((item, modelIndex) => {
    dimensions.forEach((dim, dimIndex) => {
      heatmapData.push([modelIndex, dimIndex, item.scores[dim].toFixed(1)]);
    });
  });
  
  // 获取图表容器
  const chartDom = document.getElementById('modelQualityHeatmapChart');
  const chart = echarts.init(chartDom);
  
  // 配置热力图选项
  const option = {
    title: {
      text: '模型答案质量维度详细比较 - 热力图 (heatmap)',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: function(params) {
        return `${models[params.data[0]]}<br>${dimensions[params.data[1]]}: ${params.data[2]}分`;
      }
    },
    grid: {
      left: '3%',
      right: '7%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dimensions,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: models,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 5,
      max: 10,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: ['#e0f5ff', '#51b4ff', '#1170fb']
      }
    },
    series: [{
      name: '质量评分',
      type: 'heatmap',
      data: heatmapData,
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化所有图表
document.addEventListener('DOMContentLoaded', function() {
  initModelQualityRadarChart();
  initModelQualityBarChart();
  initModelQualityHeatmapChart();
}); 