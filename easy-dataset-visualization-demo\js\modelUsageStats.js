/**
 * 模型使用统计条形图
 * 比较不同模型生成的问题/答案的数量
 */

// 生成表示不同模型生成的问题和答案数量的虚拟数据
function generateModelUsageData() {
    // 定义模型列表
    const models = [
        'GPT-4',
        'Claude 3',
        'Gemini',
        'LLaMA 3',
        'Qwen',
        'GLM-4',
        'Mixtral'
    ];
    
    // 为每个模型生成问题和答案的数量
    const data = models.map(model => {
        // 生成随机的问题和答案数量
        const questions = Math.floor(Math.random() * 500) + 200; // 200-700 之间的随机值
        const answers = Math.floor(Math.random() * questions * 0.8) + questions * 0.2; // 问题数量的20%-100%之间
        
        return {
            model: model,
            questions: questions,
            answers: answers
        };
    });
    
    return data;
}

// 初始化ECharts条形图
function initModelUsageChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('model-usage-stats');
    if (!chartContainer) {
        console.error('找不到图表容器：model-usage-stats');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const modelData = generateModelUsageData();
    
    // 提取模型名称、问题数量和答案数量
    const models = modelData.map(item => item.model);
    const questions = modelData.map(item => item.questions);
    const answers = modelData.map(item => item.answers);
    
    // 配置图表选项
    const option = {
        title: {
            text: '模型使用统计 - 堆叠条形图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                const modelName = params[0].name;
                let result = `${modelName}<br/>`;
                
                params.forEach(param => {
                    const seriesName = param.seriesName;
                    const value = param.value;
                    const color = param.color;
                    
                    result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
                    result += `${seriesName}: ${value}<br/>`;
                });
                
                return result;
            }
        },
        legend: {
            data: ['生成的问题', '生成的答案'],
            top: '10%'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '20%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            name: '数量',
            nameLocation: 'middle',
            nameGap: 30,
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: models,
            axisLabel: {
                fontSize: 12
            }
        },
        series: [
            {
                name: '生成的问题',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                    position: 'insideRight'
                },
                emphasis: {
                    focus: 'series'
                },
                data: questions,
                itemStyle: {
                    color: '#5470c6'
                }
            },
            {
                name: '生成的答案',
                type: 'bar',
                stack: 'total',
                label: {
                    show: true,
                    position: 'insideRight'
                },
                emphasis: {
                    focus: 'series'
                },
                data: answers,
                itemStyle: {
                    color: '#91cc75'
                }
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 在文档加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initModelUsageChart();
}); 