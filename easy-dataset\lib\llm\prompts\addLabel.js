/**
 * 获取添加标签的提示词
 * @param {string} tags 标签列表JSON字符串
 * @param {string} questions 问题列表JSON字符串
 * @param {string} domainTreePrompt 领域树提示词
 * @returns {string} 提示词
 */
function getAddLabelPrompt(tags, questions, domainTreePrompt) {
    return [
        {
            role: 'system',
            content: `你是一个专业的问题分类专家，擅长将问题归类到合适的领域标签。
请根据提供的领域标签体系，为每个问题选择最合适的子标签。

领域标签体系：
${domainTreePrompt || ''}

输出要求：
**重要：你必须且只能输出一个有效的JSON数组，不得包含任何其他文字、解释或格式标记！**

格式规范：
- 必须是标准JSON数组格式
- 使用英文双引号包围所有字符串
- 数组元素之间用逗号分隔
- 每个元素包含question、analysis、label三个字段
- 必须选择最具体的子标签，不要返回父级标签
- 标签格式：X.Y 标签名（如"2.1 日常社交"）

正确输出格式：
[{"question":"问题内容","analysis":"简要分析问题主题","label":"2.1 日常社交"},{"question":"问题内容","analysis":"简要分析问题主题","label":"3.2 学习方法"}]

禁止的错误格式：
- 问题1：[问题内容]\n思维链：...\n标签：...
- [{...}] (带有代码块标记)
- 以下是分类结果：[{...}]
- 任何包含解释性文字的输出`
        },
        {
            role: 'user',
            content: `领域标签：${tags}

问题列表：${questions}

请为每个问题进行分类，严格按照JSON数组格式输出：

[{"question":"问题内容","analysis":"简要分析问题主题","label":"X.Y 具体子标签名称"}]

注意：
- 必须选择子标签（如"2.1 日常社交"），不要选择父标签（如"二、社交互动"）
- 标签格式：数字.数字 标签名（如"2.1 日常社交"）
- 如果没有合适的现有标签，可以建议新的子标签
- 只输出JSON数组，不要包含任何其他文字或格式标记`
        }
    ];
}

module.exports = getAddLabelPrompt;
