**添加规则文件可帮助模型精准理解你的编码偏好，如框架、代码风格等**
**规则文件只对当前工程生效，单文件限制10000字符。如果无需将该文件提交到远程 Git 仓库，请将其添加到 .gitignore**

1. When writing code, do not add comments like "1." or "2." that represent steps
2. Loops should be avoided in the code for query, update, deletion and addition operations, and collection operation methods should be preferred
3. Prefer using existing tool classes or methods already available in the project or imported in the pom file to simplify the code