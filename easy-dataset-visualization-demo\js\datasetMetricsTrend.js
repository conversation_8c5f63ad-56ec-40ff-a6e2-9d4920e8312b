/**
 * 数据集大小指标趋势图
 * 显示文本块、问题和答案的数量随时间的变化
 */

// 1. 创建虚拟时间序列数据生成函数
function generateDatasetMetricsData(days = 30) {
    const data = [];
    const now = new Date();
    
    // 设置初始值
    let textBlocks = Math.floor(Math.random() * 100) + 50; // 初始文本块数量在50-150之间
    let questions = Math.floor(Math.random() * 50) + 20;   // 初始问题数量在20-70之间
    let answers = Math.floor(Math.random() * 40) + 10;     // 初始答案数量在10-50之间
    
    // 生成过去days天的数据
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(now.getDate() - i);
        
        // 增加一些随机波动，但保持整体上升趋势
        textBlocks += Math.floor(Math.random() * 10) - 2; // 每天-2到+8的变化
        questions += Math.floor(Math.random() * 8) - 1;   // 每天-1到+7的变化
        answers += Math.floor(Math.random() * 6) - 1;     // 每天-1到+5的变化
        
        // 确保数值不会为负
        textBlocks = Math.max(textBlocks, 50);
        questions = Math.max(questions, 20);
        answers = Math.max(answers, 10);
        
        data.push({
            date: date.toISOString().split('T')[0], // 格式化为YYYY-MM-DD
            textBlocks: textBlocks,
            questions: questions,
            answers: answers
        });
    }
    
    return data;
}

// 2. 初始化ECharts折线图
function initDatasetMetricsTrendChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('dataset-metrics-trend');
    if (!chartContainer) {
        console.error('找不到图表容器：dataset-metrics-trend');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const metricsData = generateDatasetMetricsData();
    
    // 3. 配置图表选项
    const option = {
        title: {
            text: '数据集规模指标趋势 - 折线图 (line)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = params[0].axisValue + '<br/>';
                params.forEach(param => {
                    let color = param.color;
                    let name = param.seriesName;
                    let value = param.value;
                    result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
                    result += `${name}: ${value}<br/>`;
                });
                return result;
            }
        },
        legend: {
            data: ['文本块', '问题', '答案'],
            bottom: '0%'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: metricsData.map(item => item.date),
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    // 简化日期显示
                    return value.substring(5); // 只显示MM-DD部分
                }
            }
        },
        yAxis: {
            type: 'value',
            name: '数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '文本块',
                type: 'line',
                data: metricsData.map(item => item.textBlocks),
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: '#5470c6'
                }
            },
            {
                name: '问题',
                type: 'line',
                data: metricsData.map(item => item.questions),
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: '#91cc75'
                }
            },
            {
                name: '答案',
                type: 'line',
                data: metricsData.map(item => item.answers),
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 3
                },
                itemStyle: {
                    color: '#fac858'
                }
            }
        ]
    };
    
    // 显示加载状态
    showChartLoading('dataset-metrics-trend');
    
    // 渲染图表
    chart.setOption(option);
    
    // 应用统一主题
    applyChartTheme(chart);
    
    // 隐藏加载状态
    hideChartLoading('dataset-metrics-trend');
    
    // 注册图表实例以便响应式调整
    registerChart(chart);
    
    return chart;
}

// 在文档加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initDatasetMetricsTrendChart();
}); 