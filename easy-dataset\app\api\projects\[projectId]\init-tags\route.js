import { NextResponse } from 'next/server';
import { batchSaveTags, getTags } from '@/lib/db/tags';
import { DEFAULT_DOMAIN_TAGS } from '@/constant/index';

/**
 * 为项目初始化默认领域标签
 * 用于解决从SQLite迁移到MySQL后现有项目缺少默认标签的问题
 */
export async function POST(request, { params }) {
  try {
    const { projectId } = params;

    // 验证项目ID
    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    // 检查项目是否已有标签
    const existingTags = await getTags(projectId);
    
    if (existingTags && existingTags.length > 0) {
      return NextResponse.json({ 
        message: '项目已存在标签，无需初始化',
        tagCount: existingTags.length 
      }, { status: 200 });
    }

    // 初始化默认领域标签
    await batchSaveTags(projectId, DEFAULT_DOMAIN_TAGS);
    
    // 获取初始化后的标签数量
    const newTags = await getTags(projectId);
    
    console.log(`项目 ${projectId} 默认领域标签初始化成功，共创建 ${newTags.length} 个标签`);
    
    return NextResponse.json({ 
      success: true,
      message: '默认领域标签初始化成功',
      tagCount: newTags.length
    }, { status: 200 });
    
  } catch (error) {
    console.error(`项目 ${params.projectId} 默认领域标签初始化失败:`, error);
    return NextResponse.json({ 
      error: error.message || '默认领域标签初始化失败' 
    }, { status: 500 });
  }
}

/**
 * 强制重新初始化项目的默认领域标签
 * 会删除现有标签并重新创建默认标签
 */
export async function PUT(request, { params }) {
  try {
    const { projectId } = params;

    // 验证项目ID
    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    // 强制重新初始化默认领域标签（会删除现有标签）
    await batchSaveTags(projectId, DEFAULT_DOMAIN_TAGS);
    
    // 获取初始化后的标签数量
    const newTags = await getTags(projectId);
    
    console.log(`项目 ${projectId} 默认领域标签强制重新初始化成功，共创建 ${newTags.length} 个标签`);
    
    return NextResponse.json({ 
      success: true,
      message: '默认领域标签强制重新初始化成功',
      tagCount: newTags.length
    }, { status: 200 });
    
  } catch (error) {
    console.error(`项目 ${params.projectId} 默认领域标签强制重新初始化失败:`, error);
    return NextResponse.json({ 
      error: error.message || '默认领域标签强制重新初始化失败' 
    }, { status: 500 });
  }
}