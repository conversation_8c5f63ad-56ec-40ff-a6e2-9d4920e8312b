"use strict";(()=>{var e={};e.id=1551,e.ids=[1551],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},30653:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>j,patchFetch:()=>x,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>d,dynamic:()=>l});var s=r(49303),o=r(88716),i=r(60670),n=r(87070),p=r(4342);let l="force-dynamic";async function d(e,{params:t}){try{let{projectId:e}=t;if(!e)return n.NextResponse.json({error:"项目ID不能为空"},{status:400});let r=(await p.db.tags.findMany({where:{projectId:e},orderBy:[{parentId:"asc"},{id:"asc"}]})).map(e=>({...e,displayName:e.label}));return n.NextResponse.json(r)}catch(e){return console.error("获取蒸馏标签失败:",String(e)),n.NextResponse.json({error:e.message||"获取蒸馏标签失败"},{status:500})}}let u=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/projects/[projectId]/distill/tags/all/route",pathname:"/api/projects/[projectId]/distill/tags/all",filename:"route",bundlePath:"app/api/projects/[projectId]/distill/tags/all/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\[projectId]\\distill\\tags\\all\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:c,staticGenerationAsyncStorage:m,serverHooks:g}=u,j="/api/projects/[projectId]/distill/tags/all/route";function x(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},4342:(e,t,r)=>{r.d(t,{db:()=>s});var a=r(53524);let s=globalThis.prisma||new a.PrismaClient({log:["error"]})}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972],()=>r(30653));module.exports=a})();