import { useState, useEffect, useRef } from 'react';

/**
 * 布局稳定性管理Hook
 * 确保数据加载过程中布局不会发生跳跃
 */
export const useStableLayout = (initialData = null, loadingDelay = 300) => {
  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isStable, setIsStable] = useState(false);
  
  // 用于防止快速切换导致的布局闪烁
  const stableTimeoutRef = useRef(null);
  const loadingTimeoutRef = useRef(null);

  /**
   * 设置加载状态
   * @param {boolean} isLoading - 是否正在加载
   */
  const setLoadingState = (isLoading) => {
    if (isLoading) {
      // 延迟显示加载状态，避免快速请求导致的闪烁
      loadingTimeoutRef.current = setTimeout(() => {
        setLoading(true);
        setIsStable(false);
      }, loadingDelay);
    } else {
      // 清除延迟加载的定时器
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      
      setLoading(false);
      
      // 延迟设置稳定状态，确保DOM更新完成
      stableTimeoutRef.current = setTimeout(() => {
        setIsStable(true);
      }, 100);
    }
  };

  /**
   * 更新数据
   * @param {any} newData - 新数据
   * @param {boolean} immediate - 是否立即更新（跳过稳定性检查）
   */
  const updateData = (newData, immediate = false) => {
    if (immediate) {
      setData(newData);
      setIsStable(true);
    } else {
      // 先设置为不稳定状态
      setIsStable(false);
      
      // 延迟更新数据，确保布局稳定
      setTimeout(() => {
        setData(newData);
        setIsStable(true);
      }, 50);
    }
  };

  /**
   * 设置错误状态
   * @param {Error|string} err - 错误信息
   */
  const setErrorState = (err) => {
    setError(err);
    setLoading(false);
    setIsStable(true);
  };

  /**
   * 重置所有状态
   */
  const reset = () => {
    setData(initialData);
    setLoading(false);
    setError(null);
    setIsStable(false);
    
    // 清除所有定时器
    if (stableTimeoutRef.current) {
      clearTimeout(stableTimeoutRef.current);
      stableTimeoutRef.current = null;
    }
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (stableTimeoutRef.current) {
        clearTimeout(stableTimeoutRef.current);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    isStable,
    setLoadingState,
    updateData,
    setErrorState,
    reset,
    
    // 便捷方法
    isReady: !loading && !error && isStable,
    isEmpty: !loading && !error && !data,
    hasError: !!error,
  };
};

/**
 * 骨架屏数据生成器
 * 为不同类型的数据生成占位数据
 */
export const createSkeletonData = {
  /**
   * 生成列表骨架数据
   * @param {number} count - 项目数量
   * @param {object} template - 数据模板
   */
  list: (count = 5, template = {}) => {
    return Array.from({ length: count }, (_, index) => ({
      id: `skeleton-${index}`,
      ...template,
      _isSkeleton: true,
    }));
  },

  /**
   * 生成图表骨架数据
   * @param {number} dataPoints - 数据点数量
   */
  chart: (dataPoints = 6) => {
    return {
      data: Array(dataPoints).fill(0),
      labels: Array.from({ length: dataPoints }, (_, i) => `加载中${i + 1}`),
      _isSkeleton: true,
    };
  },

  /**
   * 生成统计数据骨架
   * @param {number} count - 统计项数量
   */
  stats: (count = 3) => {
    return Array.from({ length: count }, (_, index) => ({
      title: '数据加载中',
      value: '--',
      unit: '',
      _isSkeleton: true,
    }));
  },

  /**
   * 生成表格骨架数据
   * @param {number} rows - 行数
   * @param {number} cols - 列数
   */
  table: (rows = 5, cols = 3) => {
    return {
      header: Array.from({ length: cols }, (_, i) => `列${i + 1}`),
      data: Array.from({ length: rows }, (_, rowIndex) =>
        Array.from({ length: cols }, (_, colIndex) => `加载中...`)
      ),
      _isSkeleton: true,
    };
  },
};

/**
 * 布局稳定性CSS类名生成器
 */
export const getStableClassNames = (loading, isStable, hasError) => {
  const classes = ['layout-stable'];
  
  if (loading) {
    classes.push('data-loading');
  } else if (isStable) {
    classes.push('data-loaded');
  }
  
  if (hasError) {
    classes.push('data-error');
  }
  
  return classes.join(' ');
};

export default useStableLayout;
