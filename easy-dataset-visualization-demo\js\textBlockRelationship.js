/**
 * 文本块关系热图
 * 可视化文本块之间的关系（相似度、引用关系等）
 */

// 生成虚拟的文本块关系数据
function generateTextBlockRelationshipData() {
  // 定义文本块主题类别
  const categories = [
    '技术文档', '科学研究', '教育资源', 
    '新闻报道', '产品说明', '学术论文'
  ];
  
  // 为每个类别生成几个文本块
  const textBlocks = [];
  let id = 1;
  
  categories.forEach(category => {
    // 每个类别生成3-5个文本块
    const count = Math.floor(Math.random() * 3) + 3;
    
    for (let i = 0; i < count; i++) {
      textBlocks.push({
        id: id,
        name: `${category}-${i+1}`,
        category: category
      });
      id++;
    }
  });
  
  // 生成文本块之间的关系矩阵（相似度）
  const relationshipMatrix = [];
  
  // 遍历所有文本块对
  for (let i = 0; i < textBlocks.length; i++) {
    for (let j = 0; j < textBlocks.length; j++) {
      // 相同文本块的相似度为1
      if (i === j) {
        relationshipMatrix.push([i, j, 1]);
        continue;
      }
      
      // 相同类别的文本块有较高的相似度
      if (textBlocks[i].category === textBlocks[j].category) {
        // 在0.6-0.9之间随机生成相似度
        const similarity = 0.6 + Math.random() * 0.3;
        relationshipMatrix.push([i, j, parseFloat(similarity.toFixed(2))]);
      } else {
        // 不同类别的文本块有较低的相似度
        // 在0.1-0.5之间随机生成相似度
        const similarity = 0.1 + Math.random() * 0.4;
        relationshipMatrix.push([i, j, parseFloat(similarity.toFixed(2))]);
      }
    }
  }
  
  return {
    textBlocks: textBlocks,
    relationshipMatrix: relationshipMatrix
  };
}

// 初始化文本块关系热图
function initTextRelationshipHeatmap() {
  const { textBlocks, relationshipMatrix } = generateTextBlockRelationshipData();
  
  // 获取图表容器
  const chartDom = document.getElementById('text-relationship-heatmap');
  const chart = echarts.init(chartDom);
  
  // 准备数据
  const textBlockNames = textBlocks.map(block => block.name);
  
  // 配置图表选项
  const option = {
    title: {
      text: '文本块相似度关系 - 热力图 (heatmap)',
      left: 'center'
    },
    tooltip: {
      position: 'top',
      formatter: function(params) {
        const i = params.data[0];
        const j = params.data[1];
        const similarity = params.data[2];
        return `${textBlocks[i].name} 与 ${textBlocks[j].name}<br/>相似度: ${similarity}`;
      }
    },
    grid: {
      top: '15%',
      bottom: '15%',
      left: '15%',
      right: '5%'
    },
    xAxis: {
      type: 'category',
      data: textBlockNames,
      splitArea: {
        show: true
      },
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'category',
      data: textBlockNames,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: 0,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%',
      inRange: {
        color: [
          '#e0f5ff', // 浅蓝色（低相似度）
          '#97d3fb', // 中等蓝色
          '#51b4ff', // 深蓝色
          '#1170fb'  // 非常深的蓝色（高相似度）
        ]
      }
    },
    series: [{
      name: '文本块相似度',
      type: 'heatmap',
      data: relationshipMatrix,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化文本块关系弦图
function initTextRelationshipChordDiagram() {
  const { textBlocks, relationshipMatrix } = generateTextBlockRelationshipData();
  
  // 获取图表容器
  const chartDom = document.getElementById('text-relationship-chord');
  const chart = echarts.init(chartDom);
  
  // 准备数据
  const nodes = textBlocks.map(block => ({
    name: block.name,
    category: block.category
  }));
  
  // 准备边数据，只保留相似度大于0.5的关系
  const links = [];
  relationshipMatrix.forEach(item => {
    const source = item[0];
    const target = item[1];
    const value = item[2];
    
    // 避免自环和低相似度的关系
    if (source !== target && value > 0.5) {
      links.push({
        source: textBlocks[source].name,
        target: textBlocks[target].name,
        value: value
      });
    }
  });
  
  // 获取所有类别
  const categories = [...new Set(textBlocks.map(block => block.category))].map(cat => ({
    name: cat
  }));
  
  // 配置图表选项
  const option = {
    title: {
      text: '文本块关系 - 力导向图 (graph)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        if (params.dataType === 'edge') {
          return `${params.data.source} 与 ${params.data.target}<br/>相似度: ${params.data.value}`;
        } else {
          return params.data.name;
        }
      }
    },
    legend: {
      data: categories.map(cat => cat.name),
      bottom: 0
    },
    series: [{
      name: '文本块关系',
      type: 'graph',
      layout: 'force',
      data: nodes.map((node, index) => ({
        id: index.toString(),
        name: node.name,
        category: categories.findIndex(cat => cat.name === node.category),
        symbolSize: 30
      })),
      links: links.map(link => ({
        source: nodes.findIndex(node => node.name === link.source).toString(),
        target: nodes.findIndex(node => node.name === link.target).toString(),
        value: link.value,
        lineStyle: {
          width: link.value * 5,
          opacity: 0.7
        }
      })),
      categories: categories,
      roam: true,
      label: {
        show: true,
        position: 'right',
        formatter: '{b}'
      },
      force: {
        repulsion: 100,
        edgeLength: [50, 100]
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 10
        }
      }
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
  initTextRelationshipHeatmap();
  initTextRelationshipChordDiagram();
}); 