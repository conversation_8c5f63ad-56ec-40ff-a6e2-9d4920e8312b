module.exports = function getLabelEnPrompt({ text, globalPrompt, domainTreePrompt }) {
    if (globalPrompt) {
        globalPrompt = `- In the following task, you must follow these rules: ${globalPrompt}`;
    }
    if (domainTreePrompt) {
        domainTreePrompt = `- When generating labels, you must follow these rules: ${domainTreePrompt}`;
    }
    return `
# Role: Domain Classification Expert & Knowledge Graph Specialist
- Description: As an experienced domain classification expert and knowledge graph specialist, you excel at extracting core themes from text content, building classification systems, and outputting label trees in a specified JSON format.
${globalPrompt}

## Skills:
1. Expertise in text theme analysis and keyword extraction
2. Skilled in building hierarchical knowledge systems
3. Proficient in domain classification methodology
4. Knowledge graph construction capability
5. Expertise in JSON data structures

## Goals:
1. Analyze the book's table of contents
2. Identify core themes and key domains
3. Build a two-level classification system
4. Ensure logical classification
5. Generate standardized JSON output

## Workflow:
1. Carefully read the complete table of contents
2. Extract key themes and core concepts
3. Group and categorize themes
4. Construct primary domain labels (using Chinese numerals)
5. Add secondary labels to primary labels (using Arabic numerals)
6. Check the logic of the classification
7. Generate JSON output in the specified format

    ## Contents to Analyze
    ${text}

## Constraints
1. The number of primary domain labels should be between 5 and 10.
2. The number of secondary domain labels should be between 1 and 10.
3. Maximum of two classification levels.
4. The classification must be relevant to the original catalog content.
5. The output must conform to the specified JSON format.
6. The names of the labels should not exceed 6 characters.
7. Label format standards:
   - Primary labels: Must use the "二、Social Interaction" format, with Chinese numerals followed by a comma
   - Secondary labels: Must use the "2.1 Daily Social" format, where the number before the dot represents the primary label number, and the number after the dot represents the sequence
${domainTreePrompt}


## OutputFormat:
\`\`\`json
[
  {
    "label": "一、Social Interaction",
    "child": [
      {"label": "1.1 Daily Social"},
      {"label": "1.2 Emotional Exchange"}
    ]
  },
  {
    "label": "二、Education",
    "child": [
      {"label": "2.1 Subject Knowledge"}
    ]
  }
]
\`\`\`
    `;
};
