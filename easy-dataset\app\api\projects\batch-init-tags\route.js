import { NextResponse } from 'next/server';
import { getProjects } from '@/lib/db/projects';
import { batchSaveTags, getTags } from '@/lib/db/tags';
import { DEFAULT_DOMAIN_TAGS } from '@/constant/index';

/**
 * 批量为所有项目初始化默认领域标签
 * 用于解决从SQLite迁移到MySQL后所有现有项目缺少默认标签的问题
 */
export async function POST(request) {
  try {
    // 获取所有项目
    const projects = await getProjects();
    
    if (!projects || projects.length === 0) {
      return NextResponse.json({ 
        message: '没有找到任何项目' 
      }, { status: 200 });
    }

    const results = [];
    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    // 遍历所有项目
    for (const project of projects) {
      try {
        // 检查项目是否已有标签
        const existingTags = await getTags(project.id);
        
        if (existingTags && existingTags.length > 0) {
          results.push({
            projectId: project.id,
            projectName: project.name,
            status: 'skipped',
            message: '项目已存在标签，跳过初始化',
            tagCount: existingTags.length
          });
          skipCount++;
          continue;
        }

        // 初始化默认领域标签
        await batchSaveTags(project.id, DEFAULT_DOMAIN_TAGS);
        
        // 获取初始化后的标签数量
        const newTags = await getTags(project.id);
        
        results.push({
          projectId: project.id,
          projectName: project.name,
          status: 'success',
          message: '默认领域标签初始化成功',
          tagCount: newTags.length
        });
        successCount++;
        
        console.log(`项目 ${project.id} (${project.name}) 默认领域标签初始化成功`);
        
      } catch (error) {
        results.push({
          projectId: project.id,
          projectName: project.name,
          status: 'error',
          message: error.message || '初始化失败',
          error: error.message
        });
        errorCount++;
        
        console.error(`项目 ${project.id} (${project.name}) 默认领域标签初始化失败:`, error);
      }
    }
    
    return NextResponse.json({ 
      success: true,
      message: '批量初始化完成',
      summary: {
        totalProjects: projects.length,
        successCount,
        skipCount,
        errorCount
      },
      results
    }, { status: 200 });
    
  } catch (error) {
    console.error('批量初始化默认领域标签失败:', error);
    return NextResponse.json({ 
      error: error.message || '批量初始化默认领域标签失败' 
    }, { status: 500 });
  }
}

/**
 * 强制批量重新初始化所有项目的默认领域标签
 * 会删除所有项目的现有标签并重新创建默认标签
 */
export async function PUT(request) {
  try {
    // 获取所有项目
    const projects = await getProjects();
    
    if (!projects || projects.length === 0) {
      return NextResponse.json({ 
        message: '没有找到任何项目' 
      }, { status: 200 });
    }

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    // 遍历所有项目
    for (const project of projects) {
      try {
        // 强制重新初始化默认领域标签（会删除现有标签）
        await batchSaveTags(project.id, DEFAULT_DOMAIN_TAGS);
        
        // 获取初始化后的标签数量
        const newTags = await getTags(project.id);
        
        results.push({
          projectId: project.id,
          projectName: project.name,
          status: 'success',
          message: '默认领域标签强制重新初始化成功',
          tagCount: newTags.length
        });
        successCount++;
        
        console.log(`项目 ${project.id} (${project.name}) 默认领域标签强制重新初始化成功`);
        
      } catch (error) {
        results.push({
          projectId: project.id,
          projectName: project.name,
          status: 'error',
          message: error.message || '强制重新初始化失败',
          error: error.message
        });
        errorCount++;
        
        console.error(`项目 ${project.id} (${project.name}) 默认领域标签强制重新初始化失败:`, error);
      }
    }
    
    return NextResponse.json({ 
      success: true,
      message: '批量强制重新初始化完成',
      summary: {
        totalProjects: projects.length,
        successCount,
        errorCount
      },
      results
    }, { status: 200 });
    
  } catch (error) {
    console.error('批量强制重新初始化默认领域标签失败:', error);
    return NextResponse.json({ 
      error: error.message || '批量强制重新初始化默认领域标签失败' 
    }, { status: 500 });
  }
}