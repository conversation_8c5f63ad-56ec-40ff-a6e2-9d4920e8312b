/**
 * 图表响应式设计和样式一致性
 * 确保所有图表在窗口大小变化时能够正确调整大小
 */

// 存储所有图表实例的数组
const chartInstances = [];

// 初始化图表响应式功能
function initChartResponsive() {
  console.log('初始化图表响应式功能');

  // 收集页面上所有图表容器
  const chartContainers = document.querySelectorAll('.chart-container');

  // 为每个图表容器添加加载状态指示器
  chartContainers.forEach(container => {
    // 创建加载状态元素
    const loadingElement = document.createElement('div');
    loadingElement.className = 'chart-loading';
    loadingElement.style.display = 'none';

    const spinner = document.createElement('div');
    spinner.className = 'chart-loading-spinner';
    loadingElement.appendChild(spinner);

    // 添加到容器中
    container.style.position = 'relative';
    container.appendChild(loadingElement);
  });

  // 监听窗口大小变化事件
  window.addEventListener('resize', debounce(() => {
    // 重新调整所有图表大小
    resizeAllCharts();
  }, 250));

  // 初始化主题颜色变量
  initThemeColors();

  // 特殊处理地图容器
  const mapContainer = document.getElementById('asean-map');
  if (mapContainer) {
    // 确保地图容器有正确的尺寸
    mapContainer.style.width = '100%';
    mapContainer.style.height = '500px';
    mapContainer.style.position = 'relative';
  }
}

// 注册图表实例
function registerChart(chart) {
  if (chart && typeof chart.resize === 'function') {
    chartInstances.push(chart);

    // 立即调整大小以确保正确显示
    setTimeout(() => {
      chart.resize();
    }, 0);

    return true;
  }
  return false;
}

// 重新调整所有图表大小
function resizeAllCharts() {
  console.log(`重新调整 ${chartInstances.length} 个图表大小`);
  chartInstances.forEach(chart => {
    if (chart && typeof chart.resize === 'function') {
      chart.resize();
    }
  });

  // 同时检查底部可见性
  ensureBottomVisibility();
}

// 确保底部区域完全可见
function ensureBottomVisibility() {
  const contentWrapper = document.querySelector('.content-wrapper');
  const lastSection = document.querySelector('.dashboard-section:last-child');

  if (contentWrapper && lastSection) {
    const wrapperRect = contentWrapper.getBoundingClientRect();
    const lastSectionRect = lastSection.getBoundingClientRect();

    // 如果最后一个section的底部超出了可视区域
    if (lastSectionRect.bottom > wrapperRect.bottom) {
      const additionalPadding = lastSectionRect.bottom - wrapperRect.bottom + 20;
      const currentPadding = parseFloat(getComputedStyle(contentWrapper).paddingBottom) || 0;
      contentWrapper.style.paddingBottom = (currentPadding + additionalPadding) + 'px';
      console.log('调整底部padding以确保内容可见:', additionalPadding + 'px');
    }
  }
}

// 显示图表加载状态
function showChartLoading(chartId) {
  const container = document.getElementById(chartId);
  if (container) {
    const loadingElement = container.querySelector('.chart-loading');
    if (loadingElement) {
      loadingElement.style.display = 'flex';
    }
  }
}

// 隐藏图表加载状态
function hideChartLoading(chartId) {
  const container = document.getElementById(chartId);
  if (container) {
    const loadingElement = container.querySelector('.chart-loading');
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }
  }
}

// 初始化科技感主题颜色变量
function initThemeColors() {
  // 定义科技感主题颜色
  const colors = {
    primary: '#00a2ff',
    secondary: '#00ff9f',
    accent: '#ff6b35',
    warning: '#ffb347',
    error: '#ff4757',
    success: '#2ed573',
    info: '#00a2ff',
    background: '#0c1426',
    textPrimary: '#ffffff',
    textSecondary: '#bcdcff'
  };

  // 应用到CSS变量
  const root = document.documentElement;
  root.style.setProperty('--primary-color', colors.primary);
  root.style.setProperty('--secondary-color', colors.secondary);
  root.style.setProperty('--accent-color', colors.accent);
  root.style.setProperty('--warning-color', colors.warning);
  root.style.setProperty('--error-color', colors.error);
  root.style.setProperty('--success-color', colors.success);
  root.style.setProperty('--info-color', colors.info);
  root.style.setProperty('--background-color', colors.background);
  root.style.setProperty('--text-color', colors.textPrimary);
  root.style.setProperty('--text-secondary-color', colors.textSecondary);
}

// 应用科技感图表主题
function applyChartTheme(chart) {
  if (!chart) return;

  // 获取CSS变量值
  const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
  const secondaryColor = getComputedStyle(document.documentElement).getPropertyValue('--secondary-color').trim();

  // 应用科技感主题
  chart.setOption({
    color: [
      '#00a2ff',
      '#00ff9f',
      '#ff6b35',
      '#ffb347',
      '#2ed573',
      '#ff4757',
      '#9c88ff',
      '#ffa726'
    ],
    backgroundColor: 'transparent',
    textStyle: {
      fontFamily: 'Microsoft YaHei, Arial, sans-serif',
      color: '#bcdcff'
    },
    grid: {
      borderColor: 'rgba(0, 162, 255, 0.1)'
    },
    tooltip: {
      backgroundColor: 'rgba(12, 20, 38, 0.95)',
      borderColor: 'rgba(0, 162, 255, 0.3)',
      borderWidth: 1,
      textStyle: {
        color: '#ffffff'
      }
    }
  });
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
  // 确保地图容器有足够的高度
  const mapContainer = document.getElementById('asean-map');
  if (mapContainer) {
    const containerHeight = window.innerHeight * 0.6;
    mapContainer.style.height = `${Math.max(500, containerHeight)}px`;
  }

  // 初始化响应式功能
  initChartResponsive();

  // 监听图表容器可见性变化
  if ('IntersectionObserver' in window) {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // 当图表进入视口时，触发重新调整大小
          resizeAllCharts();
        }
      });
    });

    // 观察所有图表容器
    document.querySelectorAll('.chart-container').forEach(container => {
      observer.observe(container);
    });

    // 观察地图容器
    if (mapContainer) {
      observer.observe(mapContainer);
    }
  }

  // 监听比较模式切换
  const compareBtn = document.getElementById('compare-btn');
  if (compareBtn) {
    compareBtn.addEventListener('click', function () {
      // 延迟执行以确保比较面板已显示
      setTimeout(() => {
        resizeAllCharts();
      }, 600);
    });
  }

  // 监听比较按钮点击
  const compareCountriesBtn = document.getElementById('compare-countries-btn');
  if (compareCountriesBtn) {
    compareCountriesBtn.addEventListener('click', function () {
      // 延迟执行以确保比较图表已渲染
      setTimeout(() => {
        resizeAllCharts();
      }, 100);
    });
  }

  // 监听国家选择器变化
  const country1Selector = document.getElementById('country-1');
  const country2Selector = document.getElementById('country-2');

  if (country1Selector && country2Selector) {
    country1Selector.addEventListener('change', function () {
      // 延迟执行以确保比较图表已更新
      setTimeout(() => {
        resizeAllCharts();
      }, 100);
    });

    country2Selector.addEventListener('change', function () {
      // 延迟执行以确保比较图表已更新
      setTimeout(() => {
        resizeAllCharts();
      }, 100);
    });
  }

  // 监听数据类型和年份选择器变化
  const yearFilter = document.getElementById('year-filter');
  const dataTypeFilter = document.getElementById('data-type');

  if (yearFilter) {
    yearFilter.addEventListener('change', function () {
      setTimeout(() => {
        resizeAllCharts();
      }, 100);
    });
  }

  if (dataTypeFilter) {
    dataTypeFilter.addEventListener('change', function () {
      setTimeout(() => {
        resizeAllCharts();
      }, 100);
    });
  }

  // 初始触发一次resize，确保所有图表正确显示
  setTimeout(() => {
    resizeAllCharts();
  }, 500);

  // 延迟检查底部可见性，确保所有内容都已渲染
  setTimeout(() => {
    ensureBottomVisibility();

    // 如果有flexible.js，也调用其底部可见性检查
    if (window.lib && window.lib.flexible && window.lib.flexible.adjustBottomVisibility) {
      window.lib.flexible.adjustBottomVisibility();
    }
  }, 1000);
});