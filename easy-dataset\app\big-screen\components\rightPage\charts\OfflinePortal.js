import React, { PureComponent } from 'react';
import Chart from '../../../utils/chart';
import { OfflinePortalOptions } from './options';

class OfflinePortal extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      renderer: 'canvas',
    };
  }

  render() {
    const { renderer } = this.state;
    const { offlinePortalData } = this.props;

    // 检查是否有有效数据
    const hasValidData = offlinePortalData &&
      offlinePortalData.xData &&
      offlinePortalData.data1 &&
      (offlinePortalData.data1.some(val => val > 0) ||
        offlinePortalData.data2?.some(val => val > 0) ||
        offlinePortalData.data3?.some(val => val > 0) ||
        offlinePortalData.data4?.some(val => val > 0));

    return (
      <div
        style={{
          width: '5.375rem',
          height: '2.5rem', // 🔑 关键修复：进一步减少高度
        }}>
        {hasValidData ? (
          <Chart
            renderer={renderer}
            option={OfflinePortalOptions(offlinePortalData)}
          />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#BCDCFF',
            fontSize: '14px'
          }}>
            暂无数据
          </div>
        )}
      </div>
    );
  }
}

export default OfflinePortal;
