/**
 * 批量自动识别领域标签任务处理服务
 */

import { PrismaClient } from '@prisma/client';
import { updateTask } from './index';
import { createTag, getTags } from '@/lib/db/tags';
import { db } from '@/lib/db/index';
import { getProject } from '@/lib/db/projects';
import { updateDataset, batchUpdateDatasetLabels } from '@/lib/db/datasets';
import LLMClient from '@/lib/llm/core/index';
import { getAddLabelSimplePrompt } from '@/lib/llm/prompts/addLabelSimple';
import { extractTagFromText, hasErrorInResponse, parseLLMResponse } from '@/lib/util/llm-response-parser';
import { getActiveModel } from '@/lib/services/models';
import { findTagByDisplayName } from '@/lib/util/tag-display';

const prisma = new PrismaClient();

/**
 * 处理批量自动识别领域标签任务
 * @param {Object} task - 任务对象
 */
export async function processBatchAutoTagTask(task) {
  try {
    console.log(`开始处理批量自动识别领域标签任务: ${task.id}`);

    // 解析任务信息
    let taskNote;
    try {
      taskNote = JSON.parse(task.note);
    } catch (error) {
      throw new Error(`任务信息解析失败: ${error.message}`);
    }

    const { projectId, datasetIds } = taskNote;

    if (!projectId || !datasetIds || !Array.isArray(datasetIds) || datasetIds.length === 0) {
      throw new Error('任务参数无效：缺少项目ID或数据集ID列表');
    }

    // 更新任务状态为处理中
    await updateTask(task.id, {
      status: 0,
      totalCount: datasetIds.length,
      completedCount: 0,
      detail: '正在初始化...'
    });

    // 获取项目的领域标签
    let projectTags = await getTags(projectId);
    if (!projectTags || projectTags.length === 0) {
      throw new Error('项目没有领域标签，无法自动识别');
    }

    // 获取项目配置
    const projectConfig = await getProject(projectId);
    let domainTreePrompt = '';
    if (projectConfig && projectConfig.domainTreePrompt) {
      domainTreePrompt = projectConfig.domainTreePrompt;
    }

    // 获取要处理的数据集
    const datasets = await prisma.datasets.findMany({
      where: {
        id: { in: datasetIds },
        projectId: projectId
      },
      select: {
        id: true,
        question: true,
        questionLabel: true
      }
    });

    if (datasets.length === 0) {
      throw new Error('未找到要处理的数据集');
    }

    // 获取项目的默认模型配置
    const modelConfig = await getActiveModel(projectId);
    if (!modelConfig) {
      throw new Error('项目未配置默认模型，无法进行自动识别');
    }

    // 准备标签列表，包含层级关系信息
    const simplifiedTags = [];

    // 递归构建标签树，保留层级关系
    function buildTagTree(tags, parentId = null, path = '') {
      const result = [];

      // 找出当前层级的标签
      const currentLevelTags = tags.filter(tag => tag.parentId === parentId);

      for (const tag of currentLevelTags) {
        // 创建当前标签对象
        const currentPath = path ? `${path} > ${tag.label}` : tag.label;
        const tagObj = {
          id: tag.id,
          name: tag.label,
          parentId: tag.parentId,
          path: currentPath
        };

        // 递归处理子标签
        const children = buildTagTree(tags, tag.id, currentPath);
        if (children.length > 0) {
          tagObj.children = children;
        }

        result.push(tagObj);

        // 同时添加到扁平列表，便于LLM处理
        simplifiedTags.push(tagObj);
      }

      return result;
    }

    // 构建标签树
    let tagTree = buildTagTree(projectTags);

    // 创建LLM客户端
    const llmClient = new LLMClient(modelConfig);

    // 处理结果统计
    const results = {
      total: datasets.length,
      success: 0,
      failed: 0,
      errors: []
    };

    // 优化后的批量处理逻辑
    const batchSize = 10; // 批量处理大小
    const pendingUpdates = []; // 待更新的数据集标签
    const tagCache = new Map(); // 标签缓存，避免重复查找
    let needsTagRefresh = false; // 是否需要刷新标签列表

    // 分批处理数据集
    for (let batchStart = 0; batchStart < datasets.length; batchStart += batchSize) {
      const batchEnd = Math.min(batchStart + batchSize, datasets.length);
      const currentBatch = datasets.slice(batchStart, batchEnd);

      // 更新任务进度
      await updateTask(task.id, {
        completedCount: batchStart,
        detail: `正在处理第 ${batchStart + 1}-${batchEnd}/${datasets.length} 个数据集...`
      });

      // 并行处理当前批次的LLM请求
      const batchPromises = currentBatch.map(async (dataset, index) => {
        try {
          // 准备提示词
          const isReasoningModel = modelConfig.name && (modelConfig.name.toLowerCase().includes('o1') || modelConfig.name.toLowerCase().includes('reasoning'));
          const promptContent = getAddLabelSimplePrompt(
            JSON.stringify(tagTree, null, 2),
            dataset.question,
            domainTreePrompt,
            isReasoningModel
          );

          // 添加重试逻辑
          let retryCount = 0;
          let success = false;
          let response;

          while (retryCount < 3 && !success) {
            try {
              // 发送请求，设置较短的超时时间
              response = await Promise.race([
                llmClient.getResponse(promptContent),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('请求超时')), 30000) // 30秒超时
                )
              ]);
              success = true;
            } catch (retryError) {
              retryCount++;

              // 如果是最后一次重试失败，记录错误并跳过
              if (retryCount >= 3) {
                console.error(`跳过问题"${dataset.question.substring(0, 20)}..."，无法自动识别标签`);
                return {
                  datasetId: dataset.id,
                  success: false,
                  error: 'LLM调用失败'
                };
              }

              // 等待一段时间再重试
              await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000)); // 随机延迟避免并发冲突
            }
          }

          // 如果成功获取响应
          if (success && response) {
            let label = '';
            let reasoning = '';

            // 检查响应是否包含错误信息
            if (hasErrorInResponse(response)) {
              console.warn(`自动识别标签失败，使用默认标签: ${response}`);
              label = '未分类';
            } else {
              // 尝试解析JSON响应
              const parseResult = parseLLMResponse(response);

              if (parseResult.success) {
                const parentTag = parseResult.parentTag;
                const childTag = parseResult.childTag;
                reasoning = parseResult.reasoning;

                console.log(`LLM返回结果 - 父标签: ${parentTag}, 子标签: ${childTag}`);

                // 使用子标签作为最终标签，并传递父标签信息用于创建
                label = childTag || '未分类';
                // 将父标签信息传递给后续处理
                if (parentTag && childTag) {
                  label = { childTag, parentTag };
                }
              } else {
                console.warn('JSON解析失败，尝试从文本中提取标签:', parseResult.error);

                // 如果JSON解析失败，尝试从文本中提取
                label = extractTagFromText(response);
              }
            }

            return {
              datasetId: dataset.id,
              success: true,
              label: label,
              reasoning: reasoning
            };
          } else {
            return {
              datasetId: dataset.id,
              success: false,
              error: 'LLM调用失败',
              label: '未分类'
            };
          }
        } catch (error) {
          console.error(`处理数据集 ${dataset.id} 失败:`, error);
          return {
            datasetId: dataset.id,
            success: false,
            error: error.message,
            label: '未分类'
          };
        }
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);

      // 处理批次结果，准备标签更新
      for (const result of batchResults) {
        if (result.label) {
          try {
            // 使用缓存查找或创建标签
            let matchedLabel;
            const cacheKey = typeof result.label === 'object' ?
              `${result.label.parentTag}:${result.label.childTag}` :
              result.label;

            if (tagCache.has(cacheKey)) {
              matchedLabel = tagCache.get(cacheKey);
            } else {
              if (typeof result.label === 'object') {
                // 处理父子标签结构
                matchedLabel = await findOrCreateChildTag(projectId, result.label.parentTag, result.label.childTag);
              } else {
                // 处理普通标签
                matchedLabel = await findOrCreateTag(projectId, result.label);
              }
              tagCache.set(cacheKey, matchedLabel);

              // 如果创建了新标签，标记需要刷新
              needsTagRefresh = true;
            }

            // 添加到待更新列表
            pendingUpdates.push({
              id: result.datasetId,
              questionLabel: matchedLabel
            });

            if (result.success) {
              results.success++;
            } else {
              results.failed++;
              results.errors.push(`数据集 ${result.datasetId}: ${result.error}`);
            }
          } catch (error) {
            console.error(`处理标签失败: ${error.message}`);
            results.failed++;
            results.errors.push(`数据集 ${result.datasetId}: 处理标签失败 - ${error.message}`);
          }
        } else {
          results.failed++;
          results.errors.push(`数据集 ${result.datasetId}: 无法获取有效标签`);
        }
      }

      // 如果需要刷新标签列表，在批次处理完成后刷新一次
      if (needsTagRefresh) {
        try {
          console.log('刷新标签列表...');
          projectTags = await getTags(projectId);
          tagTree = buildTagTree(projectTags);
          simplifiedTags.length = 0;
          for (const tag of projectTags) {
            const tagObj = {
              id: tag.id,
              name: tag.label,
              parentId: tag.parentId
            };
            simplifiedTags.push(tagObj);
          }
          needsTagRefresh = false;
        } catch (refreshError) {
          console.error(`刷新标签列表失败: ${refreshError.message}`);
        }
      }
    }

    // 批量更新数据集标签
    if (pendingUpdates.length > 0) {
      try {
        await batchUpdateDatasetLabels(pendingUpdates);
      } catch (error) {
        console.error(`批量更新数据集标签失败: ${error.message}`);
        // 如果批量更新失败，尝试逐个更新
        for (const update of pendingUpdates) {
          try {
            const updateResult = await updateDatasetLabel(update.id, update.questionLabel);
            if (updateResult) {
              results.success++;
            } else {
              results.failed++;
              results.errors.push(`数据集 ${update.id}: 更新标签失败`);
            }
          } catch (individualError) {
            console.error(`更新数据集 ${update.id} 标签失败: ${individualError.message}`);
            results.failed++;
            results.errors.push(`数据集 ${update.id}: 更新标签失败 - ${individualError.message}`);
          }
        }
      }
    }

    // 任务完成
    const finalDetail = `处理完成：成功 ${results.success}/${results.total}，失败 ${results.failed}/${results.total}`;

    await updateTask(task.id, {
      status: 1, // 完成
      completedCount: datasets.length,
      detail: finalDetail,
      note: JSON.stringify({ ...taskNote, results }),
      endTime: new Date()
    });

    console.log(`批量自动识别领域标签任务完成: ${task.id}`);
    console.log(`处理结果: ${finalDetail}`);

  } catch (error) {
    console.error(`批量自动识别领域标签任务失败: ${task.id}`, error);

    await updateTask(task.id, {
      status: 2, // 失败
      detail: `任务失败: ${error.message}`,
      endTime: new Date()
    });

    throw error;
  }
}

/**
 * 更新数据集标签
 * @param {string} datasetId - 数据集ID
 * @param {string} label - 标签名称
 */
async function updateDatasetLabel(datasetId, label) {
  try {
    // 确保label是标签名称而不是ID
    if (!label || typeof label !== 'string') {
      console.error(`无效的标签值: ${label}`);
      return false;
    }

    // 首先获取数据集信息以获取关联的问题ID
    const db = new PrismaClient();
    const dataset = await db.datasets.findUnique({
      where: { id: datasetId },
      select: { questionId: true }
    });
    await db.$disconnect();

    // 使用数据库更新函数直接更新数据集
    await updateDataset({
      id: datasetId,
      questionLabel: label
    });

    // 同时更新关联的真实问题记录的标签
    if (dataset && dataset.questionId) {
      try {
        // 先检查问题记录是否存在
        const db2 = new PrismaClient();
        const existingQuestion = await db2.questions.findUnique({
          where: { id: dataset.questionId },
          select: { id: true }
        });
        await db2.$disconnect();

        if (existingQuestion) {
          const { updateQuestion } = await import('@/lib/db/questions');
          await updateQuestion(dataset.questionId, {
            label: label
          });
        }
      } catch (questionUpdateError) {
        // 不抛出错误，继续执行，因为数据集标签已经更新成功
      }
    }
    return true;
  } catch (error) {
    // 不抛出错误，避免中断整个批量处理过程
    return false;
  }
}

/**
 * 查找或创建标签（复用dataset-import.js中的逻辑）
 */
async function findOrCreateTag(projectId, labelName, suggestNewSubclass = null) {
  try {
    // 获取项目的所有标签
    const allTags = await getTags(projectId);

    // 去除前后空格
    const trimmedLabelName = labelName.trim();

    // 1. 基于标签名称进行精确匹配
    const exactMatch = findTagByDisplayName(trimmedLabelName, allTags);
    if (exactMatch) {
      return exactMatch.displayLabel || exactMatch.label;
    }

    // 2. 如果没有精确匹配，进行大小写不敏感的匹配
    const caseInsensitiveMatch = allTags.find(tag => {
      return tag.label.toLowerCase() === trimmedLabelName.toLowerCase();
    });
    if (caseInsensitiveMatch) {
      return caseInsensitiveMatch.displayLabel || caseInsensitiveMatch.label;
    }

    // 3. 提取纯名称
    // 处理可能的序号格式：去除中文序号前缀和数字序号前缀
    let cleanLabelName = trimmedLabelName;

    // 去除中文序号前缀
    const chineseMatch = cleanLabelName.match(/^[一二三四五六七八九十]+、(.+)$/);
    if (chineseMatch) {
      cleanLabelName = chineseMatch[1];
    }

    // 去除数字序号前缀
    const numberMatch = cleanLabelName.match(/^(\d+\.)*\d+\s+(.+)$/);
    if (numberMatch) {
      cleanLabelName = numberMatch[2];
    }

    // 如果清理后的名称与原名称不同，清理后再次查找
    if (cleanLabelName !== trimmedLabelName) {
      const cleanMatch = findTagByDisplayName(cleanLabelName, allTags);
      if (cleanMatch) {
        return cleanMatch.displayLabel || cleanMatch.label;
      }
    }

    // 4. 如果都没有匹配，创建新标签
    // 直接创建为独立标签
    try {
      await createTag(projectId, cleanLabelName, null);
      return cleanLabelName;
    } catch (error) {
      console.error(`创建标签失败: ${error.message}`);
      return '未分类';
    }
  } catch (error) {
    console.error(`查找或创建标签失败: ${error.message}`);
    // 出错时返回原始标签名称
    return labelName;
  }
}

/**
 * 查找或创建子标签（基于父子标签关系）
 */
async function findOrCreateChildTag(projectId, parentTagName, childTagName) {
  try {
    console.log(`处理父子标签 - 父标签: ${parentTagName}, 子标签: ${childTagName}`);

    // 获取项目的所有标签
    const allTags = await getTags(projectId);

    // 清理标签名称（去除可能的序号）
    let cleanParentName = parentTagName.trim();
    let cleanChildName = childTagName.trim();

    // 去除父标签的序号前缀
    const parentChineseMatch = cleanParentName.match(/^[一二三四五六七八九十]+、(.+)$/);
    if (parentChineseMatch) {
      cleanParentName = parentChineseMatch[1];
    }

    // 去除子标签的序号前缀
    const childNumberMatch = cleanChildName.match(/^(\d+\.)*\d+\s+(.+)$/);
    if (childNumberMatch) {
      cleanChildName = childNumberMatch[2];
    }

    console.log(`清理后的标签名称 - 父标签: ${cleanParentName}, 子标签: ${cleanChildName}`);

    // 1. 查找现有的父标签
    let parentTag = allTags.find(tag =>
      !tag.parentId && (
        tag.label === cleanParentName ||
        tag.label === parentTagName ||
        tag.displayName === cleanParentName
      )
    );

    // 2. 如果父标签不存在，创建父标签
    if (!parentTag) {
      console.log(`创建新的父标签: ${cleanParentName}`);
      const createdParent = await createTag(projectId, cleanParentName, null);

      // 直接使用创建的标签，避免重新查询
      parentTag = {
        id: createdParent.id,
        label: createdParent.label,
        parentId: null
      };
    }

    if (!parentTag) {
      console.error(`无法找到或创建父标签: ${cleanParentName}`);
      return cleanChildName;
    }

    console.log(`找到父标签: ${parentTag.label} (ID: ${parentTag.id})`);

    // 3. 查找现有的子标签
    const existingChild = allTags.find(tag =>
      tag.parentId === parentTag.id && (
        tag.label === cleanChildName ||
        tag.displayName === cleanChildName
      )
    );

    if (existingChild) {
      console.log(`找到现有子标签: ${existingChild.displayLabel || existingChild.label}`);
      return existingChild.displayLabel || existingChild.label;
    }

    // 4. 创建新的子标签
    console.log(`创建新的子标签: ${cleanChildName}，父标签ID: ${parentTag.id}`);
    const createdChild = await createTag(projectId, cleanChildName, parentTag.id);

    // 重新获取完整的标签树以获取正确的显示名称
    const updatedTags = await getTags(projectId);

    // 递归查找新创建的子标签
    function findChildInTree(tags, targetId) {
      for (const tag of tags) {
        if (tag.id === targetId) {
          return tag;
        }
        if (tag.child && tag.child.length > 0) {
          const found = findChildInTree(tag.child, targetId);
          if (found) return found;
        }
      }
      return null;
    }

    const newChild = findChildInTree(updatedTags, createdChild.id);
    if (newChild) {
      console.log(`成功创建子标签: ${newChild.displayLabel || newChild.label}`);
      return newChild.displayLabel || newChild.label;
    }

    return cleanChildName;

  } catch (error) {
    console.error(`查找或创建子标签失败: ${error.message}`);
    return childTagName;
  }
}
