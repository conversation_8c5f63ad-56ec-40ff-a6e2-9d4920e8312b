# Dockerfile.prod

FROM node:20-alpine AS pnpm-base
RUN npm install -g pnpm@9

FROM pnpm-base AS deps
WORKDIR /app

# 安装原生依赖
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    build-base \
    pixman-dev \
    pkgconfig

# 只复制依赖文件，利用Docker缓存
COPY package.json pnpm-lock.yaml .npmrc ./
RUN pnpm install --frozen-lockfile

FROM pnpm-base AS builder
WORKDIR /app

ARG TARGETPLATFORM

# 从deps阶段复制node_modules
COPY --from=deps /app/node_modules ./node_modules
COPY package.json pnpm-lock.yaml .npmrc ./

# 只复制构建需要的文件
COPY prisma ./prisma
COPY next.config.js ./
COPY jsconfig.json ./
COPY app ./app
COPY components ./components
COPY constant ./constant
COPY hooks ./hooks
COPY lib ./lib
COPY locales ./locales
COPY public ./public
COPY styles ./styles

# 配置Prisma并构建
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "Configuring for ARM64 platform"; \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-arm64-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-arm64-openssl-3.0.x" pnpm build:prod; \
    else \
        echo "Configuring for AMD64 platform (default)"; \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x" pnpm build:prod; \
    fi

# 清理开发依赖
RUN pnpm prune --prod

FROM pnpm-base AS runner
WORKDIR /app

# 只安装运行时需要的依赖
RUN apk add --no-cache \
    cairo \
    pango \
    jpeg \
    giflib \
    librsvg \
    pixman \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制必要文件
COPY package.json .env.production ./
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# 只复制electron目录如果存在
COPY --from=builder /app/electron ./electron 2>/dev/null || true

# 设置环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# 切换到非root用户
USER nextjs

EXPOSE 1717

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:1717/api/health || exit 1

CMD ["pnpm", "start:prod"]
