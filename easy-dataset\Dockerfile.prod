# Dockerfile.prod

FROM node:20-alpine AS pnpm-base
RUN npm install -g pnpm@9

FROM pnpm-base AS deps
WORKDIR /app

# Install system dependencies for native modules
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    build-base \
    pixman-dev \
    pkgconfig

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml .npmrc ./
RUN pnpm install --frozen-lockfile

FROM pnpm-base AS builder
WORKDIR /app

ARG TARGETPLATFORM

# Install system dependencies for building
RUN apk add --no-cache --virtual .build-deps \
    python3 \
    make \
    g++ \
    cairo-dev \
    pango-dev \
    jpeg-dev \
    giflib-dev \
    librsvg-dev \
    build-base \
    pixman-dev \
    pkgconfig

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY package.json pnpm-lock.yaml .npmrc ./

# Copy only necessary source files for building
COPY prisma ./prisma
COPY public ./public
COPY app ./app
COPY components ./components
COPY constant ./constant
COPY hooks ./hooks
COPY lib ./lib
COPY locales ./locales
COPY styles ./styles
COPY next.config.js jsconfig.json ./
COPY .env.production ./

RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        echo "Configuring for ARM64 platform"; \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-arm64-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-arm64-openssl-3.0.x" pnpm build:prod; \
    else \
        echo "Configuring for AMD64 platform (default)"; \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x" pnpm build:prod; \
    fi

RUN pnpm prune --prod

FROM pnpm-base AS runner
WORKDIR /app

RUN apk add --no-cache \
    cairo \
    pango \
    jpeg \
    giflib \
    librsvg \
    pixman

COPY package.json .env.production ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/electron ./electron
COPY --from=builder /app/prisma ./prisma

ENV NODE_ENV=production

EXPOSE 1717
CMD ["pnpm", "start:prod"]
