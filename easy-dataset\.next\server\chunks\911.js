"use strict";exports.id=911,exports.ids=[911],exports.modules={30383:(e,t,a)=>{a.d(t,{JH:()=>i,RM:()=>d,aG:()=>c,bu:()=>s,xB:()=>l});var r=a(24330);a(60166);var o=a(4342),n=a(42549);async function i(e){try{return await o.db.modelConfig.findMany({where:{projectId:e}})}catch(e){throw console.error("Failed to get modelConfig by projectId in database"),e}}async function l(e){try{return await o.db.modelConfig.createMany({data:e})}catch(e){throw console.error("Failed to create init modelConfig list in database"),e}}async function s(e){try{return await o.db.modelConfig.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get modelConfig by id in database"),e}}async function c(e){try{return await o.db.modelConfig.delete({where:{id:e}})}catch(e){throw console.error("Failed to delete modelConfig by id in database"),e}}async function d(e){try{return e.id||(e.id=(0,n.x0)(12)),await o.db.modelConfig.upsert({create:e,update:e,where:{id:e.id}})}catch(e){throw console.error("Failed to create modelConfig in database"),e}}(0,a(40618).h)([i,l,s,c,d]),(0,r.j)("5636be5c355e6e25cbd500af345c3685a2465d14",i),(0,r.j)("d561805e679cd84fa8a8fdccd34e62f30947517a",l),(0,r.j)("d11a2c8347d3e80cdce01ffccd9285bd18889165",s),(0,r.j)("7dc497640f9c6e242f0cb81326631dca91213137",c),(0,r.j)("8221e2dcd0cbdd116fbd8cbe4b51b14f573db705",d)},67696:e=>{e.exports=function({text:e,globalPrompt:t,domainTreePrompt:a}){return t&&(t=`- 在后续的任务中，你务必遵循这样的规则：${t}`),a&&(a=`- 在生成标签时，你务必遵循这样的规则：${a}`),`
# Role: 领域分类专家 & 知识图谱专家
- Description: 作为一名资深的领域分类专家和知识图谱专家，擅长从文本内容中提取核心主题，构建分类体系，并输出规定 JSON 格式的标签树。
${t}

## Skills:
1. 精通文本主题分析和关键词提取
2. 擅长构建分层知识体系
3. 熟练掌握领域分类方法论
4. 具备知识图谱构建能力
5. 精通JSON数据结构

## Goals:
1. 分析书籍目录内容
2. 识别核心主题和关键领域
3. 构建二级分类体系
4. 确保分类逻辑合理
5. 生成规范的JSON输出

## Workflow:
1. 仔细阅读完整的书籍目录内容
2. 提取关键主题和核心概念
3. 对主题进行分组和归类
4. 构建一级领域标签（使用中文数字编号）
5. 为一级标签添加二级标签（使用阿拉伯数字编号）
6. 检查分类逻辑的合理性
7. 生成符合格式的JSON输出

    ## 需要分析的目录
    ${e}

    ## 限制
1. 一级领域标签数量5-10个
2. 二级领域标签数量1-10个
3. 标签最多两层分类层级
4. 分类必须与原始目录内容相关
5. 输出必须符合指定 JSON 格式，不要输出 JSON 外其他任何不相关内容
6. 标签的名字最多不要超过 6 个字
7. 标签格式规范：
   - 一级标签格式：使用中文数字加顿号，如"二、社交互动"
   - 二级标签格式：使用阿拉伯数字（小数点前阿拉伯数字代表一级标签，小数点后代表顺序）加小数点，如"2.2 日常社交"
${a}

## OutputFormat:
\`\`\`json
[
  {
    "label": "一、社交互动",
    "child": [
      {"label": "1.1 日常社交"},
      {"label": "1.2 情感交流"}
    ]
  },
  {
    "label": "二、学习教育",
    "child": [
      {"label": "2.1 学科知识"}
    ]
  }
]
\`\`\`
    `}},69161:e=>{e.exports=function({text:e,globalPrompt:t,domainTreePrompt:a}){return t&&(t=`- In the following task, you must follow these rules: ${t}`),a&&(a=`- When generating labels, you must follow these rules: ${a}`),`
# Role: Domain Classification Expert & Knowledge Graph Specialist
- Description: As an experienced domain classification expert and knowledge graph specialist, you excel at extracting core themes from text content, building classification systems, and outputting label trees in a specified JSON format.
${t}

## Skills:
1. Expertise in text theme analysis and keyword extraction
2. Skilled in building hierarchical knowledge systems
3. Proficient in domain classification methodology
4. Knowledge graph construction capability
5. Expertise in JSON data structures

## Goals:
1. Analyze the book's table of contents
2. Identify core themes and key domains
3. Build a two-level classification system
4. Ensure logical classification
5. Generate standardized JSON output

## Workflow:
1. Carefully read the complete table of contents
2. Extract key themes and core concepts
3. Group and categorize themes
4. Construct primary domain labels (using Chinese numerals)
5. Add secondary labels to primary labels (using Arabic numerals)
6. Check the logic of the classification
7. Generate JSON output in the specified format

    ## Contents to Analyze
    ${e}

## Constraints
1. The number of primary domain labels should be between 5 and 10.
2. The number of secondary domain labels should be between 1 and 10.
3. Maximum of two classification levels.
4. The classification must be relevant to the original catalog content.
5. The output must conform to the specified JSON format.
6. The names of the labels should not exceed 6 characters.
7. Label format standards:
   - Primary labels: Must use the "二、Social Interaction" format, with Chinese numerals followed by a comma
   - Secondary labels: Must use the "2.1 Daily Social" format, where the number before the dot represents the primary label number, and the number after the dot represents the sequence
${a}


## OutputFormat:
\`\`\`json
[
  {
    "label": "一、Social Interaction",
    "child": [
      {"label": "1.1 Daily Social"},
      {"label": "1.2 Emotional Exchange"}
    ]
  },
  {
    "label": "二、Education",
    "child": [
      {"label": "2.1 Subject Knowledge"}
    ]
  }
]
\`\`\`
    `}},29365:e=>{e.exports=function({text:e,existingTags:t,deletedContent:a,newContent:r,globalPrompt:o,domainTreePrompt:n}){return`


## 现有领域树
${JSON.stringify(t,null,2)}

## 原始目录
${e}

## 删除的内容
${a||"无"}

## 新增的内容
${r||"无"}

## 要求
请分析上述信息，修订现有的领域树结构，遵循以下原则：
1. 保持领域树的总体结构稳定，避免大规模重构
2. 对于删除的内容相关的领域标签：
   - 如果某个标签仅与删除的内容相关，且在现有文献中找不到相应内容支持，则移除该标签
   - 如果某个标签同时与其他保留的内容相关，则保留该标签
3. 对于新增的内容：
   - 如果新内容可以归类到现有的标签中，优先使用现有标签
   - 如果新内容引入了现有标签体系中没有的新领域或概念，再创建新的标签
4. 每个标签必须对应目录结构中的实际内容，不要创建没有对应内容支持的空标签
5. 确保修订后的领域树仍然符合良好的层次结构，标签间具有合理的父子关系

## 限制
1. 一级领域标签数量5-10个
2. 二级领域标签数量1-10个
3. 标签最多两层分类层级
4. 分类必须与原始目录内容相关
5. 输出必须符合指定 JSON 格式，不要输出 JSON 外其他任何不相关内容
6. 标签的名字最多不要超过 6 个字
7. 标签格式规范：
   - 一级标签格式：使用中文数字加顿号，如"二、社交互动"
   - 二级标签格式：使用阿拉伯数字（小数点前阿拉伯数字代表一级标签，小数点后代表顺序）加小数点，如"2.2 日常社交"

## 输出格式
最终输出修订后的完整领域树结构，使用下面的JSON格式：

\`\`\`json
[
  {
    "label": "一、社交互动",
    "child": [
      {"label": "1.1 日常社交"},
      {"label": "1.2 情感交流"}
    ]
  },
  {
    "label": "二、学习教育",
    "child": [
      {"label": "2.1 学科知识"}
    ]
  }
]
\`\`\`

确保你的回答中只包含JSON格式的领域树，不要有其他解释性文字。`}},75809:e=>{e.exports=function({text:e,existingTags:t,deletedContent:a,newContent:r,globalPrompt:o,domainTreePrompt:n}){return`


## Existing Domain Tree
${JSON.stringify(t,null,2)}

## Original Table of Contents
${e}

## Deleted Content
${a||"None"}

## New Content
${r||"None"}

Please analyze the above information and revise the existing domain tree structure according to the following principles:
1. Maintain the overall structure of the domain tree, avoiding large-scale reconstruction
2. For domain tags related to deleted content:
   - If a tag is only related to the deleted content and no supporting content can be found in the existing literature, remove the tag
   - If a tag is also related to other retained content, keep the tag
3. For newly added content:
   - If new content can be classified into existing tags, prioritize using existing tags
   - If new content introduces new domains or concepts not present in the existing tag system, create new tags
4. Each tag must correspond to actual content in the table of contents, do not create empty tags without corresponding content support
5. Ensure that the revised domain tree still has a good hierarchical structure with reasonable parent-child relationships between tags

## Constraints
1. The number of primary domain labels should be between 5 and 10.
2. The number of secondary domain labels should be between 1 and 10.
3. Maximum of two classification levels.
4. The classification must be relevant to the original catalog content.
5. The output must conform to the specified JSON format.
6. The names of the labels should not exceed 6 characters.
7. Label format standards:
   - Primary labels: Must use the "二、Social Interaction" format, with Chinese numerals followed by a comma
   - Secondary labels: Must use the "2.1 Daily Social" format, where the number before the dot represents the primary label number, and the number after the dot represents the sequence

Output the complete revised domain tree structure using the JSON format below:

\`\`\`json
[
  {
    "label": "一、Social Interaction",
    "child": [
      {"label": "1.1 Daily Social"},
      {"label": "1.2 Emotional Exchange"}
    ]
  },
  {
    "label": "二、Education",
    "child": [
      {"label": "2.1 Subject Knowledge"}
    ]
  }
]
\`\`\`

Ensure that your answer only contains the domain tree in JSON format without any explanatory text.`}},98317:e=>{e.exports=function(){return`
    你是一个专业的文本结构化处理助手，擅长根据前缀规则和标题语义分析并优化Markdown文档的标题层级结构。请根据以下要求处理我提供的Markdown标题：
    ## 任务描述
    请根据markdown文章标题的实际含义，以及标题的前缀特征调整各级标题的正确层级关系，具体要求如下：
    1. 一般相同格式的前缀的标题是同级关系({title}代表实际的标题内容)：
        例如：
        纯数字前缀开头\`1 {title}\`， \` 2 {title}\` ，\` 3 {title}\`，\` 4 {title}\`，\` 5 {title}\`  ... 等
        罗马数字前缀开头的\`I {title}\`，\`II {title}\` ，\`III {title}\`，\`IV {title}\`，\`V {title}\` ... 等
        小数点分隔数组前缀开头 \`1.1 {title}\`, \`1.2 {title}\`, \`1.3 {title}\`.... \`2.1 {title}\`, \`2.2 {title}\` 等
    2. 将子标题正确嵌套到父标题下（如\`1.1 {title}\`应作为\`1 {title}\`的子标题）
    3. 剔除与文章内容无关的标题
    4. 保持输出标题内容与输入完全一致
    5. 确保内容无缺失
    6. 如果是中文文献，但有英文的文章题目，可以省略

    ## 输入输出格式
    - 输入：包含错误层级关系的markdown标题结构
    - 输出：修正后的标准markdown标题层级结构

    ## 处理原则
    1. 严格根据标题语义确定所属关系
    2. 仅调整层级不修改原标题文本
    3. 无关标题直接移除不保留占位
    4. 相同前缀规则的标题必须是同一级别，不能出现 一部分是 n级标题，一部分是其他级别的标题

    ## 输出要求
    请将修正后的完整标题结构放在代码块中返回，格式示例如下：
    
    期望输出：
        \`\`\`markdown
            
        \`\`\`

    请处理以下数据：
      `}},80726:e=>{e.exports=function(){return`
    You are a professional text structuring assistant specializing in analyzing and optimizing the hierarchical 
    structure of Markdown document titles based on prefix rules and semantic analysis. Please process the Markdown titles 
    I provide according to the following requirements:
    ## Task Description
    Adjust the correct hierarchical relationships of titles based on the actual meaning of the Markdown article titles and the prefix characteristics of the titles. The specific requirements are as follows:

    1. Titles with the same prefix format are generally at the same level ({title} represents the actual title content):
        For example:
        - Titles starting with pure number prefixes: \`1 {title}\`, \`2 {title}\`, \`3 {title}\`, \`4 {title}\`, \`5 {title}\`, etc.
        - Titles starting with Roman numeral prefixes: \`I {title}\`, \`II {title}\`, \`III {title}\`, \`IV {title}\`, \`V {title}\`, etc.
        - Titles starting with decimal-separated array prefixes: \`1.1 {title}\`, \`1.2 {title}\`, \`1.3 {title}\`, ..., \`2.1 {title}\`, \`2.2 {title}\`, etc.

    2. Correctly nest sub-titles under parent titles (e.g., \`1.1 {title}\` should be a sub-title of \`1 {title}\`).
    3. Remove titles unrelated to the content of the article.
    4. Keep the content of the output titles identical to the input.
    5. Ensure no content is missing.
    6. For Chinese literature with English article titles, the English titles can be omitted.

    ## Input and Output Format
    - Input: Markdown title structure with incorrect hierarchical relationships.
    - Output: Corrected standard Markdown title hierarchical structure.

    ## Processing Principles
    1. Strictly determine the hierarchical relationship based on the semantic meaning of the titles.
    2. Adjust only the hierarchy without modifying the original title text.
    3. Directly remove unrelated titles without retaining placeholders.
    4. Titles with the same prefix rules must be at the same level; they cannot be partially at one level and partially at another.

    ## Output Requirements
    Please return the corrected complete title structure within a code block, formatted as follows:

    Expected Output:
        \`\`\`markdown
            
        \`\`\`

    Please process the following data:
      `}},23018:e=>{e.exports=function(){return`
    使用markdown语法，将图片中识别到的文字转换为markdown格式输出。你必须做到：
          1. 输出和使用识别到的图片的相同的语言，例如，识别到英语的字段，输出的内容必须是英语。
          2. 不要解释和输出无关的文字，直接输出图片中的内容。
          3. 内容不要包含在\`\`\`markdown \`\`\`中、段落公式使用 $$ $$ 的形式、行内公式使用 $ $ 的形式。
          4. 忽略掉页眉页脚里的内容
          5. 请不要对图片的标题进行markdown的格式化，直接以文本形式输出到内容中。
          6. 有可能每页都会出现期刊名称，论文名称，会议名称或者书籍名称，请忽略他们不要识别成标题
          7. 请精确分析当前PDF页面的文本结构和视觉布局，按以下要求处理：
            1. 识别所有标题文本，并判断其层级（根据字体大小、加粗、位置等视觉特征）
            2. 输出为带层级的Markdown格式，严格使用以下规则：
              - 一级标题：字体最大/顶部居中，前面加 # 
              - 二级标题：字体较大/左对齐加粗，有可能是数字开头也有可能是罗马数组开头，前面加 ## 
              - 三级标题：字体稍大/左对齐加粗，前面加 ### 
              - 正文文本：直接转换为普通段落
            3. 不确定层级的标题请标记[?]
            4. 如果是中文文献，但是有英文标题和摘要可以省略不输出
            示例输出：
            ## 4研究方法
            ### 4.1数据收集
            本文采用问卷调查...
      `}},40928:e=>{e.exports=function(){return`
    Use Markdown syntax to convert the text extracted from images into Markdown format and output it. You must adhere to the following requirements:
    1. Output in the same language as the text extracted from the image. For example, if the extracted text is in English, the output must also be in English.
    2. Do not explain or output any text unrelated to the content. Directly output the text from the image.
    3. Do not enclose the content within \`\`\`markdown \`\`\`. Use $$ $$ for block equations and $ $ for inline equations.
    4. Ignore content in headers and footers.
    5. Do not format the titles from images using Markdown; output them as plain text within the content.
    6. Journal names, paper titles, conference names, or book titles that may appear on each page should be ignored and not treated as headings.
    7. Precisely analyze the text structure and visual layout of the current PDF page, and process it as follows:
        1. Identify all heading texts and determine their hierarchy based on visual features such as font size, boldness, and position.
        2. Output the text in hierarchical Markdown format, strictly following these rules:
            - Level 1 headings: Largest font size, centered at the top, prefixed with #
            - Level 2 headings: Larger font size, left-aligned and bold, possibly starting with numbers or Roman numerals, prefixed with ##
            - Level 3 headings: Slightly larger font size, left-aligned and bold, prefixed with ###
            - Body text: Convert directly into regular paragraphs
        3. For headings with uncertain hierarchy, mark them with [?].
        4. For Chinese literature with English titles and abstracts, these can be omitted from the output.

    Example Output:
    ## 4 Research Methods
    ### 4.1 Data Collection
    This paper uses questionnaires...
      `}},94570:(e,t,a)=>{a.d(t,{V:()=>l,getActiveModel:()=>i});var r=a(30383),o=a(6142),n=a(7663);async function i(e=null){try{if(e){let t=await (0,o.getProject)(e);if(t&&t.defaultModelConfigId){let a=await (0,r.bu)(t.defaultModelConfigId);if(a)return n.Z.info(`Using default model for project ${e}: ${a.modelName}`),a}}return n.Z.warn("No active model found"),null}catch(e){return n.Z.error("Failed to get active model:",e),null}}async function l(e){try{if(!e)return n.Z.warn("No model ID provided"),null;let t=await (0,r.bu)(e);if(t)return n.Z.info(`Retrieved model: ${t.modelName}`),t;return n.Z.warn(`Model not found with ID: ${e}`),null}catch(e){return n.Z.error("Failed to get model by ID:",e),null}}},911:(e,t,a)=>{a.d(t,{Wv:()=>eN,xJ:()=>ev});var r=a(53524),o=a(49428);let n=async(e,t,a,r)=>{let o=[],n=new Set,i=[...e],l=0;for(;i.length>0||n.size>0;){for(;n.size<a&&i.length>0;){let a=t(i.shift()).then(t=>(n.delete(a),r&&r(++l,e.length),t));n.add(a),o.push(a)}n.size>0&&await Promise.race(n)}return Promise.all(o)};var i=a(6142),l=a(40249);let s=new r.PrismaClient;async function c(e){try{let t;console.log(`开始处理问题生成任务: ${e.id}`);try{t=JSON.parse(e.modelInfo)}catch(e){throw Error(`模型信息解析失败: ${e.message}`)}let a=await (0,i.getTaskConfig)(e.projectId),r=a?.concurrencyLimit||2,o=(await s.chunks.findMany({where:{projectId:e.projectId,NOT:{name:{contains:"Distilled Content"}}},include:{Questions:!0}})).filter(e=>0===e.Questions.length);if(0===o.length){console.log(`项目 ${e.projectId} 没有需要生成问题的文本块`),await ev(e.id,{status:1,completedCount:0,totalCount:0,note:"没有需要生成问题的文本块"});return}let c=o.length;await ev(e.id,{totalCount:c,detail:`待处理文本块数量: ${c}`});let d=0,u=0,f=0,h=0,g=async a=>{try{let r=await s.task.findUnique({where:{id:e.id}});if(2===r.status||3===r.status){h=r.status;return}let o=await l.ZP.generateQuestionsForChunkWithGA(e.projectId,a.id,{model:t,language:"zh-CN"===e.language?"中文":"en",enableGaExpansion:!0});return console.log(`文本块 ${a.id} 已生成 ${o.total||0} 个问题`),d++,f+=o.total||0,await ev(e.id,{completedCount:d+u,detail:`已处理: ${d+u}/${c}, 成功: ${d}, 失败: ${u}, 共生成问题: ${f}`}),{success:!0,chunkId:a.id,total:o.total||0}}catch(t){return console.error(`处理文本块 ${a.id} 出错:`,t),u++,await ev(e.id,{completedCount:d+u,detail:`已处理: ${d+u}/${c}, 成功: ${d}, 失败: ${u}, 共生成问题: ${f}`}),{success:!1,chunkId:a.id,error:t.message}}};if(await n(o,g,r,async(e,t)=>{console.log(`问题生成进度: ${e}/${t}`)}),!h){let t=u>0&&0===d?2:1;await ev(e.id,{status:t,detail:"",note:"",endTime:new Date})}console.log(`问题生成任务 ${e.id} 处理完成`)}catch(t){console.error(`问题生成任务处理失败: ${e.id}`,t),await ev(e.id,{status:2,detail:`处理失败: ${t.message}`,note:`处理失败: ${t.message}`})}}var d=a(30101);let u=new r.PrismaClient;async function f(e){try{let t;console.log(`开始处理答案生成任务: ${e.id}`);try{t=JSON.parse(e.modelInfo)}catch(e){throw Error(`模型信息解析失败: ${e.message}`)}let a=e.projectId;console.log(`开始处理项目 ${a} 的答案生成任务`);let r=await u.questions.findMany({where:{projectId:a,answered:!1}});if(0===r.length){await ev(e.id,{status:1,detail:"没有需要处理的问题",note:"",endTime:new Date});return}let o=(await (0,i.getTaskConfig)(a)).concurrencyLimit||3,l=r.length;await ev(e.id,{totalCount:l,detail:`待处理问题数量: ${l}`,note:""});let s=0,c=0,f=0,h=0,g=async a=>{try{let r=await u.task.findUnique({where:{id:e.id}});if(2===r.status||3===r.status){h=r.status;return}let o=await d.Z.generateDatasetForQuestion(e.projectId,a.id,{model:t,language:"zh-CN"===e.language?"中文":"en"});console.log(`问题 ${a.id} 已生成答案，数据集 ID: ${o.dataset.id}`),s++,f++;let n=`已处理: ${s+c}/${l}, 成功: ${s}, 失败: ${c}, 共生成数据集: ${f}`;return await ev(e.id,{completedCount:s+c,detail:n,note:n}),{success:!0,questionId:a.id,datasetId:o.dataset.id}}catch(r){console.error(`处理问题 ${a.id} 出错:`,r),c++;let t=`已处理: ${s+c}/${l}, 成功: ${s}, 失败: ${c}, 共生成数据集: ${f}`;return await ev(e.id,{completedCount:s+c,detail:t,note:t}),{success:!1,questionId:a.id,error:r.message}}};if(await n(r,g,o,async(e,t)=>{console.log(`答案生成进度: ${e}/${t}`)}),!h){let t=c>0&&0===s?2:1;await ev(e.id,{status:t,completedCount:s+c,detail:"",note:"",endTime:new Date})}console.log(`任务 ${e.id} 已完成`)}catch(t){console.error("处理答案生成任务出错:",t),await ev(e.id,{status:2,detail:`处理失败: ${t.message}`,note:`处理失败: ${t.message}`,endTime:new Date})}}var h=a(68928),g=a(30065),m=a(41121),p=a.n(m),w=a(75769),$=a(33965);async function y(e,t){console.log("executing default pdf conversion strategy......");try{let r;let o=await (0,w.getUploadFileInfoByFileName)(e,t);if(!o)throw Error(`PDF file ${t} not found in database`);let n=await (0,$.AG)(o.id),i=Buffer.isBuffer(n.content)?n.content:Buffer.from(n.content);try{if(r=await p()(i),/[��]/.test(r)||r.length<100){console.warn(`PDF转换结果可能包含乱码，尝试使用备用方案: ${t}`);let{parsePdf:e}=await a.e(5162).then(a.bind(a,15162)),o=await Promise.resolve().then(a.t.bind(a,92048,23)),n=await Promise.resolve().then(a.t.bind(a,19801,23)),l=await Promise.resolve().then(a.t.bind(a,55315,23)),s=l.join(n.tmpdir(),`pdf-convert-${Date.now()}`);await o.promises.mkdir(s,{recursive:!0});let c=l.join(s,t),d=l.join(s,t.replace(".pdf",".md"));try{await o.promises.writeFile(c,i),await e(c,{outputDir:s,outputFormat:"md"}),await o.promises.access(d).then(()=>!0).catch(()=>!1)?r=await o.promises.readFile(d,"utf-8"):console.warn(`备用方案也未能生成MD文件，使用原始结果: ${t}`)}finally{try{await o.promises.rm(s,{recursive:!0,force:!0})}catch(e){console.warn("清理临时文件失败:",e)}}}}catch(e){throw console.error(`PDF转换失败: ${t}`,e),e}let l=t.replace(/\.([^.]*)$/,"")+".md",s=Buffer.from(r,"utf-8"),c=await (0,$.iG)(e,l,s);return{success:!0,fileName:l,fileId:c.fileId,filePath:c.filePath}}catch(e){throw console.error("pdf conversion failed:",e),e}}var b=a(32615),I=a.n(b),C=a(35240),T=a.n(C),S=a(16347),k=a.n(S),x=a(67565),N=a(92048),v=a.n(N),E=a(55315),D=a.n(E),j=a(19801),F=a.n(j);let L="https://mineru.net/api/v4",O={DONE:"done",FAILED:"failed"};async function P(e,t,a={}){console.log("executing pdf mineru conversion strategy......");try{let r;let{updateTask:o,task:n,message:i}=a,l=n.completedCount,s=await (0,w.getUploadFileInfoByFileName)(e,t);if(!s)throw Error(`PDF file ${t} not found in database`);let c=await (0,$.AG)(s.id),d=D().join(F().tmpdir(),`mineru-${e}-${Date.now()}`);await v().promises.mkdir(d,{recursive:!0});let u=D().join(d,t),f=Buffer.isBuffer(c.content)?c.content:Buffer.from(c.content);await v().promises.writeFile(u,f);let h=await (0,x.getProjectRoot)(),g=D().join(h,e),m=D().join(g,"task-config.json");try{await v().promises.access(m);let e=await v().promises.readFile(m,"utf8");r=JSON.parse(e)}catch(e){throw console.error("error getting mineru token configuration:",e),Error("token configuration not found, please check if mineru token is configured in task settings")}let p=r?.minerUToken;if(null==p||""===p)throw Error("token configuration not found, please check if mineru token is configured in task settings");let y=JSON.stringify({enable_formula:!0,layout_model:"doclayout_yolo",enable_table:!0,files:[{name:t,is_ocr:!0,data_id:"abcd"}]});console.log("mineru getting file upload url...");let b=await q(`${L}/file-urls/batch`,{method:"POST",headers:{"Content-Type":"application/json","Content-Length":Buffer.byteLength(y),Authorization:`Bearer ${p}`},body:y});if(0!==b.code||!b.data?.file_urls?.[0])throw Error("failed to get file upload url: "+JSON.stringify(b));let I=null,C=null;console.log("mineru executing file upload task..."),0==b.code&&(C=b.data?.file_urls?.[0],I=b.data?.batch_id),await A(u,C),console.log("mineru file upload completed!"),console.log("mineru starting to check task progress...");let T=0,S=0;for(;;)try{let a=await q(`${L}/extract-results/batch/${I}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${p}`}}),r=a.data?.extract_result?.[0]?.state,s=a.data?.extract_result?.[0]?.extract_progress;if(s?(T=s.extracted_pages,S=s.total_pages):T=S,i.current.processedPage=T,i.stepInfo=`processing ${t} ${T}/${S} pages progress: ${T/S*100}%`,await o(n.id,{completedCount:T+l,detail:JSON.stringify(i)}),console.log(`mineru ${t} current progress: ${T}/${S}, status: ${r}`),0===a.code&&r===O.DONE){let r=a.data.extract_result[0].full_zip_url;await J(r,e,t);try{await v().promises.rm(d,{recursive:!0,force:!0})}catch(e){console.warn("Failed to cleanup temporary files:",e)}break}if(0!==a.code||r===O.FAILED)throw Error(`task processing failed: ${JSON.stringify(a)}`);await new Promise(e=>setTimeout(e,3e3))}catch(e){throw e}return console.log("mineru pdf conversion completed!"),{success:!0}}catch(e){throw console.error("mineru api call error:",e),e}}async function q(e,t){return new Promise((a,r)=>{let o=e.startsWith("https"),n=o?T():I(),i=new URL(e),l={hostname:i.hostname,port:i.port||(o?443:80),path:`${i.pathname}${i.search}`,method:t.method,headers:t.headers},s=n.request(l,e=>{let t="";e.on("data",e=>{t+=e}),e.on("end",()=>{try{e.statusCode>=200&&e.statusCode<300?a(JSON.parse(t)):r(Error(`request failed, status code: ${e.statusCode}, response: ${t}`))}catch(e){r(Error("failed to parse response"))}})});s.on("error",e=>{r(e)}),t.body&&s.write(t.body),s.end()})}async function A(e,t){return new Promise((a,r)=>{let o=t.startsWith("https"),n=new URL(t),i="https:"===n.protocol?T():I(),l=v().createReadStream(e),s={hostname:n.hostname,port:n.port||(o?443:80),path:`${n.pathname}${n.search}`,method:"PUT"},c=i.request(s,e=>{let t="";e.on("data",e=>{t+=e}),e.on("end",()=>{200===e.statusCode?a(t):r(Error(`Upload failed with status ${e.statusCode}: ${t}`))})});c.on("error",e=>{r(e)}),l.pipe(c)})}async function J(e,t,a){let r=await new Promise((t,a)=>{T().get(e,e=>{let r=[];e.on("data",e=>r.push(e)),e.on("end",()=>t(Buffer.concat(r))),e.on("error",a)})}),o=new(k())(r);for(let e of o.getEntries())if(e.entryName.toLowerCase().endsWith(".md")){let r=o.readFile(e).toString("utf8"),n=a.replace(".pdf",".md"),i=Buffer.from(r,"utf-8");await (0,$.iG)(t,n,i);break}}var M=a(23018),U=a.n(M),B=a(40928),z=a.n(B),R=a(98317),_=a.n(R),G=a(80726),V=a.n(G);async function W(e,t,r={}){try{let{updateTask:o,task:n,message:l}=r,s=n.completedCount;console.log("executing vision conversion strategy......");let c=await (0,w.getUploadFileInfoByFileName)(e,t);if(!c)throw Error(`PDF file ${t} not found in database`);let d=await (0,$.AG)(c.id),u=D().join(F().tmpdir(),`vision-${e}-${Date.now()}`);await v().promises.mkdir(u,{recursive:!0});let f=D().join(u,t),h=Buffer.isBuffer(d.content)?d.content:Buffer.from(d.content);await v().promises.writeFile(f,h);let g=await (0,i.getTaskConfig)(e),m=n.vsionModel;if(!m)throw Error("please check if pdf conversion vision model is configured");if("vision"!==m.type)throw Error(`${m.modelName}(${m.providerName}) this model is not a vision model, please check [model configuration]`);if(!m.apiKey)throw Error(`${m.modelName}(${m.providerName}) this model has no api key configured, please check [model configuration]`);let p="en"===n.language?z():U(),y="en"===n.language?V():_(),b=D().join(u,"output");await v().promises.mkdir(b,{recursive:!0});let I={pdfPath:f,outputDir:b,apiKey:m.apiKey,model:m.modelId,baseUrl:m.endpoint,useFullPage:!0,verbose:!1,concurrency:g.visionConcurrencyLimit,prompt:p(),textPrompt:y(),onProgress:async({current:e,total:a})=>{o&&n.id&&(l.current.processedPage=e,l.setpInfo=`processing ${t} ${e}/${a} pages progress: ${e/a*100}% `,await o(n.id,{completedCount:s+e,detail:JSON.stringify(l)}))}};console.log("vision strategy: starting pdf file processing");let{parsePdf:C}=await a.e(5162).then(a.bind(a,15162));await C(f,I);let T=t.replace(".pdf",".md"),S=D().join(b,T);if(await v().promises.access(S).then(()=>!0).catch(()=>!1)){let t=await v().promises.readFile(S,"utf-8"),a=Buffer.from(t,"utf-8");await (0,$.iG)(e,T,a)}else throw Error(`Generated MD file not found: ${S}`);try{await v().promises.rm(u,{recursive:!0,force:!0})}catch(e){console.warn("Failed to cleanup temporary files:",e)}return{success:!0}}catch(e){throw console.error("vision strategy processing error:",e),e}}async function Z(e,t){let r=0,{getPageNum:o}=await a.e(5162).then(a.bind(a,15162));for(let a of t)if(a.fileName.endsWith(".pdf"))try{let t=await (0,w.getUploadFileInfoByFileName)(e,a.fileName);if(!t){console.error(`PDF file ${a.fileName} not found in database`),a.pageCount=1,r+=1;continue}let n=await (0,$.AG)(t.id),i=D().join(F().tmpdir(),`pagecount-${e}-${Date.now()}`);await v().promises.mkdir(i,{recursive:!0});let l=D().join(i,a.fileName),s=Buffer.isBuffer(n.content)?n.content:Buffer.from(n.content);await v().promises.writeFile(l,s);let c=await o(l);r+=c,a.pageCount=c;try{await v().promises.rm(i,{recursive:!0,force:!0})}catch(e){console.warn("Failed to cleanup temporary files:",e)}}catch(e){console.error(`Failed to get page count for ${a.fileName}:`,e),a.pageCount=1,r+=1}else r+=1,a.pageCount=1;return console.log(`Total pages to process: ${r}`),r}async function K(e="default",t,a,r={}){switch(e.toLowerCase()){case"default":return await y(t,a,r);case"mineru":return await P(t,a,r);case"vision":return await W(t,a,r);default:throw Error(`unsupported PDF processing strategy: ${e}`)}}async function Q(e){let t={current:{fileName:"",processedPage:0,totalPage:0},stepInfo:"",processedFiles:0,totalFiles:0,errorList:[],finishedList:[]};try{console.log(`start processing file processing task: ${e.id}`);let a=JSON.parse(e.note),{projectId:r,fileList:n,strategy:l="default",vsionModel:s,domainTreeAction:c}=a;t.totalFiles=n.length;let d=await Z(r,n);t.stepInfo=`Total ${t.totalFiles} files to process, total ${d} pages`,await ev(e.id,{status:o.Cs.STATUS.PROCESSING,totalCount:d+1,detail:JSON.stringify(t),startTime:new Date});let u={totalChunks:0,chunks:[],toc:""},f=await (0,i.getProject)(r);for(let i of n)try{t.current.fileName=i.fileName,t.current.processedPage=1,t.current.totalPage=i.pageCount||1,await ev(e.id,{status:o.Cs.STATUS.PROCESSING,totalCount:d+1,detail:JSON.stringify(t),startTime:new Date});let n=i;if(i.fileName.endsWith(".pdf")){e.vsionModel=s;let o=await K(l,r,i.fileName,{...a.options,updateTask:ev,task:e,message:t});if(!o.success)throw Error(o.error||"File processing failed");let c=o.fileName,d=await (0,w.getUploadFileInfoByFileName)(r,c);if(!d)throw Error(`转换后的MD文件未找到: ${c}`);n={fileName:d.fileName,fileId:d.id}}let{toc:c,chunks:f,totalChunks:g}=await (0,h.splitProjectFile)(r,n);u.toc+=c,u.chunks.push(...f),u.totalChunks+=g,console.log(r,i.fileName,`${i.fileName} Text split completed`),t.finishedList.push(i),t.processedFiles++,await ev(e.id,{completedCount:e.completedCount+i.pageCount,detail:JSON.stringify(t),updateAt:new Date}),e.completedCount+=i.pageCount}catch(r){let a=`Processing file ${i.fileName} failed: ${r.message}`;t.errorList.push(a),console.error(a),await ev(e.id,{detail:JSON.stringify(t)})}console.log("domainTreeAction",c);try{if(!await (0,g.handleDomainTree)({projectId:r,newToc:u.toc,model:JSON.parse(e.modelInfo),language:e.language,action:c,fileList:n,project:f})&&"keep"!==c)return await (0,i.updateProject)(r,{...f}),NextResponse.json({error:"AI analysis failed, please check model configuration, delete file and retry!"},{status:400});console.log("File processing completed successfully"),t.stepInfo="File processing completed successfully",await ev(e.id,{completedCount:e.totalCount,status:o.Cs.STATUS.COMPLETED,detail:JSON.stringify(t)})}catch(a){console.error("processing failed:",a),t.stepInfo=`File processing failed: ${a.message}`,await ev(e.id,{status:o.Cs.STATUS.FAILED,completedCount:0,detail:JSON.stringify(t),endTime:new Date});return}console.log(`task ${e.id} finished`)}catch(a){console.error("pdf processing failed:",a),t.stepInfo=`File processing failed: ${String(a)}`,await ev(e.id,{status:o.Cs.STATUS.FAILED,detail:JSON.stringify(t),endTime:new Date})}}var Y=a(42549),H=a(55677),X=a(33137),ee=a(3800),et=a(1491),ea=a(3359),er=a(94928),eo=a.n(er);function en(e,t,a,r=!1){return[{role:"system",content:`你是一个专业的问题分类专家，擅长将问题归类到合适的领域标签。
请根据提供的领域标签体系，为问题选择最合适的子标签。

领域标签体系：
${a||""}

可用标签：
${e}

输出要求：
1. 必须返回有效的JSON格式
2. 必须选择最具体的子标签，不要返回父级标签
3. 如果现有标签中没有合适的，必须创建新的合适标签：
   - 对于有明确主题的问题，创建具体的子标签（如："日常社交"、"编程学习"）
   - 对于模糊或无意义的问题，创建"其他"分类下的子标签（如："无意义表达"、"模糊询问"）
   - 避免直接使用"未分类"，而是根据问题特征创建更具体的分类
   - 创建的标签名称不要包含序号，只使用纯标签名称
${r?"4. 由于你是推理模型，请在reasoning字段中提供详细的思维过程，说明为什么选择或创建该标签":"4. 在reasoning字段中简要说明选择或创建该标签的原因"}

输出JSON格式：
{
  "parentTag": "父级标签名称（如：社交互动）",
  "childTag": "子级标签名称（如：日常社交）",
  "reasoning": "${r?"详细的推理过程和思维链":"选择该标签的简要原因"}"
}`},{role:"user",content:`请为以下问题选择最合适的领域标签：

问题：${t}

请严格按照JSON格式返回结果，不要包含任何其他文本。`}]}function ei(e){try{let t;if("string"==typeof e){let a=e.trim();(a=a.replace(/<think>[\s\S]*?<\/think>\s*/g,"")).startsWith("```json")?a=a.replace(/^```json\s*/,"").replace(/\s*```$/,""):a.startsWith("```")&&(a=a.replace(/^```\s*/,"").replace(/\s*```$/,"")),a=a.trim(),t=JSON.parse(a)}else t=e;return{success:!0,data:t,parentTag:t.parentTag||"",childTag:t.childTag||"",reasoning:t.reasoning||""}}catch(e){return{success:!1,error:e.message,data:null,parentTag:"",childTag:"",reasoning:""}}}function el(e){if("string"!=typeof e)return"未分类";let t=e.match(/"childTag"\s*:\s*"([^"]+)"/);if(t)return t[1];for(let t of e.trim().split("\n")){let e=t.trim().match(/^(\d+)\.(\d+)\s+(.+)$/);if(e)return`${e[1]}.${e[2]} ${e[3].trim()}`}return"未分类"}function es(e){return"string"==typeof e&&(e.includes("模型调用失败")||e.includes("模型调用出错")||e.includes("无有效响应"))}var ec=a(26481),ed=a(50121);async function eu(e){try{let a;console.log(`开始处理数据集导入任务: ${e.id}`);try{a=JSON.parse(e.note)}catch(e){throw Error(`任务信息解析失败: ${e.message}`)}let{filePath:r,fileName:n,fileFormat:l,fileRecordId:s,importOptions:c}=a;if(!r||!v().existsSync(r))throw Error("导入文件不存在或已被删除");let{importDomainTag:d,importCot:u,importInstruction:f,autoDetectDomainTag:h,useCustomInstruction:g,customInstruction:m,formatType:p="alpaca",customFields:w={questionField:"question",answerField:"answer",cotField:"cot",includeLabels:!1,includeChunk:!1}}=c,$=[],y=[],b=[],I="";if(h)try{(b=await (0,ee.getTags)(e.projectId))&&0!==b.length||y.push("项目没有领域标签，无法自动识别领域标签");let t=await (0,i.getProject)(e.projectId);t&&t.domainTreePrompt&&(I=t.domainTreePrompt)}catch(e){y.push(`获取项目标签和配置失败: ${e.message}`)}let C=[];if("excel"===l){let t=v().readFileSync(r),a=H.ij(t,{type:"buffer"}),o=a.SheetNames[0],n=a.Sheets[o],i=H.P6.sheet_to_json(n,{header:1});if(i.length<=1)throw Error("Excel文件没有数据");let l=i.length-1;await ev(e.id,{totalCount:l,detail:`待处理数据行数: ${l}`});for(let t=1;t<i.length;t++){let a=i[t];if(a.length>=2&&a[0]&&a[1])try{let r="未分类",o=!1;h&&!d?(o=!0,r="待分类"):h&&d?a[2]&&""!==a[2].trim()?(r=a[2],o=!1):(o=!0,r="待分类"):(r=d&&!h&&a[2]&&""!==a[2].trim()?a[2]:"未分类",o=!1);let n="请回答以下问题";g&&m?n=m:f&&a.length>=5&&a[4]&&(n=a[4]);let i=(0,Y.x0)(12),l={id:i,projectId:e.projectId,name:`Excel导入数据-${t}`,fileId:"virtual",fileName:`Excel导入-${e.id}.xlsx`,content:a[0],summary:a[0].substring(0,200),size:a[0].length,createAt:new Date,updateAt:new Date};await (0,et.YS)([l]);let s=(0,Y.x0)(12),c={id:s,projectId:e.projectId,chunkId:i,question:a[0],answer:a[1],instruction:n,model:"Excel导入",label:r,cot:u&&a.length>=4?a[3]:"",confirmed:!0,createdAt:new Date,updatedAt:new Date};await (0,ea.n_)(e.projectId,[c],i);let p={id:(0,Y.x0)(12),projectId:e.projectId,question:a[0],answer:a[1],instruction:n,model:"Excel导入",questionLabel:r,cot:u&&a.length>=4?a[3]:"",chunkName:`Excel导入数据-${t}`,chunkContent:a[0],questionId:s,confirmed:!0};o&&C.push({id:p.id,question:p.question}),await (0,X.Ty)(p),$.push(p)}catch(e){y.push(`第${t+1}行导入失败: ${e.message}`)}else y.push(`第${t+1}行数据不完整，已跳过`);await ev(e.id,{completedCount:t,detail:`已处理: ${t}/${l}, 成功导入: ${$.length}`})}}else if("json"===l||"jsonl"===l){let t=v().readFileSync(r,"utf-8"),a=[];if("json"===l)try{let e=JSON.parse(t);a=Array.isArray(e)?e:[e]}catch(e){throw Error("JSON格式无效")}else try{a=t.split("\n").filter(e=>""!==e.trim()).map(e=>JSON.parse(e))}catch(e){throw Error("JSONL格式无效")}if(0===a.length)throw Error("文件没有数据");let o=a.length;await ev(e.id,{totalCount:o,detail:`待处理数据项数: ${o}`});for(let t=0;t<a.length;t++){let r,n,i,s,c;let f=a[t];if("alpaca"===p)(r=ef(f,["input"]))||(r=ef(f,["instruction"]),console.warn(`Alpaca数据项缺少input字段，使用instruction字段作为问题内容: ${JSON.stringify(f).substring(0,100)}...`)),n=ef(f,["output"]),i=ef(f,["instruction"])||ef(f,["system"]),s=ef(f,["domainTag","label","questionLabel","tag"]),c=ef(f,["cot","complexCOT","chainOfThought","thinking"]);else if("sharegpt"===p){if(f.messages&&Array.isArray(f.messages)){let e=f.messages.find(e=>"user"===e.role),t=f.messages.find(e=>"assistant"===e.role),a=f.messages.find(e=>"system"===e.role);r=e?.content,n=t?.content,i=a?.content}s=ef(f,["domainTag","label","questionLabel","tag"]),c=ef(f,["cot","complexCOT","chainOfThought","thinking"])}else"custom"===p&&(r=ef(f,[w.questionField]),n=ef(f,[w.answerField]),c=ef(f,[w.cotField]),s=ef(f,["domainTag","label","questionLabel","tag"]),w.includeLabels&&(s=s||ef(f,["labels"])),w.includeChunk&&ef(f,["chunk"]));if(r&&n)try{let a="未分类",o=!1;h&&!d?(o=!0,a="待分类"):h&&d?s&&""!==s.trim()?(a=s,o=!1):(o=!0,a="待分类"):(a=d&&!h&&s&&""!==s.trim()?s:"未分类",o=!1);let f="请回答以下问题";g&&m?f=m:i&&(f=i);let p=(0,Y.x0)(12),w={id:p,projectId:e.projectId,name:`导入数据-${t+1}`,fileId:"virtual",fileName:`${l.toUpperCase()}导入-${e.id}.${l}`,content:r,summary:r.substring(0,200),size:r.length,createAt:new Date,updateAt:new Date};await (0,et.YS)([w]);let y=(0,Y.x0)(12),b={id:y,projectId:e.projectId,chunkId:p,question:r,answer:n,instruction:f,model:`${l.toUpperCase()}导入`,label:a,cot:u&&c?c:"",confirmed:!0,createdAt:new Date,updatedAt:new Date};await (0,ea.n_)(e.projectId,[b],p);let I={id:(0,Y.x0)(12),projectId:e.projectId,question:r,answer:n,instruction:f,model:`${l.toUpperCase()}导入`,questionLabel:a,cot:u&&c?c:"",chunkName:`导入数据-${t+1}`,chunkContent:r,questionId:y,confirmed:!0};o&&C.push({id:I.id,question:I.question}),await (0,X.Ty)(I),$.push(I)}catch(e){y.push(`第${t+1}项导入失败: ${e.message}`)}else y.push(`第${t+1}项缺少必填字段(问题或答案)，已跳过`);await ev(e.id,{completedCount:t+1,detail:`已处理: ${t+1}/${o}, 成功导入: ${$.length}`})}}else if("csv"===l){let t=v().readFileSync(r,"utf-8").split("\n").map(e=>{let t=[],a=!1,r="";for(let o=0;o<e.length;o++){let n=e[o];'"'===n?a&&o+1<e.length&&'"'===e[o+1]?(r+='"',o++):a=!a:","!==n||a?r+=n:(t.push(r),r="")}return t.push(r),t}).filter(e=>""!==e.join("").trim());if(t.length<=1)throw Error("CSV文件没有数据");let a=t[0],o=a.findIndex(e=>e.toLowerCase().includes("question")||e.toLowerCase().includes("input")),n=a.findIndex(e=>e.toLowerCase().includes("instruction")),i=a.findIndex(e=>e.toLowerCase().includes("answer")||e.toLowerCase().includes("output")||e.toLowerCase().includes("response")),l=a.findIndex(e=>e.toLowerCase().includes("domain")||e.toLowerCase().includes("tag")||e.toLowerCase().includes("label")),s=a.findIndex(e=>e.toLowerCase().includes("cot")||e.toLowerCase().includes("chain")||e.toLowerCase().includes("think"));if(-1===o||-1===i)throw Error("CSV文件缺少必要的问题或答案列");let c=t.length-1;await ev(e.id,{totalCount:c,detail:`待处理数据行数: ${c}`});for(let a=1;a<t.length;a++){let r=t[a];if(r.length>Math.max(o,i)&&r[o]&&r[i])try{let t="未分类",c=!1,f=-1!==l&&r[l]&&""!==r[l].trim();h&&!d?(c=!0,t="待分类"):h&&d?f?(t=r[l],c=!1):(c=!0,t="待分类"):(t=d&&!h&&f?r[l]:"未分类",c=!1);let g=(0,Y.x0)(12),m={id:g,projectId:e.projectId,name:`CSV导入数据-${a}`,fileId:"virtual",fileName:`CSV导入-${e.id}.csv`,content:r[o],summary:r[o].substring(0,200),size:r[o].length,createAt:new Date,updateAt:new Date};await (0,et.YS)([m]);let p=(0,Y.x0)(12),w=-1!==n&&r[n]?r[n]:"请回答以下问题",y={id:p,projectId:e.projectId,chunkId:g,question:r[o],answer:r[i],instruction:w,model:"CSV导入",label:t,cot:u&&-1!==s&&r[s]?r[s]:"",confirmed:!0,createdAt:new Date,updatedAt:new Date};await (0,ea.n_)(e.projectId,[y],g);let b={id:(0,Y.x0)(12),projectId:e.projectId,question:r[o],answer:r[i],instruction:w,model:"CSV导入",questionLabel:t,cot:u&&-1!==s&&r[s]?r[s]:"",chunkName:`CSV导入数据-${a}`,chunkContent:r[o],questionId:p,confirmed:!0};c&&C.push({id:b.id,question:b.question}),await (0,X.Ty)(b),$.push(b)}catch(e){y.push(`第${a+1}行导入失败: ${e.message}`)}else y.push(`第${a+1}行数据不完整，已跳过`);await ev(e.id,{completedCount:a,detail:`已处理: ${a}/${c}, 成功导入: ${$.length}`})}}if(h&&C.length>0)try{if(b&&0!==b.length){let a;let r=0,o=!1;await ev(e.id,{detail:`数据导入完成，开始处理自动识别领域标签，共${C.length}个问题`}),console.log(`开始自动识别领域标签，共${C.length}个问题，项目标签数量: ${b.length}`);let n=[];function t(e,a=null,r=""){let o=[];for(let i of e.filter(e=>e.parentId===a)){let a=r?`${r} > ${i.label}`:i.label,l={id:i.id,name:i.label,parentId:i.parentId,path:a},s=t(e,i.id,a);s.length>0&&(l.children=s),o.push(l),n.push(l)}return o}let i=t(b);try{a=JSON.parse(e.modelInfo)}catch(e){throw Error(`模型信息解析失败: ${e.message}`)}if(!a)throw Error("未找到项目默认模型配置");let l=new(eo())(a);for(;r<C.length;){let n=C.slice(r,r+1);r+=n.length;try{for(let s of(await ev(e.id,{detail:`自动识别领域标签进度: ${Math.min(r,C.length)}/${C.length}`}),n))try{let n;let c=a.modelName&&(a.modelName.includes("o1")||a.modelName.includes("reasoning")||a.modelName.includes("think")),d=JSON.stringify(i,null,2),u=en(d,s.question,I||"",c),f=0,h=!1;for(;f<3&&!h;)try{console.log(`尝试自动识别领域标签，问题 ${r}/${C.length}，重试次数: ${f}`),n=await Promise.race([l.getResponse(u),new Promise((e,t)=>setTimeout(()=>t(Error("请求超时")),3e4))]),h=!0}catch(e){if(f++,console.error(`自动识别领域标签请求失败，重试 ${f}/3:`,e.message),f>=3){console.error(`跳过问题"${s.question.substring(0,20)}..."，无法自动识别标签`);break}await new Promise(e=>setTimeout(e,2e3))}if(h&&n){let a="",r="";if(es(n))console.warn(`自动识别标签失败，使用默认标签: ${n}`),r="未分类";else{let e=ei(n);e.success?(a=e.parentTag,r=e.childTag,e.reasoning,console.log(`LLM返回结果 - 父标签: ${a}, 子标签: ${r}`)):(console.error("解析LLM JSON响应失败:",e.error),console.log("原始响应:",n),r=el(n))}if(r)try{let a=await eg(e.projectId,r);if(await eh(s.id,a)&&(o=!0),a!==r)try{b=await (0,ee.getTags)(e.projectId),i=t(b)}catch(e){console.error(`刷新标签列表失败: ${e.message}`)}}catch(e){console.error(`更新问题标签失败: ${e.message}`)}else console.warn(`无法为问题"${s.question.substring(0,20)}..."获取有效标签，跳过`)}else console.warn(`没有获取到有效响应，无法为问题"${s.question.substring(0,20)}..."标记标签`);await new Promise(e=>setTimeout(e,1e3))}catch(e){console.error(`处理问题"${s.question.substring(0,20)}..."失败:`,e)}}catch(e){console.error("处理批量自动标记失败:",e),y.push(`处理批量自动标记失败: ${e.message}`)}}o||y.push("自动识别领域标签失败。导入的数据已保存，但保留了原始标签。")}else console.error("自动识别标签失败：项目没有定义领域标签"),y.push("项目没有领域标签，无法自动识别领域标签。请先在项目设置中添加领域标签。")}catch(e){console.error("自动识别领域标签失败:",e),y.push(`自动识别领域标签失败: ${e.message}。导入的数据已保存，但保留了原始标签。`)}try{v().existsSync(r)&&v().unlinkSync(r)}catch(e){console.error(`删除临时文件失败: ${e.message}`)}if(await ev(e.id,{status:1,detail:`成功导入${$.length}条数据集`,note:JSON.stringify({total:$.length,errors:y.length>0?y:[]}),endTime:new Date}),s)try{await (0,ec.v)(s,{status:o.dI.STATUS.SUCCESS,recordCount:$.length,description:`成功导入${$.length}条数据集记录`})}catch(e){console.error("更新文件记录失败:",e)}}catch(a){console.error(`数据集导入任务失败: ${e.id}`,a);let{fileRecordId:t}=JSON.parse(e.note||"{}");if(t)try{await (0,ec.v)(t,{status:o.dI.STATUS.FAILED,description:`导入失败: ${a.message}`})}catch(e){console.error("更新文件记录失败状态失败:",e)}await ev(e.id,{status:2,detail:`导入失败: ${a.message}`,endTime:new Date})}}function ef(e,t){for(let a of t)if(void 0!==e[a]&&null!==e[a])return e[a];if(e.messages&&Array.isArray(e.messages)){for(let a of e.messages)if("user"===a.role&&t.includes("question")||"assistant"===a.role&&t.includes("answer"))return a.content}return null}async function eh(e,t){try{if(!t||"string"!=typeof t)return console.error(`无效的标签值: ${t}`),!1;let a=new r.PrismaClient,o=await a.datasets.findUnique({where:{id:e},select:{questionId:!0}});if(await a.$disconnect(),await (0,X.do)({id:e,questionLabel:t}),o&&o.questionId)try{let e=new r.PrismaClient,a=await e.questions.findUnique({where:{id:o.questionId},select:{id:!0}});await e.$disconnect(),a&&await (0,ea.updateQuestion)(o.questionId,{label:t})}catch(e){}return!0}catch(e){return!1}}async function eg(e,t,a=null){try{let r=await (0,ee.getTags)(e),o=t.trim(),n=(0,ed.E)(o,r);if(n)return n.displayLabel||n.label;let i=r.find(e=>e.label.toLowerCase()===o.toLowerCase());if(i)return i.displayLabel||i.label;let l=o.match(/^(\d+)\.(\d+)\s+(.+)$/);if(l){let t=l[1],a=l[2],r=l[3];console.log(`检测到子标签格式: ${o}，父标签序号: ${t}，子标签序号: ${a}，标签名: ${r}`);let n=await ep(e,t);if(n){console.log(`找到现有父标签: ${n}`);let t=await ew(e,n,r);return console.log(`创建新的子标签: ${t}，父标签: ${n}`),t}}if(console.log(`未找到匹配的标签，将创建新标签: ${o}`),a&&"string"==typeof a&&""!==a.trim()){let t=a.trim(),n=r.find(e=>e.label===t);if(n)return console.log(`建议的子类标签已存在: ${n.label}`),n.label;let i=r.find(e=>e.label===o&&!e.parentId);if(!i)return await e$(e,o,t);try{return await (0,ee.createTag)(e,t,i.id),console.log(`已创建新的子类标签: ${t}，父类: ${i.label}`),t}catch(a){console.error(`创建子类标签失败: ${a.message}`);try{return await (0,ee.createTag)(e,t,null),console.log(`创建子类标签失败，已创建为独立标签: ${t}`),t}catch(e){return console.error(`创建独立标签也失败: ${e.message}`),"未分类"}}}if(o.includes("领域")||/^[一二三四五六七八九十]+、/.test(o)){let t=o.replace(/领域$/,"").replace(/^[一二三四五六七八九十]+、/,"").trim();return await e$(e,o,t)}try{return await (0,ee.createTag)(e,o,null),console.log(`已创建新的独立标签: ${o}`),o}catch(e){return console.error(`创建标签失败: ${e.message}`),"未分类"}}catch(e){return console.error(`查找或创建标签失败: ${e.message}`),t}}new r.PrismaClient;let em={1:"社交互动",2:"日常生活",3:"学习发展",4:"职业工作",5:"休闲娱乐",6:"健康医疗",7:"特殊需求"};async function ep(e,t){try{let a=await (0,ee.getTags)(e),r=em[t]||"其他",o=a.find(e=>{if(e.parentId)return!1;let a=e.label.match(/^([一二三四五六七八九十]+)、(.+)$/);if(a){let e=a[2].replace(/领域$/,"");return({一:"1",二:"2",三:"3",四:"4",五:"5",六:"6",七:"7",八:"8",九:"9",十:"10"})[a[1]]===t&&e===r}let o=e.label.match(/^(\d+)[、\.](.+)$/);if(o){let e=o[2].replace(/领域$/,"");return o[1]===t&&e===r}return e.label.replace(/领域$/,"")===r});if(o)return console.log(`找到现有父标签: ${o.label}`),o.label;let n=em[t]||"其他";return await (0,ee.createTag)(e,n,null),console.log(`创建新的父标签: ${n}`),n}catch(e){return console.error(`查找或创建父标签失败: ${e.message}`),"其他"}}async function ew(e,t,a){try{let r=await (0,ee.getTags)(e),o=r.find(e=>e.label===t&&!e.parentId);if(!o)return console.error(`未找到父标签: ${t}`),a;let n=a.replace(/^\d+\.\d+\s+/,"").trim(),i=r.find(e=>{if(e.parentId!==o.id)return!1;if(e.label===n)return!0;let t=e.label.match(/^\d+\.\d+\s+(.+)$/);return!!t&&t[1]===n});if(i)return console.log(`找到现有子标签: ${i.label}`),i.label;return await (0,ee.createTag)(e,n,o.id),console.log(`创建新的子标签: ${n}，父标签: ${t}`),n}catch(e){return console.error(`查找或创建子标签失败: ${e.message}`),a}}async function e$(e,t,a=null){try{var r,o,n,i;let s,c;let d=await (0,ee.getTags)(e);function l(e){if(!e)return"";let t=e.replace(/^[一二三四五六七八九十]+、\s*/,"");return(t=(t=(t=t.replace(/^\d+[、\.]\d*\s*/,"")).replace(/^\d+\s*/,"")).replace(/领域$/,"")).trim()}let u=l(t),f=d.find(e=>e.label===u&&!e.parentId);if(f)console.log(`已存在领域标签: ${u}，直接使用`),s=f.id;else{let t=await (0,ee.createTag)(e,u,null);console.log(`已创建新的领域标签: ${u}`),s=t.id}(c=a&&""!==a.trim()?l(a):`${u}相关`)===u&&(c=`${u}相关`);let h=c,g=(r=d,o=s,n=h,i=c,r.find(e=>e.parentId===o&&e.label===n)||r.find(e=>e.parentId===o&&e.label.replace(/^[一二三四五六七八九十]+、\s*/,"").replace(/^\d+[、\.]\d*\s*/,"").replace(/^\d+\s*/,"").trim()===i)||null);if(g)return console.log(`已存在子标签: ${g.label}，直接使用`),g.label;return await (0,ee.createTag)(e,h,s),console.log(`已创建子类标签: ${h}，父标签: ${u}`),h}catch(e){throw console.error(`创建新领域标签失败: ${e.message}`),e}}a(4342);var ey=a(94570);let eb=new r.PrismaClient;async function eI(e){try{let a;console.log(`开始处理批量自动识别领域标签任务: ${e.id}`);try{a=JSON.parse(e.note)}catch(e){throw Error(`任务信息解析失败: ${e.message}`)}let{projectId:r,datasetIds:o}=a;if(!r||!o||!Array.isArray(o)||0===o.length)throw Error("任务参数无效：缺少项目ID或数据集ID列表");await ev(e.id,{status:0,totalCount:o.length,completedCount:0,detail:"正在初始化..."});let n=await (0,ee.getTags)(r);if(!n||0===n.length)throw Error("项目没有领域标签，无法自动识别");let l=await (0,i.getProject)(r),s="";l&&l.domainTreePrompt&&(s=l.domainTreePrompt);let c=await eb.datasets.findMany({where:{id:{in:o},projectId:r},select:{id:!0,question:!0,questionLabel:!0}});if(0===c.length)throw Error("未找到要处理的数据集");let d=await (0,ey.getActiveModel)(r);if(!d)throw Error("项目未配置默认模型，无法进行自动识别");let u=[];function t(e,a=null,r=""){let o=[];for(let n of e.filter(e=>e.parentId===a)){let a=r?`${r} > ${n.label}`:n.label,i={id:n.id,name:n.label,parentId:n.parentId,path:a},l=t(e,n.id,a);l.length>0&&(i.children=l),o.push(i),u.push(i)}return o}let f=t(n),h=new(eo())(d),g={total:c.length,success:0,failed:0,errors:[]},m=[],p=new Map,w=!1;for(let a=0;a<c.length;a+=10){let o=Math.min(a+10,c.length),i=c.slice(a,o);await ev(e.id,{completedCount:a,detail:`正在处理第 ${a+1}-${o}/${c.length} 个数据集...`});let l=i.map(async(e,t)=>{try{let t;let a=d.name&&(d.name.toLowerCase().includes("o1")||d.name.toLowerCase().includes("reasoning")),r=en(JSON.stringify(f,null,2),e.question,s,a),o=0,n=!1;for(;o<3&&!n;)try{t=await Promise.race([h.getResponse(r),new Promise((e,t)=>setTimeout(()=>t(Error("请求超时")),3e4))]),n=!0}catch(t){if(++o>=3)return console.error(`跳过问题"${e.question.substring(0,20)}..."，无法自动识别标签`),{datasetId:e.id,success:!1,error:"LLM调用失败"};await new Promise(e=>setTimeout(e,1e3+2e3*Math.random()))}if(!n||!t)return{datasetId:e.id,success:!1,error:"LLM调用失败",label:"未分类"};{let a="",r="";if(es(t))console.warn(`自动识别标签失败，使用默认标签: ${t}`),a="未分类";else{let e=ei(t);if(e.success){let t=e.parentTag,o=e.childTag;r=e.reasoning,console.log(`LLM返回结果 - 父标签: ${t}, 子标签: ${o}`),a=o||"未分类",t&&o&&(a={childTag:o,parentTag:t})}else console.warn("JSON解析失败，尝试从文本中提取标签:",e.error),a=el(t)}return{datasetId:e.id,success:!0,label:a,reasoning:r}}}catch(t){return console.error(`处理数据集 ${e.id} 失败:`,t),{datasetId:e.id,success:!1,error:t.message,label:"未分类"}}});for(let e of(await Promise.all(l)))if(e.label)try{let t;let a="object"==typeof e.label?`${e.label.parentTag}:${e.label.childTag}`:e.label;p.has(a)?t=p.get(a):(t="object"==typeof e.label?await eS(r,e.label.parentTag,e.label.childTag):await eT(r,e.label),p.set(a,t),w=!0),m.push({id:e.datasetId,questionLabel:t}),e.success?g.success++:(g.failed++,g.errors.push(`数据集 ${e.datasetId}: ${e.error}`))}catch(t){console.error(`处理标签失败: ${t.message}`),g.failed++,g.errors.push(`数据集 ${e.datasetId}: 处理标签失败 - ${t.message}`)}else g.failed++,g.errors.push(`数据集 ${e.datasetId}: 无法获取有效标签`);if(w)try{for(let e of(console.log("刷新标签列表..."),n=await (0,ee.getTags)(r),f=t(n),u.length=0,n)){let t={id:e.id,name:e.label,parentId:e.parentId};u.push(t)}w=!1}catch(e){console.error(`刷新标签列表失败: ${e.message}`)}}if(m.length>0)try{await (0,X.jw)(m)}catch(e){for(let t of(console.error(`批量更新数据集标签失败: ${e.message}`),m))try{await eC(t.id,t.questionLabel)?g.success++:(g.failed++,g.errors.push(`数据集 ${t.id}: 更新标签失败`))}catch(e){console.error(`更新数据集 ${t.id} 标签失败: ${e.message}`),g.failed++,g.errors.push(`数据集 ${t.id}: 更新标签失败 - ${e.message}`)}}let $=`处理完成：成功 ${g.success}/${g.total}，失败 ${g.failed}/${g.total}`;await ev(e.id,{status:1,completedCount:c.length,detail:$,note:JSON.stringify({...a,results:g}),endTime:new Date}),console.log(`批量自动识别领域标签任务完成: ${e.id}`),console.log(`处理结果: ${$}`)}catch(t){throw console.error(`批量自动识别领域标签任务失败: ${e.id}`,t),await ev(e.id,{status:2,detail:`任务失败: ${t.message}`,endTime:new Date}),t}}async function eC(e,t){try{if(!t||"string"!=typeof t)return console.error(`无效的标签值: ${t}`),!1;let o=new r.PrismaClient,n=await o.datasets.findUnique({where:{id:e},select:{questionId:!0}});if(await o.$disconnect(),await (0,X.do)({id:e,questionLabel:t}),n&&n.questionId)try{let e=new r.PrismaClient,o=await e.questions.findUnique({where:{id:n.questionId},select:{id:!0}});if(await e.$disconnect(),o){let{updateQuestion:e}=await Promise.resolve().then(a.bind(a,3359));await e(n.questionId,{label:t})}}catch(e){}return!0}catch(e){return!1}}async function eT(e,t,a=null){try{let a=await (0,ee.getTags)(e),r=t.trim(),o=(0,ed.E)(r,a);if(o)return o.displayLabel||o.label;let n=a.find(e=>e.label.toLowerCase()===r.toLowerCase());if(n)return n.displayLabel||n.label;let i=r,l=i.match(/^[一二三四五六七八九十]+、(.+)$/);l&&(i=l[1]);let s=i.match(/^(\d+\.)*\d+\s+(.+)$/);if(s&&(i=s[2]),i!==r){let e=(0,ed.E)(i,a);if(e)return e.displayLabel||e.label}try{return await (0,ee.createTag)(e,i,null),i}catch(e){return console.error(`创建标签失败: ${e.message}`),"未分类"}}catch(e){return console.error(`查找或创建标签失败: ${e.message}`),t}}async function eS(e,t,a){try{console.log(`处理父子标签 - 父标签: ${t}, 子标签: ${a}`);let r=await (0,ee.getTags)(e),o=t.trim(),n=a.trim(),i=o.match(/^[一二三四五六七八九十]+、(.+)$/);i&&(o=i[1]);let l=n.match(/^(\d+\.)*\d+\s+(.+)$/);l&&(n=l[2]),console.log(`清理后的标签名称 - 父标签: ${o}, 子标签: ${n}`);let s=r.find(e=>!e.parentId&&(e.label===o||e.label===t||e.displayName===o));if(!s){console.log(`创建新的父标签: ${o}`);let t=await (0,ee.createTag)(e,o,null);s={id:t.id,label:t.label,parentId:null}}if(!s)return console.error(`无法找到或创建父标签: ${o}`),n;console.log(`找到父标签: ${s.label} (ID: ${s.id})`);let c=r.find(e=>e.parentId===s.id&&(e.label===n||e.displayName===n));if(c)return console.log(`找到现有子标签: ${c.displayLabel||c.label}`),c.displayLabel||c.label;console.log(`创建新的子标签: ${n}，父标签ID: ${s.id}`);let d=await (0,ee.createTag)(e,n,s.id),u=await (0,ee.getTags)(e),f=function e(t,a){for(let r of t){if(r.id===a)return r;if(r.child&&r.child.length>0){let t=e(r.child,a);if(t)return t}}return null}(u,d.id);if(f)return console.log(`成功创建子标签: ${f.displayLabel||f.label}`),f.displayLabel||f.label;return n}catch(e){return console.error(`查找或创建子标签失败: ${e.message}`),a}}let ek=new r.PrismaClient;(async function(){if(!process.env.INITED){process.env.INITED=!0;try{console.log("开始检查未完成任务...");let e=await ek.task.findMany({where:{status:0}});if(0===e.length){console.log("没有需要恢复的任务");return}for(let t of(console.log(`找到 ${e.length} 个未完成任务，开始恢复...`),e))try{switch(t.taskType){case"question-generation":c(t).catch(e=>{console.error(`恢复问题生成任务 ${t.id} 失败:`,e)});break;case"answer-generation":f(t).catch(e=>{console.error(`恢复答案生成任务 ${t.id} 失败:`,e)});break;default:console.warn(`Other Task: ${t.taskType}`),await ek.task.update({where:{id:t.id},data:{status:2,detail:`${t.taskType} Error`,note:`${t.taskType} Error`,endTime:new Date}})}}catch(e){console.error(`恢复任务 ${t.id} 失败:`,e)}console.log("任务恢复服务已启动，未完成任务将在后台继续处理")}catch(e){console.error("任务恢复服务出错:",e)}}})().catch(e=>{console.error("执行任务恢复失败:",e)});let ex=new r.PrismaClient;async function eN(e){try{let t=await ex.task.findUnique({where:{id:e}});if(!t){console.error(`任务不存在: ${e}`);return}if(t.status===o.Cs.STATUS.COMPLETED||t.status===o.Cs.STATUS.FAILED){console.log(`任务已处理完成，无需再次执行: ${e}`);return}switch(t.taskType){case"question-generation":await c(t);break;case"file-processing":await Q(t);break;case"answer-generation":await f(t);break;case"dataset-import":await eu(t);break;case"batch-auto-tag":await eI(t);break;case"data-distillation":console.log("数据蒸馏任务暂未实现"),await ev(e,{status:2,note:"数据蒸馏任务暂未实现"});break;default:console.error(`未知任务类型: ${t.taskType}`),await ev(e,{status:o.Cs.STATUS.FAILED,note:`未知任务类型: ${t.taskType}`})}}catch(t){console.error(`处理任务失败: ${e}`,String(t)),await ev(e,{status:o.Cs.STATUS.FAILED,note:`处理失败: ${t.message}`})}}async function ev(e,t){try{return await ex.task.update({where:{id:e},data:{...t,updateAt:new Date}})}catch(t){throw console.error(`更新任务失败: ${e}`,String(t)),t}}},30065:(e,t,a)=>{let r=a(94928),o=a(67696),n=a(69161),i=a(29365),l=a(75809),{getProjectTocs:s}=a(68928),{getTags:c,batchSaveTags:d,createTag:u}=a(3800),{extractJsonFromLLMOutput:f}=a(44779),{filterDomainTree:h}=a(18742);async function g({projectId:e,action:t="rebuild",allToc:a,newToc:u,model:g,language:p="中文",deleteToc:w=null,project:$}){if("keep"===t)return console.log(`[${e}] Using existing domain tree`),await c(e);let y="en"===p,{globalPrompt:b,domainTreePrompt:I}=$||{};try{let p,$,C;a||(a=await s(e));let T=new r(g);if("rebuild"===t)console.log(`[${e}] Rebuilding domain tree`),$=(y?n:o)({text:a,globalPrompt:b,domainTreePrompt:I}),console.log("rebuild",$),C=await T.getResponse($),p=f(C),console.log("rebuild tags",p);else if("revise"===t){console.log(`[${e}] Revising domain tree`);let t=await c(e);$=t&&0!==t.length?(y?l:i)({text:a,existingTags:h(t),newContent:u,deletedContent:w,globalPrompt:b,domainTreePrompt:I}):(y?n:o)({text:a,globalPrompt:b,domainTreePrompt:I}),C=await T.getResponse($),p=f(C)}return p&&p.length>0&&"keep"!==t?"rebuild"===t?await d(e,p):"revise"===t&&await m(e,p):p||"keep"===t||console.error(`[${e}] Failed to generate domain tree tags`),p}catch(t){throw console.error(`[${e}] Error handling domain tree: ${t.message}`),t}}async function m(e,t){try{let r=await c(e),o=new Map;async function a(t,r=null,n=""){for(let i of t){let t=n?`${n}/${i.label}`:i.label;if(o.has(t)){let e=o.get(t);i.child&&i.child.length>0&&await a(i.child,e.id,t)}else{console.log(`[${e}] Adding new tag: ${t}`);let o=await u(e,i.label,r);i.child&&i.child.length>0&&await a(i.child,o.id,t)}}}(function e(t,a=""){for(let r of t){let t=a?`${a}/${r.label}`:r.label;o.set(t,r),r.child&&r.child.length>0&&e(r.child,t)}})(r),await a(t),console.log(`[${e}] Selective tag update completed`)}catch(t){throw console.error(`[${e}] Error in selective tag update: ${t.message}`),t}}e.exports={handleDomainTree:g}},18742:(e,t,a)=>{a.r(t),a.d(t,{filterDomainTree:()=>function e(t=[]){for(let a=0;a<t.length;a++){let{child:r}=t[a];delete t[a].id,delete t[a].projectId,delete t[a].parentId,delete t[a].questionCount,e(r)}return t},getFileMD5:()=>n});var r=a(84770),o=a(92048);async function n(e){return new Promise((t,a)=>{let n=(0,r.createHash)("md5"),i=(0,o.createReadStream)(e);i.on("data",e=>n.update(e)),i.on("end",()=>t(n.digest("hex"))),i.on("error",a)})}}};