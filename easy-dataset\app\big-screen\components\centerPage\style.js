import styled from 'styled-components';

export const CenterPage = styled.div`
  margin-top: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%; /* 使用父容器分配的11.5rem宽度 */
  box-sizing: border-box; /* 确保所有尺寸计算正确 */
  overflow-x: hidden; /* 防止中间区域产生横向滚动 */
`;

export const CenterBottom = styled.div`
    display: flex;
    margin-bottom: 0.25rem;
    margin-top: 0.875rem;
    width: 100%;
    height: 3.25rem; /* 精确高度 */

    .detail-list {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        align-content: space-between;
        justify-content: space-around;
        width: 100%;

        &-item {
            display: flex;
            align-items: center;
            position: relative;
            height: 1.5625rem;
            padding: 0 0.1rem;
            width: 32%;
            box-sizing: border-box; /* 确保padding包含在width内 */
            border-radius: 5px;
            border: 1px solid #343f4b;
            background-color: rgba(19, 25, 47, 0.8);

            img {
                width: 1.25rem;
                height: 1.25rem;
            }

            .detail-item-text {
                margin-left: 0.1rem;
              
                h3 {
                    color: #bcdcff;
                    font-size: 16px;
                    margin-bottom: 0.25rem;
                    margin-top: 0;
                    line-height: 1.2;
                }

                span {
                    font-weight: 500;
                    font-size: 0.25rem;
                    background: linear-gradient(to bottom, #fff, #4db6e5);
                    color: transparent;
                    -webkit-background-clip: text;
                    background-clip: text;
                    display: inline;
                }

                .unit {
                    font-size: 0.2rem;
                    margin-left: 0.125rem;
                    display: inline;
                }
            }
        }
    }
`;
