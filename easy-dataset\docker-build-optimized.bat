@echo off
REM Docker构建优化脚本 (Windows版本)
REM 使用BuildKit和多种缓存策略来加速构建

setlocal enabledelayedexpansion

REM 配置变量
set IMAGE_NAME=%1
set TAG=%2
set REGISTRY=%3
set PLATFORM=%4

if "%IMAGE_NAME%"=="" set IMAGE_NAME=easy-dataset
if "%TAG%"=="" set TAG=latest
if "%PLATFORM%"=="" set PLATFORM=linux/amd64

echo 🚀 开始优化构建 Docker 镜像...
echo 镜像名称: %IMAGE_NAME%
echo 标签: %TAG%
echo 平台: %PLATFORM%

REM 启用 BuildKit
set DOCKER_BUILDKIT=1

REM 构建参数
set BUILD_ARGS=--platform=%PLATFORM% --target=runner --build-arg=BUILDKIT_INLINE_CACHE=1 --progress=plain

REM 如果指定了注册表，添加缓存配置
if not "%REGISTRY%"=="" (
    set FULL_IMAGE_NAME=%REGISTRY%/%IMAGE_NAME%:%TAG%
    set BUILD_ARGS=%BUILD_ARGS% --cache-from=%REGISTRY%/%IMAGE_NAME%:cache --cache-to=%REGISTRY%/%IMAGE_NAME%:cache,mode=max --tag=!FULL_IMAGE_NAME!
) else (
    set FULL_IMAGE_NAME=%IMAGE_NAME%:%TAG%
    set BUILD_ARGS=%BUILD_ARGS% --tag=!FULL_IMAGE_NAME!
)

echo 🔧 构建参数: %BUILD_ARGS%

REM 执行构建
echo ⏳ 开始构建镜像...
set start_time=%time%

docker build %BUILD_ARGS% -f Dockerfile.prod .

if %errorlevel% neq 0 (
    echo ❌ 构建失败！
    exit /b 1
)

set end_time=%time%

echo ✅ 构建完成！

REM 显示镜像信息
echo 📊 镜像信息:
docker images %FULL_IMAGE_NAME%

REM 如果指定了注册表，推送镜像
if not "%REGISTRY%"=="" (
    echo 📤 推送镜像到注册表...
    
    docker push %FULL_IMAGE_NAME%
    
    if %errorlevel% neq 0 (
        echo ❌ 推送失败！
        exit /b 1
    )
    
    echo ✅ 推送完成！
)

echo 🎉 所有操作完成！
pause
