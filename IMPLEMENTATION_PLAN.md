# 标签序号渲染时生成方案

## 1. 数据库结构调整

### 1.1 添加排序字段
```sql
-- 为Tags表添加排序字段
ALTER TABLE Tags ADD COLUMN sortOrder INT DEFAULT 0;
ALTER TABLE Tags ADD COLUMN displayName STRING; -- 不含序号的显示名称
```

### 1.2 更新数据库模式
```prisma
model Tags {
  id          String   @id @default(nanoid())
  label       String   // 保持原有字段用于兼容性
  displayName String   // 不含序号的纯标签名称
  sortOrder   Int      @default(0) // 排序顺序
  project     Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId   String
  parentId    String?
  parent      Tags?    @relation("Tags", fields: [parentId], references: [id])
  children    Tags[]   @relation("Tags")

  @@unique([projectId, displayName, parentId])
}
```

## 2. 核心逻辑修改

### 2.1 标签创建逻辑
- 模型生成时只生成纯标签名称（不含序号）
- 存储到 `displayName` 字段
- 根据同级标签数量自动设置 `sortOrder`
- `label` 字段保持兼容性，存储带序号的完整名称

### 2.2 标签匹配逻辑
- 优先使用 `displayName` 进行匹配
- 保留对 `label` 字段的兼容性支持
- 父子关系通过 `parentId` 确定，不再依赖序号解析

### 2.3 前端渲染逻辑
- 查询时按 `sortOrder` 排序
- 渲染时动态生成序号：
  - 一级标签：中文数字 + 顿号（一、二、三、）
  - 二级标签：阿拉伯数字 + 小数点（1.1、1.2、1.3）

## 3. 具体实现步骤

### 3.1 数据库迁移
1. 添加新字段
2. 数据迁移脚本：从现有 `label` 提取 `displayName` 和 `sortOrder`
3. 更新唯一约束

### 3.2 后端API调整
1. 修改标签创建API
2. 更新标签查询逻辑
3. 调整标签匹配算法

### 3.3 LLM提示词调整
1. 移除序号生成要求
2. 只要求生成纯标签名称

### 3.4 前端组件更新
1. 更新标签树渲染逻辑
2. 修改排序函数
3. 调整标签显示组件

## 4. 兼容性考虑

### 4.1 向后兼容
- 保留 `label` 字段用于兼容性
- 支持旧数据的自动迁移
- 渐进式更新，不影响现有功能

### 4.2 数据迁移策略
```javascript
// 数据迁移示例
async function migrateTagData() {
  const tags = await db.tags.findMany();
  
  for (const tag of tags) {
    const { displayName, sortOrder } = extractFromLabel(tag.label);
    
    await db.tags.update({
      where: { id: tag.id },
      data: {
        displayName,
        sortOrder
      }
    });
  }
}

function extractFromLabel(label) {
  // 提取序号和显示名称
  const match = label.match(/^([\d.]+)\s+(.+)$/) || label.match(/^[一二三四五六七八九十]+、\s*(.+)$/);
  
  if (match) {
    return {
      displayName: match[2] || match[1],
      sortOrder: calculateSortOrder(match[1])
    };
  }
  
  return {
    displayName: label,
    sortOrder: 0
  };
}
```

## 5. 优势分析

### 5.1 技术优势
- 解耦序号生成和标签内容
- 更灵活的排序控制
- 简化LLM生成逻辑
- 提高标签匹配准确性

### 5.2 维护优势
- 减少序号相关的复杂逻辑
- 降低标签匹配错误率
- 便于国际化支持
- 更好的数据一致性

## 6. 风险评估

### 6.1 技术风险
- 数据迁移复杂性：中等
- 兼容性问题：低（保留原字段）
- 性能影响：低（增加索引）

### 6.2 业务风险
- 用户体验变化：无（显示效果相同）
- 数据丢失风险：低（保留备份）
- 功能回归风险：低（充分测试）

## 7. 实施建议

### 7.1 分阶段实施
1. **阶段1**：数据库结构调整和数据迁移
2. **阶段2**：后端API和逻辑更新
3. **阶段3**：前端组件调整
4. **阶段4**：LLM提示词优化

### 7.2 测试策略
- 单元测试：标签创建、查询、匹配逻辑
- 集成测试：完整的标签生成流程
- 回归测试：确保现有功能不受影响
- 性能测试：大量标签场景下的性能表现
