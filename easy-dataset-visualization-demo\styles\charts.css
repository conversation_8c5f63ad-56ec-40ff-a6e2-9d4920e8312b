/**
 * 大数据可视化平台样式
 * 基于原项目精确布局，确保底部区域完全可见
 */

/* 图表容器基础样式 - 科技感设计 */
.chart-container {
  height: 5rem;
  /* 400px */
  margin-bottom: 0.375rem;
  /* 30px */
  border-radius: 0.1rem;
  /* 8px */
  background: rgba(19, 25, 47, 0.8);
  border: 1px solid rgba(0, 162, 255, 0.3);
  padding: 0.1875rem;
  /* 15px */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00a2ff, transparent);
  opacity: 0.6;
}

.chart-container:hover {
  border-color: rgba(0, 162, 255, 0.6);
  box-shadow: 0 0 0.25rem rgba(0, 162, 255, 0.3);
}

/* 图表行布局 */
.chart-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -0.1875rem;
  /* -15px */
  gap: 0.25rem;
  /* 20px */
}

/* 图表列布局 */
.chart-col {
  flex: 1;
  min-width: 3.75rem;
  /* 300px */
  padding: 0 0.1875rem;
  /* 15px */
  margin-bottom: 0.375rem;
  /* 30px */
}

/* 仪表板部分样式 */
.dashboard-section {
  background: rgba(12, 20, 38, 0.9);
  border-radius: 0.15rem;
  /* 12px */
  border: 1px solid rgba(0, 162, 255, 0.2);
  padding: 0.3125rem;
  /* 25px */
  margin-bottom: 0.5rem;
  /* 40px */
  position: relative;
  backdrop-filter: blur(10px);
}

.dashboard-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 162, 255, 0.5), transparent);
}

/* 部分标题样式 */
.section-title {
  margin-top: 0;
  padding-bottom: 0.1875rem;
  /* 15px */
  border-bottom: 1px solid rgba(0, 162, 255, 0.3);
  color: #00a2ff;
  font-size: 0.3rem;
  /* 24px */
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
  letter-spacing: 0.025rem;
}

/* 图表标题样式覆盖 */
.chart-title {
  font-size: 0.2rem;
  /* 16px */
  font-weight: 500;
  color: #bcdcff;
  margin-bottom: 0.1875rem;
  /* 15px */
  text-shadow: 0 0 5px rgba(188, 220, 255, 0.3);
}

/* 图表工具提示自定义样式 */
.chart-tooltip {
  background-color: rgba(12, 20, 38, 0.95);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 0.05rem;
  /* 4px */
  padding: 0.125rem;
  /* 10px */
  color: #ffffff;
  box-shadow: 0 0.025rem 0.1rem rgba(0, 162, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* 图表图例样式 */
.chart-legend {
  padding: 0.125rem;
  /* 10px */
  font-size: 0.15rem;
  /* 12px */
  color: #bcdcff;
}

/* 加载状态样式 */
.chart-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(12, 20, 38, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  backdrop-filter: blur(5px);
}

.chart-loading-spinner {
  border: 0.05rem solid rgba(0, 162, 255, 0.1);
  /* 4px */
  border-radius: 50%;
  border-top: 0.05rem solid #00a2ff;
  /* 4px */
  width: 0.5rem;
  /* 40px */
  height: 0.5rem;
  /* 40px */
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 空状态样式 */
.chart-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #999;
  text-align: center;
  padding: 20px;
}

.chart-empty-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: #ccc;
}

/* 图表交互元素样式 */
.chart-controls {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.chart-control-button {
  background-color: #f5f5f5;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 12px;
  color: #555;
  transition: all 0.2s ease;
}

.chart-control-button:hover {
  background-color: #e0e0e0;
}

/* 科技感主题色变量 */
:root {
  --primary-color: #00a2ff;
  --secondary-color: #00ff9f;
  --accent-color: #ff6b35;
  --warning-color: #ffb347;
  --error-color: #ff4757;
  --success-color: #2ed573;
  --info-color: #00a2ff;
  --background-color: #0c1426;
  --text-color: #ffffff;
  --text-secondary-color: #bcdcff;
}

/* 确保底部区域完全可见的关键样式 */
.content-wrapper {
  padding-bottom: 0.625rem !important;
  /* 50px 底部留白 */
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 1.25rem);
  /* 减去header高度 */
}

/* 最后一个dashboard-section的特殊处理 */
.dashboard-section:last-child {
  margin-bottom: 0.625rem !important;
  /* 50px 确保底部可见 */
}

/* 响应式设计 - 基于rem单位 */
@media screen and (max-width: 1366px) {
  .chart-container {
    height: 4.375rem;
    /* 350px */
  }

  .section-title {
    font-size: 0.25rem;
    /* 20px */
  }

  .content-wrapper {
    padding-bottom: 0.5rem !important;
    /* 40px */
  }
}

@media screen and (min-width: 1367px) and (max-width: 1600px) {
  .chart-container {
    height: 4.6875rem;
    /* 375px */
  }

  .section-title {
    font-size: 0.275rem;
    /* 22px */
  }
}

@media screen and (min-width: 1921px) and (max-width: 2560px) {
  .chart-container {
    height: 5.25rem;
    /* 420px */
  }

  .section-title {
    font-size: 0.325rem;
    /* 26px */
  }

  .content-wrapper {
    padding-bottom: 0.75rem !important;
    /* 60px */
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .chart-col {
    flex: 100%;
    min-width: auto;
  }

  .chart-row {
    flex-direction: column;
    gap: 0.125rem;
    /* 10px */
  }

  .chart-container {
    height: 4.375rem;
    /* 350px */
  }

  .dashboard-section {
    padding: 0.1875rem;
    /* 15px */
    margin-bottom: 0.25rem;
    /* 20px */
  }

  .section-title {
    font-size: 0.225rem;
    /* 18px */
    padding-bottom: 0.125rem;
    /* 10px */
  }

  .content-wrapper {
    padding: 0.125rem;
    /* 10px */
    padding-bottom: 0.5rem !important;
    /* 40px */
  }
}