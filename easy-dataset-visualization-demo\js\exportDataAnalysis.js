/**
 * 导出数据分析图表
 * 分析导出数据集的特征，包括大小、格式和质量分数
 */

// 生成虚拟的导出数据集数据
function generateExportDatasetData() {
  // 生成数据集大小统计数据
  const datasetSizeData = [
    { name: '小型数据集 (<1GB)', value: 35 },
    { name: '中型数据集 (1-5GB)', value: 42 },
    { name: '大型数据集 (5-20GB)', value: 18 },
    { name: '超大型数据集 (>20GB)', value: 5 }
  ];
  
  // 生成数据集格式分布数据
  const datasetFormatData = [
    { name: 'JSON', value: 28 },
    { name: 'CSV', value: 22 },
    { name: 'JSONL', value: 15 },
    { name: '<PERSON><PERSON><PERSON>', value: 12 },
    { name: 'HDF5', value: 8 },
    { name: 'SQLite', value: 10 },
    { name: '其他格式', value: 5 }
  ];
  
  // 生成导出历史趋势数据
  const exportHistoryData = [];
  // 生成过去12个月的数据
  const currentDate = new Date();
  const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  
  for (let i = 11; i >= 0; i--) {
    const month = new Date(currentDate);
    month.setMonth(currentDate.getMonth() - i);
    
    // 生成该月的导出数据
    const jsonCount = Math.floor(Math.random() * 15) + 5;
    const csvCount = Math.floor(Math.random() * 12) + 3;
    const parquetCount = Math.floor(Math.random() * 8) + 2;
    const otherCount = Math.floor(Math.random() * 5) + 1;
    
    exportHistoryData.push({
      month: monthNames[month.getMonth()],
      'JSON': jsonCount,
      'CSV': csvCount,
      'Parquet': parquetCount,
      '其他': otherCount,
      'total': jsonCount + csvCount + parquetCount + otherCount
    });
  }
  
  // 生成数据集质量评分数据
  const datasetQualityData = [
    { 
      name: 'Easy Dataset 标准集',
      scores: {
        '数据完整性': 9.2,
        '问答对质量': 8.8,
        '格式规范性': 9.5,
        '覆盖范围': 8.5,
        '多样性': 8.7
      }
    },
    { 
      name: 'OpenAssistant',
      scores: {
        '数据完整性': 8.5,
        '问答对质量': 8.7,
        '格式规范性': 8.2,
        '覆盖范围': 9.0,
        '多样性': 9.2
      }
    },
    { 
      name: 'Alpaca',
      scores: {
        '数据完整性': 8.0,
        '问答对质量': 7.8,
        '格式规范性': 8.5,
        '覆盖范围': 7.5,
        '多样性': 7.2
      }
    },
    { 
      name: 'ShareGPT',
      scores: {
        '数据完整性': 7.5,
        '问答对质量': 8.2,
        '格式规范性': 7.0,
        '覆盖范围': 8.8,
        '多样性': 8.5
      }
    }
  ];
  
  // 生成导出目标平台分布数据
  const exportPlatformData = [
    { name: 'HuggingFace', value: 35 },
    { name: '本地文件', value: 25 },
    { name: 'ModelScope', value: 15 },
    { name: 'Kaggle', value: 12 },
    { name: 'OpenDataLab', value: 8 },
    { name: '其他平台', value: 5 }
  ];
  
  return {
    datasetSizeData,
    datasetFormatData,
    exportHistoryData,
    datasetQualityData,
    exportPlatformData
  };
}

// 初始化数据集大小统计图表
function initDatasetSizeChart() {
  const { datasetSizeData } = generateExportDatasetData();
  
  // 获取图表容器
  const chartDom = document.getElementById('export-dataset-size');
  const chart = echarts.init(chartDom);
  
  // 配置图表选项
  const option = {
    title: {
      text: '导出数据集大小分布 - 环形图 (pie)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: datasetSizeData.map(item => item.name)
    },
    series: [
      {
        name: '数据集大小',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: datasetSizeData
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化数据集格式分布图表
function initDatasetFormatChart() {
  const { datasetFormatData } = generateExportDatasetData();
  
  // 获取图表容器
  const chartDom = document.getElementById('dataset-format-distribution');
  const chart = echarts.init(chartDom);
  
  // 配置图表选项
  const option = {
    title: {
      text: '数据集格式分布 - 饼图 (pie)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '5%',
      left: 'center',
      data: datasetFormatData.map(item => item.name)
    },
    series: [
      {
        name: '数据集格式',
        type: 'pie',
        radius: '50%',
        center: ['50%', '45%'],
        data: datasetFormatData.sort((a, b) => b.value - a.value),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化导出历史记录趋势图
function initExportHistoryChart() {
  const { exportHistoryData } = generateExportDatasetData();
  
  // 获取图表容器
  const chartDom = document.getElementById('export-history');
  const chart = echarts.init(chartDom);
  
  // 准备数据
  const months = exportHistoryData.map(item => item.month);
  
  // 配置图表选项
  const option = {
    title: {
      text: '导出历史记录和趋势 - 堆叠柱状图 (bar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['JSON', 'CSV', 'Parquet', '其他'],
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      name: '导出次数'
    },
    series: [
      {
        name: 'JSON',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: exportHistoryData.map(item => item.JSON)
      },
      {
        name: 'CSV',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: exportHistoryData.map(item => item.CSV)
      },
      {
        name: 'Parquet',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: exportHistoryData.map(item => item.Parquet)
      },
      {
        name: '其他',
        type: 'bar',
        stack: 'total',
        emphasis: {
          focus: 'series'
        },
        data: exportHistoryData.map(item => item.其他)
      },
      {
        name: '总计',
        type: 'line',
        smooth: true,
        data: exportHistoryData.map(item => item.total),
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#5470c6'
        },
        itemStyle: {
          color: '#5470c6'
        }
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化数据集质量评分雷达图
function initDatasetQualityRadarChart() {
  const { datasetQualityData } = generateExportDatasetData();
  
  // 获取图表容器
  const chartDom = document.getElementById('dataset-quality-radar');
  const chart = echarts.init(chartDom);
  
  // 准备雷达图数据
  const indicators = Object.keys(datasetQualityData[0].scores).map(key => ({
    name: key,
    max: 10
  }));
  
  const series = datasetQualityData.map(dataset => ({
    name: dataset.name,
    value: Object.values(dataset.scores),
    areaStyle: {
      opacity: 0.1
    }
  }));
  
  // 配置图表选项
  const option = {
    title: {
      text: '数据集质量评分对比 - 雷达图 (radar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: datasetQualityData.map(dataset => dataset.name),
      bottom: '0%'
    },
    radar: {
      indicator: indicators,
      radius: '65%'
    },
    series: [{
      type: 'radar',
      data: series
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化数据集与行业标准对比分析图
function initDatasetComparisonChart() {
  const { datasetQualityData, exportPlatformData } = generateExportDatasetData();
  
  // 获取图表容器
  const chartDom = document.getElementById('dataset-comparison');
  const chart = echarts.init(chartDom);
  
  // 准备数据
  const datasets = datasetQualityData.map(dataset => dataset.name);
  const metrics = Object.keys(datasetQualityData[0].scores);
  
  // 为每个指标准备数据系列
  const series = metrics.map(metric => {
    return {
      name: metric,
      type: 'bar',
      data: datasetQualityData.map(dataset => dataset.scores[metric])
    };
  });
  
  // 配置图表选项
  const option = {
    title: {
      text: '数据集与行业标准对比分析 - 柱状图 (bar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: metrics,
      bottom: '0%'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: datasets,
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '评分',
      min: 0,
      max: 10
    },
    series: series
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
  initDatasetSizeChart();
  initDatasetFormatChart();
  initExportHistoryChart();
  initDatasetQualityRadarChart();
  initDatasetComparisonChart();
}); 