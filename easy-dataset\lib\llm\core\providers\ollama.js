import { createOllama } from 'ollama-ai-provider';
import BaseClient from './base.js';

class OllamaClient extends BaseClient {
  constructor(config) {
    super(config);

    // 确保endpoint正确处理
    let baseURL = this.endpoint;

    // 移除尾部斜杠
    while (baseURL.endsWith('/')) {
      baseURL = baseURL.slice(0, -1);
    }

    // 处理后的Ollama端点
    console.log('处理后的Ollama端点:', baseURL);

    // 确保基础URL正确 - 不要自动添加/api，保持原始端点
    this.baseURL = baseURL;

    // 为ollama-ai-provider库创建带/api的URL
    let apiBaseURL = baseURL;
    if (!apiBaseURL.endsWith('/api')) {
      apiBaseURL = `${apiBaseURL}/api`;
    }

    console.log('Ollama 初始化baseURL:', apiBaseURL);

    this.ollama = createOllama({
      baseURL: apiBaseURL,
      apiKey: this.apiKey
    });
  }

  _getModel() {
    return this.ollama(this.model);
  }

  /**
   * 获取本地可用的模型列表
   * @returns {Promise<Array>} 返回模型列表
   */
  async getModels() {
    try {
      // 使用标准的Ollama API路径
      const response = await fetch(`${this.baseURL}/api/tags`);
      const data = await response.json();
      // 处理响应，提取模型名称
      if (data && data.models) {
        return data.models.map(model => ({
          name: model.name,
          modified_at: model.modified_at,
          size: model.size
        }));
      }
      return [];
    } catch (error) {
      console.error('Fetch error:', error);
      return [];
    }
  }

  async chat(messages, options) {
    try {
      // 使用fetch直接调用API，避免使用ollama-ai-provider库的超时限制
      const payload = {
        model: this.model,
        messages: this._convertJson(messages),
        stream: false,
        options: {
          temperature: options.temperature || this.modelConfig.temperature,
          top_p: options.top_p || this.modelConfig.top_p,
          num_predict: options.max_tokens || this.modelConfig.max_tokens
        }
      };

      // 设置超时时间 - 增加到120秒以支持复杂任务
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 120000); // 120秒超时

      // 构建API URL - 使用标准的Ollama API路径
      const apiUrl = `${this.baseURL}/api/chat`;
      console.log('Ollama API请求URL:', apiUrl);
      console.log('请求模型:', this.model);
      console.log('请求负载:', JSON.stringify(payload));

      // 添加重试逻辑
      let retryCount = 0;
      let maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(payload),
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${errorText}`);
          }

          const data = await response.json();
          console.log('Ollama API 原始响应:', JSON.stringify(data));

          // 如果响应为空或无效
          if (!data || (Object.keys(data).length === 0)) {
            console.warn('Ollama API 返回了空响应');
            return {
              text: '无有效响应',
              reasoning: '',
              response: {
                body: { message: { content: '无有效响应' } },
                messages: '无有效响应'
              }
            };
          }

          // 确保响应有有效内容
          const content = data.message?.content || '';

          // 创建标准格式的响应对象
          return {
            text: content,
            reasoning: data.message?.thinking || '',
            response: {
              body: data,
              messages: content
            }
          };
        } catch (error) {
          retryCount++;
          if (retryCount > maxRetries) {
            console.error(`Ollama API调用失败，已重试${maxRetries}次:`, error);

            // 返回错误响应而不是抛出异常
            return {
              text: `模型调用失败: ${error.message}`,
              reasoning: '',
              response: {
                body: { error: error.message },
                messages: `模型调用失败: ${error.message}`
              }
            };
          }

          console.log(`Ollama API调用失败，重试 ${retryCount}/${maxRetries}:`, error.message);
          // 等待一段时间再重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    } catch (error) {
      console.error('ollama API 调用出错:', error);

      // 返回错误响应而不是抛出异常
      return {
        text: `模型调用出错: ${error.message}`,
        reasoning: '',
        response: {
          body: { error: error.message },
          messages: `模型调用出错: ${error.message}`
        }
      };
    }
  }

  async chatStreamAPI(messages, options) {
    const model = this._getModel();
    const modelName = typeof model === 'function' ? model.modelName : this.model;

    // 构建符合 Ollama API 的请求数据
    const payload = {
      model: modelName,
      messages: this._convertJson(messages),
      stream: true, // 开启流式输出
      options: {
        temperature: options.temperature || this.modelConfig.temperature,
        top_p: options.top_p || this.modelConfig.top_p,
        num_predict: options.max_tokens || this.modelConfig.max_tokens
      }
    };

    // 构建API URL - 使用标准的Ollama API路径
    const apiUrl = `${this.baseURL}/api/chat`;
    console.log('Ollama 流式API请求URL:', apiUrl);

    try {
      // 设置超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 120000); // 120秒超时

      // 发起流式请求
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload),
        signal: controller.signal
      });

      // 清除超时定时器
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${errorText}`);
      }

      if (!response.body) {
        throw new Error('响应中没有可读取的数据流');
      }

      // 处理原始数据流，实现思维链的流式输出
      const reader = response.body.getReader();
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();

      // 创建一个新的可读流
      const newStream = new ReadableStream({
        async start(controller) {
          let buffer = '';
          let isThinking = false; // 当前是否在输出思维链模式
          let pendingReasoning = null; // 等待输出的思维链

          // 输出文本内容
          const sendContent = text => {
            if (!text) return;

            // 如果正在输出思维链，需要先关闭思维链标签
            if (isThinking) {
              controller.enqueue(encoder.encode('</think>'));
              isThinking = false;
            }

            controller.enqueue(encoder.encode(text));
          };

          // 流式输出思维链
          const sendReasoning = text => {
            if (!text) return;

            // 如果还没有开始思维链输出，需要先添加思维链标签
            if (!isThinking) {
              controller.enqueue(encoder.encode('<think>'));
              isThinking = true;
            }

            controller.enqueue(encoder.encode(text));
          };

          try {
            while (true) {
              const { done, value } = await reader.read();

              if (done) {
                // 流结束时，如果还在思维链模式，关闭标签
                if (isThinking) {
                  controller.enqueue(encoder.encode('</think>'));
                }
                controller.close();
                break;
              }

              // 解析数据块
              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;

              // 处理数据行
              let boundary = buffer.indexOf('\n');
              while (boundary !== -1) {
                const line = buffer.substring(0, boundary).trim();
                buffer = buffer.substring(boundary + 1);

                if (line) {
                  try {
                    // 解析JSON数据
                    const jsonData = JSON.parse(line);
                    const deltaContent = jsonData.message?.content;
                    const deltaReasoning = jsonData.message?.thinking;

                    // 如果有思维链内容，则实时流式输出
                    if (deltaReasoning) {
                      sendReasoning(deltaReasoning);
                    }

                    // 如果有正文内容也实时输出
                    if (deltaContent !== undefined && deltaContent !== null) {
                      sendContent(deltaContent);
                    }
                  } catch (e) {
                    // 忽略 JSON 解析错误
                    console.error('解析响应数据出错:', e);
                  }
                }

                boundary = buffer.indexOf('\n');
              }
            }
          } catch (error) {
            console.error('处理数据流时出错:', error);
            // 如果出错时正在输出思维链，要关闭思维链标签
            if (isThinking) {
              try {
                controller.enqueue(encoder.encode('</think>'));
              } catch (e) {
                console.error('关闭思维链标签出错:', e);
              }
            }
            controller.error(error);
          }
        }
      });

      // 最终返回响应流
      return new Response(newStream, {
        headers: {
          'Content-Type': 'text/plain', // 纯文本格式
          'Cache-Control': 'no-cache',
          Connection: 'keep-alive'
        }
      });
    } catch (error) {
      console.error('流式API调用出错:', error);
      throw error;
    }
  }

  // 将消息转换为Ollama API所需的格式
  _convertJson(messages) {
    if (!Array.isArray(messages)) {
      return [{ role: 'user', content: messages }];
    }

    // 确保消息格式正确
    return messages.map(msg => {
      const role = msg.role?.toLowerCase() || 'user';
      return {
        role: role === 'assistant' ? 'assistant' : role === 'system' ? 'system' : 'user',
        content: msg.content || ''
      };
    });
  }
}

module.exports = OllamaClient;
