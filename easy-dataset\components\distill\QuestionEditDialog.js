'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Typography,
  Autocomplete,
  Chip
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

/**
 * 问题编辑对话框组件
 * @param {Object} props
 * @param {boolean} props.open - 对话框是否打开
 * @param {Function} props.onClose - 关闭对话框的回调
 * @param {Object} props.question - 要编辑的问题对象
 * @param {string} props.projectId - 项目ID
 * @param {Array} props.tags - 可选标签列表
 * @param {Function} props.onSuccess - 编辑成功的回调
 */
export default function QuestionEditDialog({ open, onClose, question, projectId, tags = [], onSuccess }) {
  const { t } = useTranslation();
  const [questionText, setQuestionText] = useState('');
  const [selectedTags, setSelectedTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 当问题变化时，更新输入框的值
  useEffect(() => {
    if (question) {
      setQuestionText(question.question || '');
      // 设置当前问题的标签
      if (question.tags && Array.isArray(question.tags)) {
        setSelectedTags(question.tags);
      } else if (question.tag) {
        // 如果问题只有一个标签
        setSelectedTags([question.tag]);
      } else {
        setSelectedTags([]);
      }
    }
  }, [question]);

  // 重置状态
  const resetState = () => {
    setQuestionText('');
    setSelectedTags([]);
    setError('');
    setLoading(false);
  };

  // 处理对话框关闭
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 处理问题编辑
  const handleEdit = async () => {
    if (!questionText.trim()) {
      setError(t('distill.questionRequired'));
      return;
    }

    if (selectedTags.length === 0) {
      setError(t('distill.tagRequired'));
      return;
    }

    // 检查是否有变化
    const hasQuestionChanged = questionText.trim() !== question?.question;
    const hasTagsChanged = JSON.stringify(selectedTags.map(tag => tag.id).sort()) !==
                          JSON.stringify((question?.tags || [question?.tag].filter(Boolean)).map(tag => tag.id).sort());

    if (!hasQuestionChanged && !hasTagsChanged) {
      // 如果没有变化，直接关闭
      handleClose();
      return;
    }

    try {
      setLoading(true);
      setError('');

      // 准备更新数据
      const updateData = {
        question: questionText.trim(),
        tagIds: selectedTags.map(tag => tag.id)
      };

      // 调用更新问题API
      const response = await axios.put(`/api/projects/${projectId}/questions/${question.id}`, updateData);

      if (response.data) {
        // 编辑成功
        if (onSuccess) {
          onSuccess(response.data);
        }
        handleClose();
      }
    } catch (error) {
      console.error('编辑问题失败:', error);
      setError(error.response?.data?.error || t('distill.editQuestionError'));
    } finally {
      setLoading(false);
    }
  };

  // 处理键盘事件
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleEdit();
    }
  };

  // 获取标签选项
  const getTagOptions = () => {
    return tags.filter(tag => tag.id && tag.label);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>{t('domain.editQuestion')}</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 1 }}>
          <TextField
            autoFocus
            fullWidth
            label={t('domain.questionContent')}
            value={questionText}
            onChange={(e) => setQuestionText(e.target.value)}
            onKeyPress={handleKeyPress}
            multiline
            rows={4}
            error={!!error && error.includes(t('domain.questionRequired'))}
            disabled={loading}
            placeholder={t('domain.inputEditQuestionContent')}
            sx={{ mb: 2 }}
          />

          <Autocomplete
            multiple
            options={getTagOptions()}
            getOptionLabel={(option) => option.label || ''}
            value={selectedTags}
            onChange={(event, newValue) => {
              setSelectedTags(newValue);
            }}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option.label}
                  {...getTagProps({ index })}
                  key={option.id}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                label={t('domain.selectTags')}
                placeholder={t('domain.selectTagsPlaceholder')}
                error={!!error && error.includes(t('domain.tagRequired'))}
                disabled={loading}
              />
            )}
            disabled={loading}
          />

          {error && (
            <Typography variant="body2" color="error" sx={{ mt: 1 }}>
              {error}
            </Typography>
          )}

          {question && (
            <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('domain.originalQuestion')}:
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                {question.question}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('domain.originalTags')}: {(question.tags || [question.tag].filter(Boolean)).map(tag => tag?.label).join(', ')}
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleEdit}
          variant="contained"
          disabled={loading || !questionText.trim() || selectedTags.length === 0}
        >
          {loading ? t('common.saving') : t('common.save')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
