import { getProjectRoot } from '@/lib/db/base';
import { getTaskConfig } from '@/lib/db/projects';
import { getUploadFileInfoByFileName } from '@/lib/db/upload-files';
import { downloadDocument, uploadDocument } from '@/lib/oss-simple';
import convertPrompt from '@/lib/llm/prompts/pdfToMarkdown';
import convertPromptEn from '@/lib/llm/prompts/pdfToMarkdownEn';
import reTitlePrompt from '@/lib/llm/prompts/optimalTitle';
import reTitlePromptEn from '@/lib/llm/prompts/optimalTitleEn';
import path from 'path';
import fs from 'fs';
import os from 'os';

export async function visionProcessing(projectId, fileName, options = {}) {
  try {
    const { updateTask, task, message } = options;

    let taskCompletedCount = task.completedCount;

    console.log('executing vision conversion strategy......');

    // 1. 获取PDF文件信息
    const pdfFileInfo = await getUploadFileInfoByFileName(projectId, fileName);
    if (!pdfFileInfo) {
      throw new Error(`PDF file ${fileName} not found in database`);
    }

    // 2. 从OSS下载PDF文件到临时目录
    const pdfResult = await downloadDocument(pdfFileInfo.id);

    // 创建临时文件
    const tempDir = path.join(os.tmpdir(), `vision-${projectId}-${Date.now()}`);
    await fs.promises.mkdir(tempDir, { recursive: true });
    const tempFilePath = path.join(tempDir, fileName);

    // 将OSS内容写入临时文件（PDF文件应该返回Buffer）
    const pdfBuffer = Buffer.isBuffer(pdfResult.content) ? pdfResult.content : Buffer.from(pdfResult.content);
    await fs.promises.writeFile(tempFilePath, pdfBuffer);

    // 获取项目配置
    const taskConfig = await getTaskConfig(projectId);

    const model = task.vsionModel;

    if (!model) {
      throw new Error('please check if pdf conversion vision model is configured');
    }

    if (model.type !== 'vision') {
      throw new Error(
        `${model.modelName}(${model.providerName}) this model is not a vision model, please check [model configuration]`
      );
    }

    if (!model.apiKey) {
      throw new Error(
        `${model.modelName}(${model.providerName}) this model has no api key configured, please check [model configuration]`
      );
    }

    const convert = task.language === 'en' ? convertPromptEn : convertPrompt;
    const reTitle = task.language === 'en' ? reTitlePromptEn : reTitlePrompt;

    //创建临时输出目录
    const tempOutputDir = path.join(tempDir, 'output');
    await fs.promises.mkdir(tempOutputDir, { recursive: true });

    const config = {
      pdfPath: tempFilePath,
      outputDir: tempOutputDir,
      apiKey: model.apiKey,
      model: model.modelId,
      baseUrl: model.endpoint,
      useFullPage: true,
      verbose: false,
      concurrency: taskConfig.visionConcurrencyLimit,
      prompt: convert(),
      textPrompt: reTitle(),
      onProgress: async ({ current, total }) => {
        if (updateTask && task.id) {
          message.current.processedPage = current;
          message.setpInfo = `processing ${fileName} ${current}/${total} pages progress: ${(current / total) * 100}% `;
          await updateTask(task.id, {
            completedCount: taskCompletedCount + current,
            detail: JSON.stringify(message)
          });
        }
      }
    };

    console.log('vision strategy: starting pdf file processing');

    const { parsePdf } = await import('pdf2md-js');
    await parsePdf(tempFilePath, config);

    // 处理完成后，将生成的MD文件上传到OSS
    const convertName = fileName.replace('.pdf', '.md');
    const outputMdPath = path.join(tempOutputDir, convertName);

    if (await fs.promises.access(outputMdPath).then(() => true).catch(() => false)) {
      // 读取生成的MD文件
      const markdownText = await fs.promises.readFile(outputMdPath, 'utf-8');

      // 上传到OSS
      const mdBuffer = Buffer.from(markdownText, 'utf-8');
      await uploadDocument(projectId, convertName, mdBuffer);

    } else {
      throw new Error(`Generated MD file not found: ${outputMdPath}`);
    }

    // 清理临时文件
    try {
      await fs.promises.rm(tempDir, { recursive: true, force: true });
    } catch (cleanupError) {
      console.warn('Failed to cleanup temporary files:', cleanupError);
    }

    //转换结束
    return { success: true };
  } catch (error) {
    console.error('vision strategy processing error:', error);
    throw error;
  }
}

export default {
  visionProcessing
};
