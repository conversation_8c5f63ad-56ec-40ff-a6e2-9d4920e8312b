/**
 * 数据集生成进度显示
 * 使用仪表盘图显示数据集生成进度和完成率
 */

// 生成数据集生成进度的虚拟数据
function generateDatasetProgressData() {
    // 生成一个0-100之间的随机进度值，偏向于较高的完成度
    const progress = Math.floor(Math.random() * 30) + 70; // 70-100之间的随机值
    return progress;
}

// 初始化ECharts仪表盘图
function initDatasetProgressChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('dataset-progress');
    if (!chartContainer) {
        console.error('找不到图表容器：dataset-progress');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const progressValue = generateDatasetProgressData();
    
    // 配置图表选项
    const option = {
        title: {
            text: '数据集生成进度 - 仪表盘 (gauge)',
            left: 'center'
        },
        tooltip: {
            formatter: '{a} <br/>{b} : {c}%'
        },
        series: [
            {
                name: '进度',
                type: 'gauge',
                detail: {
                    formatter: '{value}%',
                    fontSize: 24,
                    offsetCenter: [0, '70%']
                },
                data: [{ value: progressValue, name: '完成率' }],
                axisLine: {
                    lineStyle: {
                        width: 30,
                        color: [
                            [0.3, '#ff6e76'],   // 0-30% 红色
                            [0.7, '#fddd60'],   // 30-70% 黄色
                            [1, '#7cffb2']      // 70-100% 绿色
                        ]
                    }
                },
                pointer: {
                    itemStyle: {
                        color: 'auto'
                    }
                },
                axisTick: {
                    distance: -30,
                    length: 8,
                    lineStyle: {
                        color: '#fff',
                        width: 2
                    }
                },
                splitLine: {
                    distance: -30,
                    length: 30,
                    lineStyle: {
                        color: '#fff',
                        width: 4
                    }
                },
                axisLabel: {
                    color: 'auto',
                    distance: 40,
                    fontSize: 14
                },
                title: {
                    offsetCenter: [0, '95%'],
                    fontSize: 16
                },
                animationDuration: 1000,
                animationDurationUpdate: 1000
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    // 添加一个定时器，每5秒更新一次进度（仅用于演示）
    if (window.progressInterval) {
        clearInterval(window.progressInterval);
    }
    
    window.progressInterval = setInterval(function() {
        // 获取当前进度
        const currentValue = option.series[0].data[0].value;
        
        // 如果进度已经达到100%，重置为较低值
        if (currentValue >= 99) {
            option.series[0].data[0].value = 10;
        } else {
            // 否则增加一个随机值（1-5之间）
            option.series[0].data[0].value = Math.min(100, currentValue + Math.floor(Math.random() * 5) + 1);
        }
        
        // 更新图表
        chart.setOption(option);
    }, 5000);
    
    return chart;
}

// 在文档加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initDatasetProgressChart();
}); 