-- 按项目去除标签序号的SQL脚本
-- 可以指定特定项目ID来执行更新

-- =====================================================
-- 配置区域：设置要处理的项目ID
-- =====================================================

-- 请将下面的 'YOUR_PROJECT_ID' 替换为实际的项目ID
SET @target_project_id = 'YOUR_PROJECT_ID';

-- 或者查看所有项目ID来选择
SELECT id, name, description FROM Projects ORDER BY createAt DESC;

-- =====================================================
-- 预览指定项目中将要更新的数据
-- =====================================================

-- 预览 Tags 表中指定项目的记录
SELECT 
    'Tags' as table_name,
    id,
    label as original_label,
    CASE 
        WHEN label REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN label REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
        ELSE label
    END as new_label,
    projectId
FROM Tags 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)
ORDER BY label;

-- 预览 Questions 表中指定项目的记录
SELECT 
    'Questions' as table_name,
    id,
    label as original_label,
    CASE 
        WHEN label REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN label REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
        ELSE label
    END as new_label,
    projectId
FROM Questions 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)
ORDER BY label
LIMIT 20;

-- 预览 Datasets 表中指定项目的记录
SELECT 
    'Datasets' as table_name,
    id,
    questionLabel as original_label,
    CASE 
        WHEN questionLabel REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN questionLabel REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+、\\s*', ''))
        WHEN questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN questionLabel REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\s+', ''))
        ELSE questionLabel
    END as new_label,
    projectId
FROM Datasets 
WHERE projectId = @target_project_id
AND (
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+'
)
ORDER BY questionLabel
LIMIT 20;

-- =====================================================
-- 统计指定项目中将要更新的记录数量
-- =====================================================

SELECT 
    'Tags' as table_name,
    COUNT(*) as records_to_update
FROM Tags 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)

UNION ALL

SELECT 
    'Questions' as table_name,
    COUNT(*) as records_to_update
FROM Questions 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)

UNION ALL

SELECT 
    'Datasets' as table_name,
    COUNT(*) as records_to_update
FROM Datasets 
WHERE projectId = @target_project_id
AND (
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+'
);

-- =====================================================
-- 执行更新（确认预览结果无误后再执行）
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 更新指定项目的 Tags 表
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[一二三四五六七八九十]+、';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+、';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+\\s+';

-- 更新指定项目的 Questions 表
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[一二三四五六七八九十]+、';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+、';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND label REGEXP '^[0-9]+\\s+';

-- 更新指定项目的 Datasets 表
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE projectId = @target_project_id AND questionLabel REGEXP '^[一二三四五六七八九十]+、';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+、\\s*', ''))
WHERE projectId = @target_project_id AND questionLabel REGEXP '^[0-9]+、';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\s+', ''))
WHERE projectId = @target_project_id AND questionLabel REGEXP '^[0-9]+\\s+';

-- 提交事务
COMMIT;

-- =====================================================
-- 验证更新结果
-- =====================================================

-- 检查指定项目是否还有遗漏的序号格式
SELECT 'Tags - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Tags 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)

UNION ALL

SELECT 'Questions - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Questions 
WHERE projectId = @target_project_id
AND (
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
)

UNION ALL

SELECT 'Datasets - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Datasets 
WHERE projectId = @target_project_id
AND (
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+'
);

-- 显示更新后的标签示例
SELECT 'Updated Tags' as type, id, label, projectId FROM Tags 
WHERE projectId = @target_project_id 
ORDER BY label 
LIMIT 10;
