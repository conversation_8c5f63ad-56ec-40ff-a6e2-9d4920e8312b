import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

const prisma = new PrismaClient();

export async function GET() {
  try {
    // 获取所有项目
    const projects = await prisma.projects.findMany({
      select: {
        id: true,
        country: true,
        name: true
      }
    });

    // 国家代码到英文名称的映射
    const codeToCountryName = {
      'BN': 'Brunei',
      'KH': 'Cambodia',
      'ID': 'Indonesia',
      'LA': 'Laos',
      'MY': 'Malaysia',
      'MM': 'Myanmar',
      'PH': 'Philippines',
      'SG': 'Singapore',
      'TH': 'Thailand',
      'VN': 'Vietnam'
    };

    const globalStatistics = {};

    // 为每个国家聚合统计数据
    for (const project of projects) {
      if (project.country) {
        const countryName = codeToCountryName[project.country];
        if (countryName) {
          try {
            // 获取该项目的统计数据
            const [datasetCount, questionCount] = await Promise.all([
              // 数据集数量
              prisma.datasets.count({
                where: { projectId: project.id }
              }),

              // 问题数量
              prisma.questions.count({
                where: { projectId: project.id }
              })
            ]);

            // 如果该国家已有数据，则累加；否则初始化
            if (globalStatistics[countryName]) {
              globalStatistics[countryName].datasets += datasetCount;
              globalStatistics[countryName].questions += questionCount;
              globalStatistics[countryName].projects += 1;
            } else {
              globalStatistics[countryName] = {
                datasets: datasetCount,
                questions: questionCount,
                projects: 1,
                lastUpdated: new Date().toISOString()
              };
            }
          } catch (error) {
            console.warn(`获取项目 ${project.id} 统计数据失败:`, error);
          }
        }
      }
    }

    return NextResponse.json(globalStatistics);
  } catch (error) {
    console.error('获取全局统计数据失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}