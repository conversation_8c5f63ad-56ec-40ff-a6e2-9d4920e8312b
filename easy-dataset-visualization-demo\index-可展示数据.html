<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大数据可视化平台</title>
    <!-- ECharts CDN -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/echarts.min.js"></script>
    <link rel="stylesheet" href="styles/charts-original.css">
    <style>
        /* 响应式适配系统 - 基于原项目的flexible.js逻辑 */
        html {
            font-size: 80px;
            /* 1rem = 80px，基于1920px设计稿 */
        }

        /* 根据屏幕宽度动态调整根字体大小 */
        @media screen and (max-width: 1366px) {
            html {
                font-size: 56.67px;
            }

            /* 1366/24 */
        }

        @media screen and (min-width: 1367px) and (max-width: 1600px) {
            html {
                font-size: 66.67px;
            }

            /* 1600/24 */
        }

        @media screen and (min-width: 1601px) and (max-width: 1920px) {
            html {
                font-size: 80px;
            }

            /* 1920/24 */
        }

        @media screen and (min-width: 1921px) and (max-width: 2560px) {
            html {
                font-size: 106.67px;
            }

            /* 2560/24 */
        }

        @media screen and (min-width: 2561px) {
            html {
                font-size: 106.67px;
            }

            /* 最大适配2560px */
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            margin: 0;
            padding: 0.125rem 0 0 0;
            /* 对应原项目的 10px 0 0 0 */
            background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
            color: #ffffff;
            overflow-x: hidden;
            overflow-y: auto;
            /* 🔑 明确允许垂直滚动 */
            min-height: 100vh;
            position: relative;
        }

        /* 添加科技感背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(0, 162, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(0, 255, 162, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .main-container {
            width: 100%;
            min-height: 100vh;
            /* 🔑 改为min-height，允许内容超出 */
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header {
            text-align: center;
            padding: 0.25rem 0;
            /* 20px */
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 2px solid rgba(0, 162, 255, 0.3);
            position: relative;
            z-index: 10;
        }

        .header h1 {
            color: #00a2ff;
            margin: 0;
            font-size: 0.5rem;
            /* 40px */
            font-weight: 600;
            text-shadow: 0 0 20px rgba(0, 162, 255, 0.5);
            letter-spacing: 0.05rem;
        }

        .header p {
            color: #bcdcff;
            font-size: 0.2rem;
            /* 16px */
            margin: 0.125rem 0 0 0;
            opacity: 0.8;
        }

        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: visible;
            /* 🔑 允许内容溢出，触发body滚动 */
            padding: 0.25rem;
            /* 20px */
        }
    </style>
</head>

<body>
    <div class="index-page-style">
        <!-- 🔑 原项目的顶部标题区域 -->
        <div class="top-page">
            <div class="top-box">
                <div class="top_box">
                    <div class="title-box">
                        <div class="title">
                            <div class="title-text">大数据可视化平台</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="time-box">
                <h3 id="current-time">2025-08-07 09:51:57 星期四</h3>
            </div>
        </div>

        <!-- 🔑 原项目的主要内容区域 -->
        <div class="index-page-content">
            <!-- 左侧内容 -->
            <div class="left-page">
                <!-- 这里将放置左侧的图表内容 -->
                <div class="left-content-placeholder">
                    <div
                        style="height: 4.375rem; background: rgba(19, 25, 47, 0.6); border-radius: 10px; margin-bottom: 0.25rem;">
                    </div>
                    <div style="height: 7.75rem; background: rgba(19, 25, 47, 0.6); border-radius: 10px;"></div>
                </div>
            </div>

            <!-- 中间内容 -->
            <div class="center-page">
                <div class="center-page-container">
                    <!-- 地图区域 -->
                    <div id="asean-map"
                        style="width: 100%; height: 500px; background: rgba(19, 25, 47, 0.6); border-radius: 10px;">
                    </div>

                    <!-- 🔑 原项目的底部数据统计区域 -->
                    <div class="center-bottom">
                        <div class="detail-list">
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDBhMmZmIi8+Cjwvc3ZnPgo="
                                    alt="文档生成总量">
                                <div class="detail-item-text">
                                    <h3>文档生成总量</h3>
                                    <span>1573</span>
                                    <span class="unit">个</span>
                                </div>
                            </div>
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMDBmZjlmIi8+Cjwvc3ZnPgo="
                                    alt="问题生成总量">
                                <div class="detail-item-text">
                                    <h3>问题生成总量</h3>
                                    <span>2</span>
                                    <span class="unit">个</span>
                                </div>
                            </div>
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjZmY2YjM1Ii8+Cjwvc3ZnPgo="
                                    alt="答案总数">
                                <div class="detail-item-text">
                                    <h3>答案总数</h3>
                                    <span>1569</span>
                                    <span class="unit">个</span>
                                </div>
                            </div>
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjZmZiMzQ3Ii8+Cjwvc3ZnPgo="
                                    alt="导入文档总量">
                                <div class="detail-item-text">
                                    <h3>导入文档总量</h3>
                                    <span>2</span>
                                    <span class="unit">个</span>
                                </div>
                            </div>
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjMmVkNTczIi8+Cjwvc3ZnPgo="
                                    alt="导出文档总量">
                                <div class="detail-item-text">
                                    <h3>导出文档总量</h3>
                                    <span>0</span>
                                    <span class="unit">个</span>
                                </div>
                            </div>
                            <div class="detail-list-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBmaWxsPSIjZmY0NzU3Ii8+Cjwvc3ZnPgo="
                                    alt="平均文档长度">
                                <div class="detail-item-text">
                                    <h3>平均文档长度</h3>
                                    <span>0</span>
                                    <span class="unit">字</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 🔑 保持现有功能：所有仪表板内容 -->
                <div class="dashboard-content" style="margin-top: 0.25rem;">

                    <!-- 1. 数据集概览仪表板 -->
                    <div class="dashboard-section">
                        <h2 class="section-title">数据集概览仪表板</h2>
                        <div class="chart-row">
                            <!-- 数据集规模指标趋势图 -->
                            <div class="chart-col">
                                <div id="dataset-metrics-trend" class="chart-container"></div>
                            </div>
                            <!-- 文本块长度分布图 -->
                            <div class="chart-col">
                                <div id="text-block-length" class="chart-container"></div>
                            </div>
                        </div>
                        <div class="chart-row">
                            <!-- 问题类型分布饼图 -->
                            <div class="chart-col">
                                <div id="question-type-distribution" class="chart-container"></div>
                            </div>
                            <!-- 数据集生成进度和完成率 -->
                            <div class="chart-col">
                                <div id="dataset-progress" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 模型使用统计 -->
                        <div class="chart-container" id="model-usage-stats"></div>
                    </div>

                    <!-- 2. 文本分析可视化 -->
                    <div class="dashboard-section">
                        <h2 class="section-title">文本分析可视化</h2>
                        <div class="chart-row">
                            <!-- 文本块长度分布直方图 -->
                            <div class="chart-col">
                                <div id="text-block-histogram" class="chart-container"></div>
                            </div>
                            <!-- 文本块主题聚类可视化 -->
                            <div class="chart-col">
                                <div id="text-theme-clustering" class="chart-container"></div>
                            </div>
                        </div>
                        <div class="chart-row">
                            <!-- 文本复杂度分析图表 - 句子长度分布 -->
                            <div class="chart-col">
                                <div id="text-complexity-analysis" class="chart-container"></div>
                            </div>
                            <!-- 词汇丰富度分析 -->
                            <div class="chart-col">
                                <div id="vocabulary-richness-chart" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 文本复杂度多维度分析雷达图 -->
                        <div class="chart-container" id="text-complexity-radar"></div>

                        <div class="chart-row">
                            <!-- 领域树结构可视化 -->
                            <div class="chart-col">
                                <div id="domain-tree-chart" class="chart-container"></div>
                            </div>
                            <!-- 领域分布旭日图 -->
                            <div class="chart-col">
                                <div id="domain-sunburst-chart" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 领域径向树 -->
                        <div class="chart-container" id="domain-radial-tree"></div>
                        <div class="chart-row">
                            <!-- 文本块相似度热图 -->
                            <div class="chart-col">
                                <div id="text-relationship-heatmap" class="chart-container"></div>
                            </div>
                            <!-- 文本块关系网络图 -->
                            <div class="chart-col">
                                <div id="text-relationship-chord" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 文本块相似度网络图 -->
                        <div class="chart-container" id="text-similarity-network"></div>
                    </div>

                    <!-- 3. 问题生成分析 -->
                    <div class="dashboard-section">
                        <h2 class="section-title">问题生成分析</h2>
                        <div class="chart-row">
                            <!-- 问题类型分布 -->
                            <div class="chart-col">
                                <div id="question-category-distribution" class="chart-container"></div>
                            </div>
                            <!-- 问题复杂度评分分布图 -->
                            <div class="chart-col">
                                <div id="question-complexity-distribution" class="chart-container"></div>
                            </div>
                        </div>
                        <div class="chart-row">
                            <!-- 问题生成时间趋势图 -->
                            <div class="chart-col">
                                <div id="question-generation-time" class="chart-container"></div>
                            </div>
                            <!-- 问题与文本块的覆盖率分析 -->
                            <div class="chart-col">
                                <div id="question-coverage" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 问题相似度聚类可视化 -->
                        <div class="chart-container" id="question-similarity-clustering"></div>
                    </div>

                    <!-- 4. 答案质量分析 -->
                    <div class="dashboard-section">
                        <h2 class="section-title">答案质量分析</h2>
                        <div class="chart-row">
                            <!-- 答案长度分布图 -->
                            <div class="chart-col">
                                <div id="answer-length-distribution" class="chart-container"></div>
                            </div>
                            <!-- 答案生成时间分布 -->
                            <div class="chart-col">
                                <div id="answer-generation-time" class="chart-container"></div>
                            </div>
                        </div>
                        <div class="chart-row">
                            <!-- 答案质量评分分布 -->
                            <div class="chart-col">
                                <div id="answer-quality-distribution" class="chart-container"></div>
                            </div>
                            <!-- 问答对一致性评分可视化 -->
                            <div class="chart-col">
                                <div id="qa-consistency" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 不同模型生成答案质量对比图表 -->
                        <div class="chart-row">
                            <!-- 模型答案质量雷达图 -->
                            <div class="chart-col">
                                <div id="modelQualityRadarChart" class="chart-container"></div>
                            </div>
                            <!-- 模型答案质量条形图 -->
                            <div class="chart-col">
                                <div id="modelQualityBarChart" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 模型答案质量热力图 -->
                        <div class="chart-container" id="modelQualityHeatmapChart"></div>
                    </div>

                    <!-- 5. 导出数据分析 -->
                    <div class="dashboard-section">
                        <h2 class="section-title">导出数据分析</h2>
                        <div class="chart-row">
                            <!-- 导出数据集规模统计图表 -->
                            <div class="chart-col">
                                <div id="export-dataset-size" class="chart-container"></div>
                            </div>
                            <!-- 数据集格式分布 -->
                            <div class="chart-col">
                                <div id="dataset-format-distribution" class="chart-container"></div>
                            </div>
                        </div>
                        <div class="chart-row">
                            <!-- 导出历史记录和趋势图 -->
                            <div class="chart-col">
                                <div id="export-history" class="chart-container"></div>
                            </div>
                            <!-- 数据集质量评分雷达图 -->
                            <div class="chart-col">
                                <div id="dataset-quality-radar" class="chart-container"></div>
                            </div>
                        </div>
                        <!-- 数据集与行业标准数据集的对比分析 -->
                        <div class="chart-container" id="dataset-comparison"></div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容 -->
            <div class="right-page">
                <!-- 这里将放置右侧的图表内容 -->
                <div class="right-content-placeholder">
                    <div
                        style="height: 3rem; background: rgba(19, 25, 47, 0.6); border-radius: 10px; margin-bottom: 0.25rem;">
                    </div>
                    <div
                        style="height: 3.125rem; background: rgba(19, 25, 47, 0.6); border-radius: 10px; margin-bottom: 0.25rem;">
                    </div>
                    <div style="height: 6rem; background: rgba(19, 25, 47, 0.6); border-radius: 10px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="js/flexible.js"></script>
    <script src="js/datasetMetricsTrend.js"></script>
    <script src="js/textBlockLengthHistogram.js"></script>
    <script src="js/questionTypeDistribution.js"></script>
    <script src="js/datasetProgress.js"></script>
    <script src="js/modelUsageStats.js"></script>
    <script src="js/questionGenerationAnalysis.js"></script>
    <script src="js/answerQualityAnalysis.js"></script>
    <script src="js/modelAnswerQualityComparison.js"></script>
    <script src="js/textThemeClustering.js"></script>
    <script src="js/textComplexityAnalysis.js"></script>
    <script src="js/domainTreeVisualization.js"></script>
    <script src="js/textBlockRelationship.js"></script>
    <script src="js/exportDataAnalysis.js"></script>
    <script src="js/chartResponsive.js"></script>
    <script>
        // 时间更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                weekday: 'long'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        setInterval(updateTime, 1000);
        updateTime();

        // 🔑 恢复原项目的页面滚动行为
        function restorePageScroll() {
            // 确保body和html允许滚动
            document.body.style.overflowX = 'hidden';
            document.body.style.overflowY = 'auto';
            document.documentElement.style.overflowX = 'hidden';
            document.documentElement.style.overflowY = 'auto';
            document.body.style.height = 'auto';
            document.documentElement.style.height = 'auto';

            // 检查页面内容高度
            const bodyHeight = document.body.scrollHeight;
            const windowHeight = window.innerHeight;
            console.log('🔑 页面滚动已恢复');
            console.log('页面内容高度:', bodyHeight, 'px');
            console.log('窗口高度:', windowHeight, 'px');
            console.log('是否需要滚动:', bodyHeight > windowHeight ? '是' : '否');
        }

        document.addEventListener('DOMContentLoaded', function () {
            console.log('🔑 原项目布局 + 现有功能已加载');

            // 恢复页面滚动
            restorePageScroll();

            // 延迟再次应用，确保覆盖任何后续设置
            setTimeout(restorePageScroll, 1000);

            // 初始化响应式功能
            if (typeof initChartResponsive === 'function') {
                initChartResponsive();
            }

            // 🔑 保持所有现有功能，包括地图点击等
            console.log('所有图表和交互功能已保留');
        });
    </script>
</body>

</html>