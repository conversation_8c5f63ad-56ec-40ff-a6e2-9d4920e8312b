"use strict";(()=>{var e={};e.id=4481,e.ids=[4481],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50465:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>w,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>f,staticGenerationAsyncStorage:()=>h});var l={};a.r(l),a.d(l,{POST:()=>d,PUT:()=>b});var r=a(49303),n=a(88716),o=a(60670),i=a(87070),s=a(3800),c=a(49428);async function d(e,{params:t}){try{let{projectId:e}=t;if(!e)return i.NextResponse.json({error:"项目ID不能为空"},{status:400});let a=await (0,s.getTags)(e);if(a&&a.length>0)return i.NextResponse.json({message:"项目已存在标签，无需初始化",tagCount:a.length},{status:200});await (0,s.batchSaveTags)(e,c.I0);let l=await (0,s.getTags)(e);return console.log(`项目 ${e} 默认领域标签初始化成功，共创建 ${l.length} 个标签`),i.NextResponse.json({success:!0,message:"默认领域标签初始化成功",tagCount:l.length},{status:200})}catch(e){return console.error(`项目 ${t.projectId} 默认领域标签初始化失败:`,e),i.NextResponse.json({error:e.message||"默认领域标签初始化失败"},{status:500})}}async function b(e,{params:t}){try{let{projectId:e}=t;if(!e)return i.NextResponse.json({error:"项目ID不能为空"},{status:400});await (0,s.batchSaveTags)(e,c.I0);let a=await (0,s.getTags)(e);return console.log(`项目 ${e} 默认领域标签强制重新初始化成功，共创建 ${a.length} 个标签`),i.NextResponse.json({success:!0,message:"默认领域标签强制重新初始化成功",tagCount:a.length},{status:200})}catch(e){return console.error(`项目 ${t.projectId} 默认领域标签强制重新初始化失败:`,e),i.NextResponse.json({error:e.message||"默认领域标签强制重新初始化失败"},{status:500})}}let u=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/projects/[projectId]/init-tags/route",pathname:"/api/projects/[projectId]/init-tags",filename:"route",bundlePath:"app/api/projects/[projectId]/init-tags/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\[projectId]\\init-tags\\route.js",nextConfigOutput:"",userland:l}),{requestAsyncStorage:p,staticGenerationAsyncStorage:h,serverHooks:f}=u,g="/api/projects/[projectId]/init-tags/route";function w(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:h})}},49428:(e,t,a)=>{a.d(t,{Cs:()=>l,I0:()=>n,dI:()=>r});let l={STATUS:{PROCESSING:0,COMPLETED:1,FAILED:2,CANCELED:3},TYPE:{FILE_PROCESSING:"file-processing",QUESTION_GENERATION:"question-generation",ANSWER_GENERATION:"answer-generation",DATA_DISTILLATION:"data-distillation",DATASET_IMPORT:"dataset-import"}},r={OPERATION_TYPE:{IMPORT:"import",EXPORT:"export",UPLOAD:"upload"},FORMAT:{EXCEL:"excel",JSON:"json",JSONL:"jsonl",CSV:"csv",PDF:"pdf",DOCX:"docx",MARKDOWN:"markdown",TXT:"txt"},STATUS:{PROCESSING:0,SUCCESS:1,FAILED:2,DELETED:3},MIME_TYPES:{excel:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",json:"application/json",jsonl:"application/jsonl",csv:"text/csv",pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",markdown:"text/markdown",txt:"text/plain"}},n=[{label:"社交互动",child:[{label:"日常社交"},{label:"情感交流"},{label:"人际关系"},{label:"社会交往"},{label:"社交礼仪"}]},{label:"日常生活",child:[{label:"居家事务"},{label:"消费购物"},{label:"饮食事务"},{label:"服饰穿戴"},{label:"交通出行"},{label:"时间管理"}]},{label:"学习发展",child:[{label:"正式教育"},{label:"高等教育"},{label:"职业培训"},{label:"自主学习"},{label:"知识获取"}]},{label:"职业工作",child:[{label:"求职就业"},{label:"职场协作"},{label:"商务活动"},{label:"职业技能"},{label:"工作管理"},{label:"职业规划"}]},{label:"休闲娱乐",child:[{label:"文化体验"},{label:"旅行探索"},{label:"运动健康"},{label:"游戏娱乐"},{label:"艺术爱好"},{label:"社交娱乐"}]},{label:"健康医疗",child:[{label:"日常保健"},{label:"疾病预防"},{label:"医疗服务"},{label:"康复护理"},{label:"心理健康"}]},{label:"家庭事务",child:[{label:"婚姻关系"},{label:"育儿教养"},{label:"养老赡养"},{label:"家庭财务"},{label:"家庭规划"}]},{label:"金融财务",child:[{label:"日常理财"},{label:"投资管理"},{label:"借贷信贷"},{label:"保险规划"},{label:"税务处理"}]},{label:"法律事务",child:[{label:"民事纠纷"},{label:"权益保障"},{label:"法律文书"},{label:"法律咨询"},{label:"公共事务"}]},{label:"科技应用",child:[{label:"数码设备"},{label:"网络应用"},{label:"软件工具"},{label:"信息安全"},{label:"新兴技术"}]},{label:"特殊需求",child:[{label:"跨文化沟通"},{label:"无障碍支持"},{label:"应急处理"},{label:"特殊场景"}]},{label:"其他",child:[]}]},4342:(e,t,a)=>{a.d(t,{db:()=>r});var l=a(53524);let r=globalThis.prisma||new l.PrismaClient({log:["error"]})},3800:(e,t,a)=>{a.r(t),a.d(t,{batchSaveTags:()=>f,createTag:()=>c,deleteTag:()=>b,getTags:()=>o,updateTag:()=>d});var l=a(24330);a(60166);var r=a(4342),n=a(50121);async function o(e){try{let t=await i(e);return(0,n.h)(t)}catch(e){return[]}}async function i(e,t=null){let a=await r.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of a){let a=await s(t.id);t.questionCount=await r.db.questions.count({where:{label:{in:a},projectId:e}}),t.child=await i(e,t.id)}return a}async function s(e){let t=[],a=[e];for(;a.length>0;){let e=a.shift(),l=await r.db.tags.findUnique({where:{id:e}});if(l){t.push(l.label);let n=await r.db.tags.findMany({where:{parentId:e},select:{id:!0}});a.push(...n.map(e=>e.id))}}return t}async function c(e,t,a){try{let l=await r.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(l)return console.log(`标签已存在: ${t}，返回现有标签`),l;let n={projectId:e,label:t};return a&&(n.parentId=a),await r.db.tags.create({data:n})}catch(l){if("P2002"===l.code&&l.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let l=await r.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(l)return l}throw console.error("Error insert tags db:",l),l}}async function d(e,t){try{let a=await r.db.tags.findUnique({where:{id:t}});if(!a)throw Error(`标签不存在: ${t}`);let l=a.label,n=a.projectId,o=await r.db.tags.update({where:{id:t},data:{label:e}});return l!==e&&(console.log(`标签名称从 "${l}" 更新为 "${e}"，开始同步更新相关数据`),await r.db.questions.updateMany({where:{label:l,projectId:n},data:{label:e}}),console.log(`已更新问题表中的标签: ${l} -> ${e}`),await r.db.datasets.updateMany({where:{questionLabel:l,projectId:n},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${l} -> ${e}`)),o}catch(e){throw console.error("Error update tags db:",e),e}}async function b(e){try{console.log(`开始删除标签: ${e}`);let t=await r.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let a=await u(e,t.projectId);for(let e of(console.log(`找到 ${a.length} 个子标签需要删除`),a.reverse()))await h(e.label,e.projectId),await p(e.label,e.projectId),await r.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await h(t.label,t.projectId),await p(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await r.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function u(e,t){let a=[];async function l(e){let n=await r.db.tags.findMany({where:{parentId:e,projectId:t}});if(n.length>0)for(let e of(a.push(...n),n))await l(e.id)}return await l(e),a}async function p(e,t){try{await r.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function h(e,t){try{await r.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function f(e,t){try{await r.db.tags.deleteMany({where:{projectId:e}}),await g(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function g(e,t,a=null){for(let l of t){let t=await r.db.tags.create({data:{projectId:e,label:l.label,parentId:a}});l.child&&l.child.length>0&&await g(e,l.child,t.id)}}(0,a(40618).h)([o,c,d,b,f]),(0,l.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",o),(0,l.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",c),(0,l.j)("745145798d6802bd295e673536ce529c25576c3c",d),(0,l.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",b),(0,l.j)("1e89c922534cbdd6a59e5783551aafd799be4734",f)},50121:(e,t,a)=>{a.d(t,{E:()=>l,h:()=>function e(t,a=0,l=0){return Array.isArray(t)?t.map((t,r)=>{let n;if(0===a){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let a=Math.floor(e/10),l=e%10;return 1===a?0===l?"十":"十"+t[l]:t[a]+"十"+(0===l?"":t[l])}(r+1);n=`${e}、${t.label}`}else n=`${l+1}.${r+1} ${t.label}`;let o={...t,displayLabel:n,displayName:t.label};return t.child&&t.child.length>0&&(o.child=e(t.child,a+1,r)),t.children&&t.children.length>0&&(o.children=e(t.children,a+1,r)),o}):[]}});function l(e,t){return e&&Array.isArray(t)?function t(a){for(let l of a){if(l.label===e)return l;if(l.child&&l.child.length>0){let e=t(l.child);if(e)return e}}return null}(t):null}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),l=t.X(0,[8948,5972,8341],()=>a(50465));module.exports=l})();