{"/_not-found/page": "app/_not-found/page.js", "/api/llm/fetch-models/route": "app/api/llm/fetch-models/route.js", "/api/llm/providers/route": "app/api/llm/providers/route.js", "/api/check-update/route": "app/api/check-update/route.js", "/api/llm/model/route": "app/api/llm/model/route.js", "/api/projects/[projectId]/chunks/[chunkId]/questions/route": "app/api/projects/[projectId]/chunks/[chunkId]/questions/route.js", "/api/projects/[projectId]/batch-generateGA/route": "app/api/projects/[projectId]/batch-generateGA/route.js", "/api/projects/[projectId]/chunks/[chunkId]/route": "app/api/projects/[projectId]/chunks/[chunkId]/route.js", "/api/projects/[projectId]/chunks/batch-edit/route": "app/api/projects/[projectId]/chunks/batch-edit/route.js", "/api/projects/[projectId]/chunks/route": "app/api/projects/[projectId]/chunks/route.js", "/api/projects/[projectId]/config/route": "app/api/projects/[projectId]/config/route.js", "/api/projects/[projectId]/chunks/name/route": "app/api/projects/[projectId]/chunks/name/route.js", "/api/projects/[projectId]/custom-split/route": "app/api/projects/[projectId]/custom-split/route.js", "/api/projects/[projectId]/datasets/[datasetId]/token-count/route": "app/api/projects/[projectId]/datasets/[datasetId]/token-count/route.js", "/api/projects/[projectId]/datasets/[datasetId]/route": "app/api/projects/[projectId]/datasets/[datasetId]/route.js", "/api/projects/[projectId]/datasets/batch-auto-tag/route": "app/api/projects/[projectId]/datasets/batch-auto-tag/route.js", "/api/projects/[projectId]/datasets/route": "app/api/projects/[projectId]/datasets/route.js", "/api/projects/[projectId]/distill/questions/by-tag/route": "app/api/projects/[projectId]/distill/questions/by-tag/route.js", "/api/projects/[projectId]/datasets/import/route": "app/api/projects/[projectId]/datasets/import/route.js", "/api/projects/[projectId]/distill/tags/route": "app/api/projects/[projectId]/distill/tags/route.js", "/api/projects/[projectId]/datasets/optimize/route": "app/api/projects/[projectId]/datasets/optimize/route.js", "/api/projects/[projectId]/distill/questions/route": "app/api/projects/[projectId]/distill/questions/route.js", "/api/projects/[projectId]/file-records/route": "app/api/projects/[projectId]/file-records/route.js", "/api/projects/[projectId]/files/[fileId]/ga-pairs/route": "app/api/projects/[projectId]/files/[fileId]/ga-pairs/route.js", "/api/projects/[projectId]/generate-questions/route": "app/api/projects/[projectId]/generate-questions/route.js", "/api/projects/[projectId]/llamaFactory/checkConfig/route": "app/api/projects/[projectId]/llamaFactory/checkConfig/route.js", "/api/projects/[projectId]/init-tags/route": "app/api/projects/[projectId]/init-tags/route.js", "/api/projects/[projectId]/model-config/[modelConfigId]/route": "app/api/projects/[projectId]/model-config/[modelConfigId]/route.js", "/api/projects/[projectId]/huggingface/upload/route": "app/api/projects/[projectId]/huggingface/upload/route.js", "/api/projects/[projectId]/model-config/route": "app/api/projects/[projectId]/model-config/route.js", "/api/projects/[projectId]/llamaFactory/generate/route": "app/api/projects/[projectId]/llamaFactory/generate/route.js", "/api/projects/[projectId]/models/[modelId]/route": "app/api/projects/[projectId]/models/[modelId]/route.js", "/api/projects/[projectId]/models/route": "app/api/projects/[projectId]/models/route.js", "/api/projects/[projectId]/playground/chat/route": "app/api/projects/[projectId]/playground/chat/route.js", "/api/projects/[projectId]/questions/[questionId]/route": "app/api/projects/[projectId]/questions/[questionId]/route.js", "/api/projects/[projectId]/playground/chat/stream/route": "app/api/projects/[projectId]/playground/chat/stream/route.js", "/api/projects/[projectId]/questions/tree/route": "app/api/projects/[projectId]/questions/tree/route.js", "/api/projects/[projectId]/preview/[fileId]/route": "app/api/projects/[projectId]/preview/[fileId]/route.js", "/api/projects/[projectId]/questions/batch-delete/route": "app/api/projects/[projectId]/questions/batch-delete/route.js", "/api/projects/[projectId]/route": "app/api/projects/[projectId]/route.js", "/api/projects/[projectId]/questions/route": "app/api/projects/[projectId]/questions/route.js", "/api/projects/[projectId]/split/route": "app/api/projects/[projectId]/split/route.js", "/api/projects/[projectId]/tasks/list/route": "app/api/projects/[projectId]/tasks/list/route.js", "/api/projects/[projectId]/statistics/route": "app/api/projects/[projectId]/statistics/route.js", "/api/projects/[projectId]/tasks/route": "app/api/projects/[projectId]/tasks/route.js", "/api/projects/[projectId]/tasks/[taskId]/route": "app/api/projects/[projectId]/tasks/[taskId]/route.js", "/api/projects/[projectId]/tags/route": "app/api/projects/[projectId]/tags/route.js", "/api/projects/batch-init-tags/route": "app/api/projects/batch-init-tags/route.js", "/api/projects/delete-directory/route": "app/api/projects/delete-directory/route.js", "/api/projects/open-directory/route": "app/api/projects/open-directory/route.js", "/api/projects/migrate/route": "app/api/projects/migrate/route.js", "/api/projects/unmigrated/route": "app/api/projects/unmigrated/route.js", "/api/update/route": "app/api/update/route.js", "/page": "app/page.js", "/big-screen/page": "app/big-screen/page.js", "/dataset-square/page": "app/dataset-square/page.js", "/projects/page": "app/projects/page.js", "/api/llm/ollama/models/route": "app/api/llm/ollama/models/route.js", "/api/projects/[projectId]/datasets/export/route": "app/api/projects/[projectId]/datasets/export/route.js", "/api/projects/[projectId]/distill/tags/all/route": "app/api/projects/[projectId]/distill/tags/all/route.js", "/api/projects/[projectId]/files/route": "app/api/projects/[projectId]/files/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/statistics/dashboard/route": "app/api/statistics/dashboard/route.js", "/api/statistics/global/route": "app/api/statistics/global/route.js", "/api/statistics/file-records/route": "app/api/statistics/file-records/route.js", "/api/statistics/timeline/route": "app/api/statistics/timeline/route.js", "/projects/[projectId]/datasets/page": "app/projects/[projectId]/datasets/page.js", "/projects/[projectId]/distill/page": "app/projects/[projectId]/distill/page.js", "/projects/[projectId]/page": "app/projects/[projectId]/page.js", "/projects/[projectId]/datasets/[datasetId]/page": "app/projects/[projectId]/datasets/[datasetId]/page.js", "/projects/[projectId]/playground/page": "app/projects/[projectId]/playground/page.js", "/projects/[projectId]/text-split/page": "app/projects/[projectId]/text-split/page.js", "/projects/[projectId]/questions/page": "app/projects/[projectId]/questions/page.js", "/projects/[projectId]/settings/page": "app/projects/[projectId]/settings/page.js", "/projects/[projectId]/tasks/page": "app/projects/[projectId]/tasks/page.js"}