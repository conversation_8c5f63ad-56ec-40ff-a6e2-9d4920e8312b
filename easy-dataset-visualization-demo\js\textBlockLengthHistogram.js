/**
 * 文本块长度分布直方图
 * 可视化文本块长度的分布（字符计数或 token 计数）
 */

// 生成表示文本块长度的虚拟数据
function generateTextBlockLengthData(count = 200) {
    const data = [];
    
    // 生成不同长度的文本块数据
    // 使用正态分布来模拟更真实的数据分布
    const mean = 500; // 平均字符数
    const stdDev = 200; // 标准差
    
    for (let i = 0; i < count; i++) {
        // 使用Box-Muller变换生成正态分布的随机数
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();
        let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        
        // 将正态分布的随机数转换为文本块长度
        let length = Math.round(z * stdDev + mean);
        
        // 确保长度为正数
        length = Math.max(50, length);
        
        data.push(length);
    }
    
    return data;
}

// 初始化ECharts直方图
function initTextBlockLengthHistogram(containerId, title, colorScheme) {
    // 获取图表容器
    const chartContainer = document.getElementById(containerId);
    if (!chartContainer) {
        console.error(`找不到图表容器：${containerId}`);
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 显示加载状态
    showChartLoading(containerId);
    
    // 生成数据
    const textBlockLengths = generateTextBlockLengthData();
    
    // 计算数据的最小值和最大值，用于确定直方图的范围
    const minLength = Math.min(...textBlockLengths);
    const maxLength = Math.max(...textBlockLengths);
    
    // 设置直方图的分箱数量
    const binCount = 20;
    const binSize = Math.ceil((maxLength - minLength) / binCount);
    
    // 创建分箱
    const bins = Array(binCount).fill(0);
    const binLabels = [];
    
    // 计算每个分箱的范围标签
    for (let i = 0; i < binCount; i++) {
        const start = minLength + i * binSize;
        const end = start + binSize - 1;
        binLabels.push(`${start}-${end}`);
    }
    
    // 将数据分配到各个分箱
    textBlockLengths.forEach(length => {
        const binIndex = Math.min(
            Math.floor((length - minLength) / binSize),
            binCount - 1
        );
        bins[binIndex]++;
    });
    
    // 设置颜色方案
    const color = colorScheme || '#5470c6';
    const emphasisColor = colorScheme === '#91cc75' ? '#6da350' : '#3a56b4';
    
    // 配置图表选项
    const option = {
        title: {
            text: title || '文本块长度分布',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const range = params.name.split('-');
                return `文本块长度: ${params.name} 字符<br/>数量: ${params.value}`;
            }
        },
        xAxis: {
            type: 'category',
            data: binLabels,
            name: '字符数',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                rotate: 45,
                interval: Math.ceil(binCount / 10)
            }
        },
        yAxis: {
            type: 'value',
            name: '文本块数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '文本块数量',
                type: 'bar',
                data: bins,
                itemStyle: {
                    color: color
                },
                emphasis: {
                    itemStyle: {
                        color: emphasisColor
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 应用统一主题
    applyChartTheme(chart);
    
    // 隐藏加载状态
    hideChartLoading(containerId);
    
    // 注册图表实例以便响应式调整
    registerChart(chart);
    
    return chart;
}

// 在文档加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    // 初始化数据集概览部分的文本块长度直方图
    initTextBlockLengthHistogram('text-block-length', '文本块长度分布概览 - 直方图 (bar)', '#5470c6');
    
    // 初始化文本分析部分的文本块长度直方图
    // 使用不同的标题和颜色方案以区分
    initTextBlockLengthHistogram('text-block-histogram', '文本块长度详细分布 - 直方图 (bar)', '#91cc75');
}); 