/**
 * 获取简化版添加标签的提示词 - 直接返回JSON格式
 * @param {string} tags 标签列表JSON字符串
 * @param {string} question 单个问题
 * @param {string} domainTreePrompt 领域树提示词
 * @param {boolean} isReasoningModel 是否为推理模型
 * @returns {Array} 提示词数组
 */
function getAddLabelSimplePrompt(tags, question, domainTreePrompt, isReasoningModel = false) {
    const systemContent = `你是一个专业的问题分类专家，擅长将问题归类到合适的领域标签。
请根据提供的领域标签体系，为问题选择最合适的子标签。

领域标签体系：
${domainTreePrompt || ''}

可用标签：
${tags}

输出要求：
1. 必须返回有效的JSON格式
2. 必须选择最具体的子标签，不要返回父级标签
3. 如果现有标签中没有合适的，必须创建新的合适标签：
   - 对于有明确主题的问题，创建具体的子标签（如："日常社交"、"编程学习"）
   - 对于模糊或无意义的问题，创建"其他"分类下的子标签（如："无意义表达"、"模糊询问"）
   - 避免直接使用"未分类"，而是根据问题特征创建更具体的分类
   - 创建的标签名称不要包含序号，只使用纯标签名称
${isReasoningModel ?
            '4. 由于你是推理模型，请在reasoning字段中提供详细的思维过程，说明为什么选择或创建该标签' :
            '4. 在reasoning字段中简要说明选择或创建该标签的原因'}

输出JSON格式：
{
  "parentTag": "父级标签名称（如：社交互动）",
  "childTag": "子级标签名称（如：日常社交）",
  "reasoning": "${isReasoningModel ? '详细的推理过程和思维链' : '选择该标签的简要原因'}"
}`;

    const userContent = `请为以下问题选择最合适的领域标签：

问题：${question}

请严格按照JSON格式返回结果，不要包含任何其他文本。`;

    return [
        {
            role: 'system',
            content: systemContent
        },
        {
            role: 'user',
            content: userContent
        }
    ];
}

export { getAddLabelSimplePrompt };