/**
 * LLM API 统一调用工具类
 * 支持多种模型提供商：OpenAI、Ollama、智谱AI等
 * 支持普通输出和流式输出
 */
import { DEFAULT_MODEL_SETTINGS } from '@/constant/model';
import { extractThinkChain, extractAnswer } from '@/lib/llm/common/util';
const OllamaClient = require('./providers/ollama'); // 导入 OllamaClient
const OpenAIClient = require('./providers/openai'); // 导入 OpenAIClient
const ZhiPuClient = require('./providers/zhipu'); // 导入 ZhiPuClient
const OpenRouterClient = require('./providers/openrouter');

class LLMClient {
  /**
   * 创建 LLM 客户端实例
   * @param {Object} config - 配置信息
   * @param {string} config.provider - 提供商名称，如 'openai', 'ollama', 'zhipu' 等
   * @param {string} config.endpoint - API 端点，如 'https://api.openai.com/v1/'
   * @param {string} config.apiKey - API 密钥（如果需要）
   * @param {string} config.model - 模型名称，如 'gpt-3.5-turbo', 'llama2' 等
   * @param {number} config.temperature - 温度参数
   */
  constructor(config = {}) {
    this.config = {
      provider: config.providerId || 'openai',
      endpoint: this._handleEndpoint(config.providerId, config.endpoint) || '',
      apiKey: config.apiKey || '',
      model: config.modelName || '',
      temperature: config.temperature || DEFAULT_MODEL_SETTINGS.temperature,
      maxTokens: config.maxTokens || DEFAULT_MODEL_SETTINGS.maxTokens,
      max_tokens: config.maxTokens || DEFAULT_MODEL_SETTINGS.maxTokens
    };
    if (config.topP !== 0) {
      this.config.topP = config.topP;
    }
    if (config.topK !== 0) {
      this.config.topK = config.topK;
    }

    this.client = this._createClient(this.config.provider, this.config);
  }

  /**
   * 兼容之前版本的用户配置
   */
  _handleEndpoint(provider, endpoint) {
    if (!endpoint) return '';

    // 针对Ollama的特殊处理
    if (provider && provider.toLowerCase() === 'ollama') {
      // 标准化Ollama端点
      if (!endpoint.includes('http')) {
        // 如果没有协议，默认使用http
        endpoint = `http://${endpoint}`;
      }

      // 移除多余的api路径
      if (endpoint.endsWith('/api')) {
        endpoint = endpoint.slice(0, -4);
      }
      if (endpoint.endsWith('/api/')) {
        endpoint = endpoint.slice(0, -5);
      }

      // 确保端点以/结尾
      if (!endpoint.endsWith('/')) {
        endpoint = `${endpoint}/`;
      }

      console.log('处理后的Ollama端点:', endpoint);
      return endpoint;
    }

    // 其他提供商的处理
    if (endpoint.includes('/chat/completions')) {
      return endpoint.replace('/chat/completions', '');
    }
    return endpoint;
  }

  _createClient(provider, config) {
    const clientMap = {
      ollama: OllamaClient,
      openai: OpenAIClient,
      siliconflow: OpenAIClient,
      deepseek: OpenAIClient,
      zhipu: ZhiPuClient,
      openrouter: OpenRouterClient
    };
    const ClientClass = clientMap[provider.toLowerCase()] || OpenAIClient;
    return new ClientClass(config);
  }

  async _callClientMethod(method, ...args) {
    try {
      return await this.client[method](...args);
    } catch (error) {
      console.error(`${this.config.provider} API 调用出错:`, error);
      throw error;
    }
  }

  /**
   * 生成对话响应
   * @param {string|Array} prompt - 用户输入的提示词或对话历史
   * @param {Object} options - 可选参数
   * @returns {Promise<Object>} 返回模型响应
   */
  async chat(prompt, options = {}) {
    const messages = Array.isArray(prompt) ? prompt : [{ role: 'user', content: prompt }];
    options = {
      ...options,
      ...this.config
    };
    return this._callClientMethod('chat', messages, options);
  }

  /**
   * 流式生成对话响应
   * @param {string|Array} prompt - 用户输入的提示词或对话历史
   * @param {Object} options - 可选参数
   * @returns {ReadableStream} 返回可读流
   */
  /**
   * 纯API流式生成对话响应
   * @param {string|Array} prompt - 用户输入的提示词或对话历史
   * @param {Object} options - 可选参数
   * @returns {Response} 返回原生Response对象
   */
  async chatStreamAPI(prompt, options = {}) {
    const messages = Array.isArray(prompt) ? prompt : [{ role: 'user', content: prompt }];
    options = {
      ...options,
      ...this.config
    };
    return this._callClientMethod('chatStreamAPI', messages, options);
  }

  /**
   * 流式生成对话响应
   * @param {string|Array} prompt - 用户输入的提示词或对话历史
   * @param {Object} options - 可选参数
   * @returns {ReadableStream} 返回可读流
   */
  async chatStream(prompt, options = {}) {
    const messages = Array.isArray(prompt) ? prompt : [{ role: 'user', content: prompt }];
    options = {
      ...options,
      ...this.config
    };
    return this._callClientMethod('chatStream', messages, options);
  }

  // 获取模型响应
  async getResponse(prompt, options = {}) {
    try {
      const llmRes = await this.chat(prompt, options);
      console.log('LLM原始响应:', JSON.stringify(llmRes));

      // 处理不同类型的响应
      if (typeof llmRes === 'string') {
        return llmRes;
      } else if (llmRes && typeof llmRes === 'object') {
        // 优先尝试从text字段获取
        if (llmRes.text && typeof llmRes.text === 'string') {
          return llmRes.text;
        }

        // 尝试从response.messages获取
        if (llmRes.response && llmRes.response.messages) {
          if (Array.isArray(llmRes.response.messages) && llmRes.response.messages.length > 0) {
            // 尝试找到assistant的消息
            const assistantMsg = llmRes.response.messages.find(msg =>
                msg.role === 'assistant' && typeof msg.content === 'string'
            );
            if (assistantMsg) {
              return assistantMsg.content;
            }

            // 如果没有找到assistant消息，使用最后一条消息
            const lastMsg = llmRes.response.messages[llmRes.response.messages.length - 1];
            if (lastMsg && typeof lastMsg.content === 'string') {
              return lastMsg.content;
            }
          } else if (typeof llmRes.response.messages === 'string') {
            return llmRes.response.messages;
          }
        }

        // 如果以上都失败，尝试将整个对象转为JSON字符串
        console.log('无法从LLM响应中提取字符串，尝试转换整个对象');
        return JSON.stringify(llmRes);
      }

      // 如果上述所有尝试都失败，返回空字符串
      console.warn('LLM响应格式不正确，无法提取有效内容');
      return '';
    } catch (error) {
      console.error('获取LLM响应时出错:', error);
      return '';
    }
  }

  async getResponseWithCOT(prompt, options = {}) {
    const llmRes = await this.chat(prompt, options);
    let answer = llmRes.text || '';
    let cot = llmRes.reasoning || '';
    if ((answer && answer.startsWith('<think>')) || answer.startsWith('<thinking>')) {
      cot = extractThinkChain(answer);
      answer = extractAnswer(answer);
    } else if (
        llmRes?.response?.body?.choices?.length > 0 &&
        llmRes.response.body.choices[0].message.reasoning_content
    ) {
      if (llmRes.response.body.choices[0].message.reasoning_content) {
        cot = llmRes.response.body.choices[0].message.reasoning_content;
      }
      if (llmRes.response.body.choices[0].message.content) {
        answer = llmRes.response.body.choices[0].message.content;
      }
    }
    if (answer.startsWith('\n\n')) {
      answer = answer.slice(2);
    }
    if (cot.endsWith('\n\n')) {
      cot = cot.slice(0, -2);
    }
    return { answer, cot };
  }
}

module.exports = LLMClient;
