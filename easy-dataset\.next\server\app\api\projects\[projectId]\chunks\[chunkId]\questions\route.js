(()=>{var e={};e.id=7195,e.ids=[7195],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92048:e=>{"use strict";e.exports=require("fs")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},6005:e=>{"use strict";e.exports=require("node:crypto")},53814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>b,routeModule:()=>f,serverHooks:()=>w,staticGenerationAsyncStorage:()=>p});var a={};r.r(a),r.d(a,{GET:()=>h,POST:()=>u});var n=r(49303),o=r(88716),c=r(60670),i=r(87070),s=r(3359),d=r(7663),l=r(40249);async function u(e,{params:t}){try{let{projectId:r,chunkId:a}=t;if(!r||!a)return i.NextResponse.json({error:"Project ID or text block ID cannot be empty"},{status:400});let{model:n,language:o="中文",number:c,enableGaExpansion:s=!1}=await e.json();if(!n)return i.NextResponse.json({error:"Model cannot be empty"},{status:400});let d=l.ZP.generateQuestionsForChunkWithGA,u=await d(r,a,{model:n,language:o,number:c,enableGaExpansion:s}),h={chunkId:a,questions:u.questions||u.labelQuestions||[],total:u.total||(u.questions||u.labelQuestions||[]).length,gaExpansionUsed:u.gaExpansionUsed||!1,gaPairsCount:u.gaPairsCount||0,expectedTotal:u.expectedTotal||u.total};return i.NextResponse.json(h)}catch(e){return d.Z.error("Error generating questions:",e),i.NextResponse.json({error:e.message||"Error generating questions"},{status:500})}}async function h(e,{params:t}){try{let{projectId:e,chunkId:r}=t;if(!e||!r)return i.NextResponse.json({error:"The item ID or text block ID cannot be empty"},{status:400});let a=await (0,s.AW)(e,r);return i.NextResponse.json({chunkId:r,questions:a,total:a.length})}catch(e){return console.error("Error getting questions:",String(e)),i.NextResponse.json({error:e.message||"Error getting questions"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/projects/[projectId]/chunks/[chunkId]/questions/route",pathname:"/api/projects/[projectId]/chunks/[chunkId]/questions",filename:"route",bundlePath:"app/api/projects/[projectId]/chunks/[chunkId]/questions/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\[projectId]\\chunks\\[chunkId]\\questions\\route.js",nextConfigOutput:"",userland:a}),{requestAsyncStorage:b,staticGenerationAsyncStorage:p,serverHooks:w}=f,y="/api/projects/[projectId]/chunks/[chunkId]/questions/route";function g(){return(0,c.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:p})}},67565:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ensureDbExists:()=>f,ensureDir:()=>w,getProjectRoot:()=>h,readJsonFile:()=>b,writeJsonFile:()=>p});var a=r(24330);r(60166);var n=r(92048),o=r.n(n),c=r(55315),i=r.n(c),s=r(19801),d=r.n(s),l=r(40618);let u="";async function h(){return u||(u=function(){if(process.resourcesPath){let e=String(o().readFileSync(i().join(process.resourcesPath,"root-path.txt")));if(e)return e}if(!process.versions||!process.versions.electron)return i().join(process.cwd(),"local-db");try{let{app:e}=r(66616);return i().join(e.getPath("userData"),"local-db")}catch(e){return console.error("Failed to get user data directory:",String(e),i().join(d().homedir(),".easy-dataset-db")),i().join(d().homedir(),".easy-dataset-db")}}()),u}async function f(){try{await o().promises.access(u)}catch(e){await o().promises.mkdir(u,{recursive:!0})}}async function b(e){try{await o().promises.access(e);let t=await o().promises.readFile(e,"utf8");return JSON.parse(t)}catch(e){return null}}async function p(e,t){let r=`${e}_${Date.now()}.tmp`;try{let a=JSON.stringify(t,null,2);await o().promises.writeFile(r,a,"utf8");try{let t=await o().promises.readFile(r,"utf8");JSON.parse(t),await o().promises.rename(r,e)}catch(e){throw await o().promises.unlink(r).catch(()=>{}),Error(`写入的JSON文件内容无效: ${e.message}`)}return t}catch(t){throw console.error(`写入JSON文件 ${e} 失败:`,t),t}finally{await o().promises.unlink(r).catch(()=>{})}}async function w(e){try{await o().promises.access(e)}catch(t){await o().promises.mkdir(e,{recursive:!0})}}(0,l.h)([h,f,b,p,w]),(0,a.j)("62c7fabcd2e3ee7caf7f462f293b892cc0b14386",h),(0,a.j)("ded1554cc91f298bb2ed3c9cb64c452d8ad4653b",f),(0,a.j)("88f51ddb916fc05f7c693302a1f7cc717fe6b538",b),(0,a.j)("7f894c53dded7fe7d9466fff5abe1a69b2db2354",p),(0,a.j)("332c9473f7cbbb0bc5ae43d3adcf4fc93f5d23bd",w)},1491:(e,t,r)=>{"use strict";r.d(t,{B3:()=>h,Ct:()=>b,NP:()=>u,Uj:()=>w,W8:()=>f,YS:()=>l,dO:()=>p,r5:()=>y});var a=r(24330);r(60166);var n=r(4342),o=r(67565),c=r(55315),i=r.n(c),s=r(92048),d=r.n(s);async function l(e){try{return await n.db.chunks.createMany({data:e})}catch(e){throw console.error("Failed to create chunks in database"),e}}async function u(e){try{return await n.db.chunks.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get chunks by id in database"),e}}async function h(e){try{return await n.db.chunks.findMany({where:{fileId:{in:e}},include:{Questions:{select:{question:!0}}}})}catch(e){throw console.error("Failed to get chunks by id in database"),e}}async function f(e,t){try{let r={projectId:e,NOT:{name:{contains:"Distilled Content"}}};return"generated"===t?r.Questions={some:{}}:"ungenerated"===t&&(r.Questions={none:{}}),await n.db.chunks.findMany({where:r,include:{Questions:{select:{question:!0}}}})}catch(e){throw console.error("Failed to get chunks by projectId in database"),e}}async function b(e){try{let t=n.db.questions.deleteMany({where:{chunkId:e}}),r=n.db.chunks.delete({where:{id:e}});return await n.db.$transaction([t,r])}catch(e){throw console.error("Failed to delete chunks by id in database"),e}}async function p(e,t){try{return await n.db.chunks.findFirst({where:{projectId:e,name:t}})}catch(e){throw console.error("根据名称获取文本块失败",e),e}}async function w(e,t){try{let r=(await n.db.chunks.findMany({where:{projectId:e,fileId:t},select:{id:!0}})).map(e=>e.id);if(0===r.length)return{count:0};let a=n.db.questions.deleteMany({where:{chunkId:{in:r}}}),o=n.db.chunks.deleteMany({where:{id:{in:r}}});return{count:(await n.db.$transaction([a,o]))[1].count}}catch(e){throw console.error("删除文件相关文本块失败:",e),e}}async function y(e,t){try{return await n.db.chunks.update({where:{id:e},data:t})}catch(e){throw console.error("Failed to update chunks by id in database"),e}}async function g(e,t){try{let r=await (0,o.getProjectRoot)(),a=i().join(r,e),n=i().join(a,"files"),c=i().join(a,"toc");await (0,o.ensureDir)(c);let s=i().join(n,t);try{await d().promises.access(s),await d().promises.unlink(s)}catch(e){console.error(`删除文件 ${t} 失败:`,e)}let l=i().basename(t,i().extname(t)),u=i().join(n,`${l}-toc.json`);try{await d().promises.access(u),await d().promises.unlink(u)}catch(e){}}catch(e){throw console.error("Failed to delete chunks by id in database"),e}}(0,r(40618).h)([l,u,h,f,b,p,w,y,g]),(0,a.j)("08c1f7f5ec4a4e19c30f1a39cc46bcd8a070be93",l),(0,a.j)("cff9742417fbdcc46057ec8361f8cf081ae0a55e",u),(0,a.j)("db294f462f272e04356bddeb4f34d8e48a38cd93",h),(0,a.j)("7316a51463dc73091c2ac5c2d40210dd09b61dca",f),(0,a.j)("33c59039c28443822a2fd0e95e41c26f4d2f168b",b),(0,a.j)("cd8a21986d94843d73de6dba2a6c9cda06c1c620",p),(0,a.j)("7c02e7419ad31524d3285cdc239c9c7267132dc4",w),(0,a.j)("709d11e53c6c003c45b054f0cbe187df86112873",y),(0,a.j)("1b26c0537332a71c5f754cddb6a7054e93ae4d67",g)},4342:(e,t,r)=>{"use strict";r.d(t,{db:()=>n});var a=r(53524);let n=globalThis.prisma||new a.PrismaClient({log:["error"]})},6142:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createProject:()=>h,deleteProject:()=>y,getProject:()=>p,getProjects:()=>b,getTaskConfig:()=>g,isExistByName:()=>f,updateProject:()=>w});var a=r(24330);r(60166);var n=r(92048),o=r.n(n),c=r(55315),i=r.n(c),s=r(67565);let d={textSplitMinLength:1500,textSplitMaxLength:2e3,questionGenerationLength:240,questionMaskRemovingProbability:60,huggingfaceToken:"",concurrencyLimit:5,visionConcurrencyLimit:5};var l=r(4342),u=r(42549);async function h(e){try{let t=(0,u.x0)(12),r=await (0,s.getProjectRoot)(),a=i().join(r,t);return await o().promises.mkdir(a,{recursive:!0}),await o().promises.mkdir(i().join(a,"files"),{recursive:!0}),await l.db.projects.create({data:{id:t,name:e.name,description:e.description,country:e.country}})}catch(e){throw console.error("Failed to create project in database"),e}}async function f(e){try{return await l.db.projects.count({where:{name:e}})>0}catch(e){throw console.error("Failed to get project by name in database"),e}}async function b(){try{return await l.db.projects.findMany({include:{_count:{select:{Datasets:!0,Questions:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get projects in database"),e}}async function p(e){try{return await l.db.projects.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get project by id in database"),e}}async function w(e,t){try{return delete t.projectId,await l.db.projects.update({where:{id:e},data:{...t}})}catch(e){throw console.error("Failed to update project in database"),e}}async function y(e){try{let t=await (0,s.getProjectRoot)(),r=i().join(t,e);return await l.db.projects.delete({where:{id:e}}),o().existsSync(r)&&await o().promises.rm(r,{recursive:!0}),!0}catch(e){return!1}}async function g(e){let t=await (0,s.getProjectRoot)(),r=i().join(t,e),a=i().join(r,"task-config.json");return await (0,s.readJsonFile)(a)||d}(0,r(40618).h)([h,f,b,p,w,y,g]),(0,a.j)("735bd40bd5e797892e593b8fddc2605b8d39c8fd",h),(0,a.j)("bd403a39d7d466f99a4386bb75622a1fd55c733f",f),(0,a.j)("2680d512486ca803a14da96551e1a84226778673",b),(0,a.j)("f9f198a77b5228a5545ccb8d1ddbac6437c363d5",p),(0,a.j)("a20b40853cd9266fb771e0109a9df77229473516",w),(0,a.j)("ed0d857f38cd63a93b7768748cc0f7b89e8627b9",y),(0,a.j)("8e0c652d3cc60dd97f2d2b56a785bf2d4b1ab3a5",g)},3800:(e,t,r)=>{"use strict";r.r(t),r.d(t,{batchSaveTags:()=>p,createTag:()=>d,deleteTag:()=>u,getTags:()=>c,updateTag:()=>l});var a=r(24330);r(60166);var n=r(4342),o=r(50121);async function c(e){try{let t=await i(e);return(0,o.h)(t)}catch(e){return[]}}async function i(e,t=null){let r=await n.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of r){let r=await s(t.id);t.questionCount=await n.db.questions.count({where:{label:{in:r},projectId:e}}),t.child=await i(e,t.id)}return r}async function s(e){let t=[],r=[e];for(;r.length>0;){let e=r.shift(),a=await n.db.tags.findUnique({where:{id:e}});if(a){t.push(a.label);let o=await n.db.tags.findMany({where:{parentId:e},select:{id:!0}});r.push(...o.map(e=>e.id))}}return t}async function d(e,t,r){try{let a=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:r||null}});if(a)return console.log(`标签已存在: ${t}，返回现有标签`),a;let o={projectId:e,label:t};return r&&(o.parentId=r),await n.db.tags.create({data:o})}catch(a){if("P2002"===a.code&&a.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let a=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:r||null}});if(a)return a}throw console.error("Error insert tags db:",a),a}}async function l(e,t){try{let r=await n.db.tags.findUnique({where:{id:t}});if(!r)throw Error(`标签不存在: ${t}`);let a=r.label,o=r.projectId,c=await n.db.tags.update({where:{id:t},data:{label:e}});return a!==e&&(console.log(`标签名称从 "${a}" 更新为 "${e}"，开始同步更新相关数据`),await n.db.questions.updateMany({where:{label:a,projectId:o},data:{label:e}}),console.log(`已更新问题表中的标签: ${a} -> ${e}`),await n.db.datasets.updateMany({where:{questionLabel:a,projectId:o},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${a} -> ${e}`)),c}catch(e){throw console.error("Error update tags db:",e),e}}async function u(e){try{console.log(`开始删除标签: ${e}`);let t=await n.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let r=await h(e,t.projectId);for(let e of(console.log(`找到 ${r.length} 个子标签需要删除`),r.reverse()))await b(e.label,e.projectId),await f(e.label,e.projectId),await n.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await b(t.label,t.projectId),await f(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await n.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function h(e,t){let r=[];async function a(e){let o=await n.db.tags.findMany({where:{parentId:e,projectId:t}});if(o.length>0)for(let e of(r.push(...o),o))await a(e.id)}return await a(e),r}async function f(e,t){try{await n.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function b(e,t){try{await n.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function p(e,t){try{await n.db.tags.deleteMany({where:{projectId:e}}),await w(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function w(e,t,r=null){for(let a of t){let t=await n.db.tags.create({data:{projectId:e,label:a.label,parentId:r}});a.child&&a.child.length>0&&await w(e,a.child,t.id)}}(0,r(40618).h)([c,d,l,u,p]),(0,a.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",c),(0,a.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",d),(0,a.j)("745145798d6802bd295e673536ce529c25576c3c",l),(0,a.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",u),(0,a.j)("1e89c922534cbdd6a59e5783551aafd799be4734",p)},50121:(e,t,r)=>{"use strict";function a(e,t){return e&&Array.isArray(t)?function t(r){for(let a of r){if(a.label===e)return a;if(a.child&&a.child.length>0){let e=t(a.child);if(e)return e}}return null}(t):null}r.d(t,{E:()=>a,h:()=>function e(t,r=0,a=0){return Array.isArray(t)?t.map((t,n)=>{let o;if(0===r){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let r=Math.floor(e/10),a=e%10;return 1===r?0===a?"十":"十"+t[a]:t[r]+"十"+(0===a?"":t[a])}(n+1);o=`${e}、${t.label}`}else o=`${a+1}.${n+1} ${t.label}`;let c={...t,displayLabel:o,displayName:t.label};return t.child&&t.child.length>0&&(c.child=e(t.child,r+1,n)),t.children&&t.children.length>0&&(c.children=e(t.children,r+1,n)),c}):[]}})},66616:(e,t,r)=>{let a=r(92048),n=r(55315),o=n.join(__dirname,"path.txt");e.exports=function(){let e;if(a.existsSync(o)&&(e=a.readFileSync(o,"utf-8")),process.env.ELECTRON_OVERRIDE_DIST_PATH)return n.join(process.env.ELECTRON_OVERRIDE_DIST_PATH,e||"electron");if(e)return n.join(__dirname,"dist",e);throw Error("Electron failed to install correctly, please delete node_modules/electron and try installing again")}()},42549:(e,t,r)=>{"use strict";let a,n;r.d(t,{x0:()=>c});var o=r(6005);function c(e=21){var t;t=e|=0,!a||a.length<t?(a=Buffer.allocUnsafe(128*t),o.webcrypto.getRandomValues(a),n=0):n+t>a.length&&(o.webcrypto.getRandomValues(a),n=0),n+=t;let r="";for(let t=n-e;t<n;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&a[t]];return r}}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,5972,8341,5161,9465,565,4928,249],()=>r(53814));module.exports=a})();