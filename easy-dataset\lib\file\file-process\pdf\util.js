import { downloadDocument } from '@/lib/oss-simple';
import { getUploadFileInfoByFileName } from '@/lib/db/upload-files';
import path from 'path';
import fs from 'fs';
import os from 'os';

export async function getFilePageCount(projectId, fileList) {
  let totalPages = 0;
  const { getPageNum } = await import('pdf2md-js');
  for (const file of fileList) {
    if (file.fileName.endsWith('.pdf')) {
      try {
        // 从OSS下载PDF文件到临时目录
        const pdfFileInfo = await getUploadFileInfoByFileName(projectId, file.fileName);
        if (!pdfFileInfo) {
          console.error(`PDF file ${file.fileName} not found in database`);
          file.pageCount = 1;
          totalPages += 1;
          continue;
        }

        const pdfResult = await downloadDocument(pdfFileInfo.id);

        // 创建临时文件
        const tempDir = path.join(os.tmpdir(), `pagecount-${projectId}-${Date.now()}`);
        await fs.promises.mkdir(tempDir, { recursive: true });
        const tempFilePath = path.join(tempDir, file.fileName);

        // 将OSS内容写入临时文件
        const pdfBuffer = Buffer.isBuffer(pdfResult.content) ? pdfResult.content : Buffer.from(pdfResult.content);
        await fs.promises.writeFile(tempFilePath, pdfBuffer);

        // 获取页数
        const pageCount = await getPageNum(tempFilePath);
        totalPages += pageCount;
        file.pageCount = pageCount;

        // 清理临时文件
        try {
          await fs.promises.rm(tempDir, { recursive: true, force: true });
        } catch (cleanupError) {
          console.warn('Failed to cleanup temporary files:', cleanupError);
        }

      } catch (error) {
        console.error(`Failed to get page count for ${file.fileName}:`, error);
        file.pageCount = 1;
        totalPages += 1;
      }
    } else {
      totalPages += 1;
      file.pageCount = 1;
    }
  }
  console.log(`Total pages to process: ${totalPages}`);
  return totalPages;
}
