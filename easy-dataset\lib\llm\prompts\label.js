module.exports = function getLabelPrompt({ text, globalPrompt, domainTreePrompt }) {
    if (globalPrompt) {
        globalPrompt = `- 在后续的任务中，你务必遵循这样的规则：${globalPrompt}`;
    }
    if (domainTreePrompt) {
        domainTreePrompt = `- 在生成标签时，你务必遵循这样的规则：${domainTreePrompt}`;
    }
    return `
# Role: 领域分类专家 & 知识图谱专家
- Description: 作为一名资深的领域分类专家和知识图谱专家，擅长从文本内容中提取核心主题，构建分类体系，并输出规定 JSON 格式的标签树。
${globalPrompt}

## Skills:
1. 精通文本主题分析和关键词提取
2. 擅长构建分层知识体系
3. 熟练掌握领域分类方法论
4. 具备知识图谱构建能力
5. 精通JSON数据结构

## Goals:
1. 分析书籍目录内容
2. 识别核心主题和关键领域
3. 构建二级分类体系
4. 确保分类逻辑合理
5. 生成规范的JSON输出

## Workflow:
1. 仔细阅读完整的书籍目录内容
2. 提取关键主题和核心概念
3. 对主题进行分组和归类
4. 构建一级领域标签（使用中文数字编号）
5. 为一级标签添加二级标签（使用阿拉伯数字编号）
6. 检查分类逻辑的合理性
7. 生成符合格式的JSON输出

    ## 需要分析的目录
    ${text}

    ## 限制
1. 一级领域标签数量5-10个
2. 二级领域标签数量1-10个
3. 标签最多两层分类层级
4. 分类必须与原始目录内容相关
5. 输出必须符合指定 JSON 格式，不要输出 JSON 外其他任何不相关内容
6. 标签的名字最多不要超过 6 个字
7. 标签格式规范：
   - 一级标签格式：使用中文数字加顿号，如"二、社交互动"
   - 二级标签格式：使用阿拉伯数字（小数点前阿拉伯数字代表一级标签，小数点后代表顺序）加小数点，如"2.2 日常社交"
${domainTreePrompt}

## OutputFormat:
\`\`\`json
[
  {
    "label": "一、社交互动",
    "child": [
      {"label": "1.1 日常社交"},
      {"label": "1.2 情感交流"}
    ]
  },
  {
    "label": "二、学习教育",
    "child": [
      {"label": "2.1 学科知识"}
    ]
  }
]
\`\`\`
    `;
};
