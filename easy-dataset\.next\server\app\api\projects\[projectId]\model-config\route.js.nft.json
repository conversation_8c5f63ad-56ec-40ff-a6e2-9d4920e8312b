{"version": 1, "files": ["../../../../../../../local-db/1tzf-epIiy_0/files/语料合成2_20250721_165259.md", "../../../../../../../local-db/1tzf-epIiy_0/files/语料合成2_20250721_165259.pdf", "../../../../../../../local-db/1tzf-epIiy_0/toc/语料合成2_20250721_165259-toc.json", "../../../../../../../local-db/Ddj9xz-hjRK8/files/语料合成2_20250721_165259.md", "../../../../../../../local-db/Ddj9xz-hjRK8/files/语料合成2_20250721_165259.pdf", "../../../../../../../local-db/Ddj9xz-hjRK8/toc/语料合成2_20250721_165259-toc.json", "../../../../../../../local-db/E-YHmOBUamwe/files/语料合成2_20250721_165259.md", "../../../../../../../local-db/E-YHmOBUamwe/files/语料合成2_20250721_165259.pdf", "../../../../../../../local-db/E-YHmOBUamwe/toc/语料合成2_20250721_165259-toc.json", "../../../../../../../local-db/I0uPQvidaJNv/toc/2-毕业论文参考结构-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/files/2-毕业论文参考结构 - 副本.md", "../../../../../../../local-db/I7Un_VGl3Fca/files/temp_pagecount_1753859208861_基于SpringBoot+Vue的同城二手交易平台的设计与实现_全文标注报告.pdf", "../../../../../../../local-db/I7Un_VGl3Fca/files/基于SpringBoot+Vue的同城二手交易平台的设计与实现_格式检测报告.md", "../../../../../../../local-db/I7Un_VGl3Fca/files/当前经济背景下校园二手交易...台的构建——以邵阳学院为例_李顺.md", "../../../../../../../local-db/I7Un_VGl3Fca/files/语料合成_20250725143954.md", "../../../../../../../local-db/I7Un_VGl3Fca/files/语料合成_20250725143954.pdf", "../../../../../../../local-db/I7Un_VGl3Fca/toc/2-毕业论文参考结构 - 副本-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/java-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/markdown语法-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/what-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/what2-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/基于SpringBoot+Vue的同城二手交易平台的设计与实现_格式检测报告-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/当前经济背景下校园二手交易...台的构建——以邵阳学院为例_李顺-toc.json", "../../../../../../../local-db/I7Un_VGl3Fca/toc/语料合成_20250725143954-toc.json", "../../../../../../../local-db/SgtvhcBGXzR3/toc/2.S99 - 副本_20250714172217-toc.json", "../../../../../../../local-db/_EcWqo-u3Aoy/toc/2-毕业论文参考结构 - 副本-toc.json", "../../../../../../../local-db/_EcWqo-u3Aoy/toc/3-摘要示例-toc.json", "../../../../../../../local-db/_Mp9PGr2gm_C/files/语料合成2_20250721_165259.md", "../../../../../../../local-db/_Mp9PGr2gm_C/files/语料合成2_20250721_165259.pdf", "../../../../../../../local-db/_Mp9PGr2gm_C/toc/语料合成2_20250721_165259-toc.json", "../../../../../../../local-db/dEQrkwzg4LNM/files/生成题目交接文档.md", "../../../../../../../local-db/dEQrkwzg4LNM/toc/生成题目交接文档-toc.json", "../../../../../../../local-db/faEFC4gI4z8B/files/生成题目交接文档.md", "../../../../../../../local-db/faEFC4gI4z8B/toc/生成题目交接文档-toc.json", "../../../../../../../local-db/pcPhkcn8QAe4/files/语料合成2_20250721_165259.md", "../../../../../../../local-db/pcPhkcn8QAe4/files/语料合成2_20250721_165259.pdf", "../../../../../../../local-db/pcPhkcn8QAe4/toc/语料合成2_20250721_165259-toc.json", "../../../../../../../node_modules/.prisma/client/default.js", "../../../../../../../node_modules/.prisma/client/index.js", "../../../../../../../node_modules/.prisma/client/libquery_engine-darwin-arm64.dylib.node", "../../../../../../../node_modules/.prisma/client/libquery_engine-darwin.dylib.node", "../../../../../../../node_modules/.prisma/client/libquery_engine-debian-openssl-1.1.x.so.node", "../../../../../../../node_modules/.prisma/client/libquery_engine-debian-openssl-3.0.x.so.node", "../../../../../../../node_modules/.prisma/client/libquery_engine-linux-arm64-openssl-3.0.x.so.node", "../../../../../../../node_modules/.prisma/client/package.json", "../../../../../../../node_modules/.prisma/client/query_engine-windows.dll.node", "../../../../../../../node_modules/.prisma/client/schema.prisma", "../../../../../../../node_modules/@opentelemetry/api/build/src/api/context.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/api/diag.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/api/metrics.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/api/propagation.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/api/trace.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/baggage/context-helpers.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/baggage/internal/baggage-impl.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/baggage/internal/symbol.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/baggage/utils.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/context-api.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/context/NoopContextManager.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/context/context.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/diag-api.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/diag/ComponentLogger.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/diag/consoleLogger.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/diag/internal/logLevelLogger.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/diag/types.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/index.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/internal/global-utils.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/internal/semver.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/metrics-api.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/metrics/Metric.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/metrics/NoopMeter.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/metrics/NoopMeterProvider.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/platform/index.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/platform/node/globalThis.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/platform/node/index.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/propagation-api.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/propagation/NoopTextMapPropagator.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/propagation/TextMapPropagator.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace-api.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/NonRecordingSpan.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/NoopTracer.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/NoopTracerProvider.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/ProxyTracer.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/ProxyTracerProvider.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/SamplingResult.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/context-utils.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/tracestate-impl.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/tracestate-validators.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/internal/utils.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/invalid-span-constants.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/span_kind.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/spancontext-utils.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/status.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/trace/trace_flags.js", "../../../../../../../node_modules/@opentelemetry/api/build/src/version.js", "../../../../../../../node_modules/@opentelemetry/api/package.json", "../../../../../../../node_modules/@prisma/client/default.js", "../../../../../../../node_modules/@prisma/client/package.json", "../../../../../../../node_modules/@prisma/client/runtime/library.js", "../../../../../../../node_modules/next/dist/client/components/action-async-storage-instance.js", "../../../../../../../node_modules/next/dist/client/components/action-async-storage.external.js", "../../../../../../../node_modules/next/dist/client/components/async-local-storage.js", "../../../../../../../node_modules/next/dist/client/components/request-async-storage-instance.js", "../../../../../../../node_modules/next/dist/client/components/request-async-storage.external.js", "../../../../../../../node_modules/next/dist/client/components/static-generation-async-storage-instance.js", "../../../../../../../node_modules/next/dist/client/components/static-generation-async-storage.external.js", "../../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../../../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../../../../../node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js", "../../../../../../../node_modules/next/dist/compiled/next-server/app-route.runtime.prod.js", "../../../../../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../../../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../../../../../node_modules/next/package.json", "../../../../../../../package.json", "../../../../../../package.json", "../../../../../chunks/5972.js", "../../../../../chunks/8341.js", "../../../../../chunks/8948.js", "../../../../../webpack-runtime.js"]}