{"version": 4, "routes": {"/api/check-update": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/api/layout,_N_T_/api/check-update/layout,_N_T_/api/check-update/route,_N_T_/api/check-update"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/api/check-update", "dataRoute": null}, "/api/llm/providers": {"initialHeaders": {"content-type": "application/json", "x-next-cache-tags": "_N_T_/layout,_N_T_/api/layout,_N_T_/api/llm/layout,_N_T_/api/llm/providers/layout,_N_T_/api/llm/providers/route,_N_T_/api/llm/providers"}, "experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/api/llm/providers", "dataRoute": null}, "/big-screen": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/big-screen", "dataRoute": "/big-screen.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/dataset-square": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/dataset-square", "dataRoute": "/dataset-square.rsc"}, "/projects": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/projects", "dataRoute": "/projects.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "92b7e57b8eb24f26dc9eea6dc4cadf4d", "previewModeSigningKey": "a67b059f2de1ad50110df4f25cb1afad1e4129cdcecc91beca504f0d21a3eaaa", "previewModeEncryptionKey": "f9634f152944caceb96d636581dabf5c52f8859a35eb487996fb926079ea21eb"}}