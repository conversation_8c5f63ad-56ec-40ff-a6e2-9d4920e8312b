module.exports = function convertPrompt() {
  return `
    使用markdown语法，将图片中识别到的文字转换为markdown格式输出。你必须做到：
          1. 输出和使用识别到的图片的相同的语言，例如，识别到英语的字段，输出的内容必须是英语。
          2. 不要解释和输出无关的文字，直接输出图片中的内容。
          3. 内容不要包含在\`\`\`markdown \`\`\`中、段落公式使用 $$ $$ 的形式、行内公式使用 $ $ 的形式。
          4. 忽略掉页眉页脚里的内容
          5. 请不要对图片的标题进行markdown的格式化，直接以文本形式输出到内容中。
          6. 有可能每页都会出现期刊名称，论文名称，会议名称或者书籍名称，请忽略他们不要识别成标题
          7. 请精确分析当前PDF页面的文本结构和视觉布局，按以下要求处理：
            1. 识别所有标题文本，并判断其层级（根据字体大小、加粗、位置等视觉特征）
            2. 输出为带层级的Markdown格式，严格使用以下规则：
              - 一级标题：字体最大/顶部居中，前面加 # 
              - 二级标题：字体较大/左对齐加粗，有可能是数字开头也有可能是罗马数组开头，前面加 ## 
              - 三级标题：字体稍大/左对齐加粗，前面加 ### 
              - 正文文本：直接转换为普通段落
            3. 不确定层级的标题请标记[?]
            4. 如果是中文文献，但是有英文标题和摘要可以省略不输出
            示例输出：
            ## 4研究方法
            ### 4.1数据收集
            本文采用问卷调查...
      `;
};
