# 高度优化的多阶段构建 Dockerfile
# 使用更细粒度的缓存策略和最小化的镜像层

FROM node:20-alpine AS base
# 安装 pnpm 和基础工具
RUN apk add --no-cache libc6-compat curl && \
    npm install -g pnpm@9 && \
    rm -rf /var/cache/apk/*

# 依赖安装阶段 - 最大化缓存利用
FROM base AS deps
WORKDIR /app

# 复制依赖文件
COPY package.json pnpm-lock.yaml .npmrc ./

# 安装所有依赖（包括开发依赖）
RUN pnpm install --frozen-lockfile

# 生产依赖阶段 - 只保留生产依赖
FROM base AS prod-deps
WORKDIR /app

COPY package.json pnpm-lock.yaml .npmrc ./
RUN pnpm install --frozen-lockfile --prod

# 构建阶段 - 包含原生依赖编译
FROM base AS builder
WORKDIR /app

ARG TARGETPLATFORM

# 安装构建依赖（合并到一个RUN命令中减少层数）
RUN apk add --no-cache --virtual .build-deps \
    python3 make g++ cairo-dev pango-dev jpeg-dev \
    giflib-dev librsvg-dev build-base pixman-dev pkgconfig

# 从deps阶段复制所有依赖
COPY --from=deps /app/node_modules ./node_modules
COPY package.json pnpm-lock.yaml .npmrc ./

# 只复制构建必需的源代码文件
COPY prisma ./prisma
COPY next.config.js jsconfig.json ./
COPY app ./app
COPY components ./components
COPY constant ./constant
COPY hooks ./hooks
COPY lib ./lib
COPY locales ./locales
COPY public ./public
COPY styles ./styles

# 生成 Prisma 客户端并构建应用
RUN if [ "$TARGETPLATFORM" = "linux/arm64" ]; then \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-arm64-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-arm64-openssl-3.0.x" pnpm build:prod; \
    else \
        sed -i 's/binaryTargets = \[.*\]/binaryTargets = \["linux-musl-openssl-3.0.x"\]/' prisma/schema.prisma; \
        PRISMA_CLI_BINARY_TARGETS="linux-musl-openssl-3.0.x" pnpm build:prod; \
    fi && \
    # 清理构建依赖
    apk del .build-deps && \
    rm -rf /var/cache/apk/*

# 最终运行时镜像 - 最小化
FROM base AS runner
WORKDIR /app

# 安装运行时依赖
RUN apk add --no-cache \
    cairo pango jpeg giflib librsvg pixman dumb-init && \
    rm -rf /var/cache/apk/*

# 创建非特权用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# 复制应用文件
COPY package.json ./
COPY --from=prod-deps --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder --chown=nextjs:nodejs /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/prisma ./prisma

# 复制环境配置
COPY .env.production ./

# 设置环境变量
ENV NODE_ENV=production \
    NEXT_TELEMETRY_DISABLED=1 \
    PORT=1717

# 切换到非特权用户
USER nextjs

EXPOSE 1717

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:1717/api/health || exit 1

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]
CMD ["pnpm", "start:prod"]
