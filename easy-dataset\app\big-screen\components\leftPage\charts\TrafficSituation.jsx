import React, { PureComponent } from 'react';
import Chart from '../../../utils/chart';
import { trafficOptions } from './options';

class TrafficSituation extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      renderer: 'canvas',
    };
  }

  render() {
    const { renderer } = this.state;
    const { trafficSitua } = this.props;
    return (
      <div
        style={{
          width: '5.375rem',
          height: '3rem',
        }}>
        {trafficSitua && trafficSitua.timeList && trafficSitua.inData && trafficSitua.outData ? (
          <Chart renderer={renderer} option={trafficOptions(trafficSitua)} />
        ) : (
          <div style={{
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#BCDCFF',
            fontSize: '14px'
          }}>
            暂无数据
          </div>
        )}
      </div>
    );
  } //endrender
}

export default TrafficSituation;
