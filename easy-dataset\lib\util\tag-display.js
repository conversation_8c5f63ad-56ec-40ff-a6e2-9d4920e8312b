/**
 * 标签显示工具函数
 * 实现标签序号的渲染时生成
 */

/**
 * 将阿拉伯数字转换为中文数字
 * @param {number} num - 要转换的数字
 * @returns {string} 中文数字
 */
function getChineseNumber(num) {
  // 简化版本：支持1-99的数字转换
  const ones = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];

  if (num < 1 || num > 99) return num.toString(); // 超出范围直接返回阿拉伯数字

  if (num <= 10) {
    // 1-10的特殊处理
    const mapping = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    return mapping[num];
  }

  const tens = Math.floor(num / 10);
  const units = num % 10;

  if (tens === 1) {
    // 11-19: 十一、十二、...、十九
    return units === 0 ? '十' : '十' + ones[units];
  } else {
    // 20-99: 二十、二十一、...、九十九
    return ones[tens] + '十' + (units === 0 ? '' : ones[units]);
  }
}

/**
 * 为标签树添加显示序号
 * @param {Array} tags - 标签树数组
 * @param {number} level - 当前层级（0为根级）
 * @param {number} parentIndex - 父标签索引（用于二级标签编号）
 * @returns {Array} 带显示序号的标签树
 */
export function addDisplayNumbers(tags, level = 0, parentIndex = 0) {
  if (!Array.isArray(tags)) return [];

  return tags.map((tag, index) => {
    let displayLabel;

    if (level === 0) {
      // 一级标签：使用中文数字
      const chineseNum = getChineseNumber(index + 1);
      displayLabel = `${chineseNum}、${tag.label}`;
    } else {
      // 二级标签：使用阿拉伯数字
      displayLabel = `${parentIndex + 1}.${index + 1} ${tag.label}`;
    }

    const result = {
      ...tag,
      displayLabel, // 用于显示的标签名（包含序号）
      displayName: tag.label // 纯标签名（不含序号）
    };

    // 递归处理子标签（支持 child 和 children 两种字段名）
    if (tag.child && tag.child.length > 0) {
      result.child = addDisplayNumbers(tag.child, level + 1, index);
    }
    if (tag.children && tag.children.length > 0) {
      result.children = addDisplayNumbers(tag.children, level + 1, index);
    }

    return result;
  });
}

/**
 * 标签匹配函数（基于标签名称匹配）
 * @param {string} targetName - 目标标签名称
 * @param {Array} tags - 标签列表
 * @returns {Object|null} 匹配的标签
 */
export function findTagByDisplayName(targetName, tags) {
  if (!targetName || !Array.isArray(tags)) return null;

  // 递归搜索所有标签
  function searchTags(tagList) {
    for (const tag of tagList) {
      // 精确匹配标签名称
      if (tag.label === targetName) {
        return tag;
      }

      // 搜索子标签
      if (tag.child && tag.child.length > 0) {
        const childResult = searchTags(tag.child);
        if (childResult) return childResult;
      }
    }
    return null;
  }

  return searchTags(tags);
}
