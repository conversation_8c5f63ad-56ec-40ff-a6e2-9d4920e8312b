import crypto from 'crypto';
import path from 'path';
import { FILE_RECORD } from '@/constant/index';

/**
 * 根据文件扩展名获取文件格式
 * @param {string} fileName 文件名
 * @returns {string} 文件格式
 */
export function getFileFormat(fileName) {
  const ext = path.extname(fileName).toLowerCase();

  switch (ext) {
    case '.xlsx':
    case '.xls':
      return FILE_RECORD.FORMAT.EXCEL;
    case '.json':
      return FILE_RECORD.FORMAT.JSON;
    case '.jsonl':
      return FILE_RECORD.FORMAT.JSONL;
    case '.csv':
      return FILE_RECORD.FORMAT.CSV;
    case '.pdf':
      return FILE_RECORD.FORMAT.PDF;
    case '.docx':
    case '.doc':
      return FILE_RECORD.FORMAT.DOCX;
    case '.md':
      return FILE_RECORD.FORMAT.MARKDOWN;
    case '.txt':
      return FILE_RECORD.FORMAT.TXT;
    default:
      return 'unknown';
  }
}

/**
 * 根据文件格式获取MIME类型
 * @param {string} fileFormat 文件格式
 * @returns {string} MIME类型
 */
export function getMimeType(fileFormat) {
  return FILE_RECORD.MIME_TYPES[fileFormat] || 'application/octet-stream';
}

/**
 * 计算文件的MD5哈希值
 * @param {Buffer} fileBuffer 文件缓冲区
 * @returns {string} MD5哈希值
 */
export function calculateMD5(fileBuffer) {
  return crypto.createHash('md5').update(fileBuffer).digest('hex');
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 验证文件类型是否支持
 * @param {string} fileName 文件名
 * @param {string} operationType 操作类型
 * @returns {boolean} 是否支持
 */
export function isFileTypeSupported(fileName, operationType) {
  const fileFormat = getFileFormat(fileName);

  switch (operationType) {
    case FILE_RECORD.OPERATION_TYPE.IMPORT:
      // 导入支持的格式
      return [
        FILE_RECORD.FORMAT.EXCEL,
        FILE_RECORD.FORMAT.JSON,
        FILE_RECORD.FORMAT.JSONL,
        FILE_RECORD.FORMAT.CSV
      ].includes(fileFormat);

    case FILE_RECORD.OPERATION_TYPE.EXPORT:
      // 导出支持的格式
      return [
        FILE_RECORD.FORMAT.EXCEL,
        FILE_RECORD.FORMAT.JSON,
        FILE_RECORD.FORMAT.JSONL,
        FILE_RECORD.FORMAT.CSV
      ].includes(fileFormat);

    case FILE_RECORD.OPERATION_TYPE.UPLOAD:
      // 上传支持的格式
      return [
        FILE_RECORD.FORMAT.PDF,
        FILE_RECORD.FORMAT.DOCX,
        FILE_RECORD.FORMAT.MARKDOWN,
        FILE_RECORD.FORMAT.TXT
      ].includes(fileFormat);

    default:
      return false;
  }
}

/**
 * 生成文件记录的基础数据
 * @param {Object} options 选项
 * @returns {Object} 文件记录数据
 */
export function generateFileRecordData(options) {
  const {
    projectId,
    fileName,
    originalName,
    fileBuffer,
    operationType,
    filePath,
    recordCount,
    description,
    metadata,
    taskId
  } = options;

  const fileFormat = getFileFormat(fileName);
  const fileExt = path.extname(fileName);
  const mimeType = getMimeType(fileFormat);
  const fileSize = fileBuffer ? fileBuffer.length : 0;
  const md5Hash = fileBuffer ? calculateMD5(fileBuffer) : null;

  return {
    projectId,
    fileName,
    originalName: originalName || fileName,
    fileExt,
    fileFormat,
    operationType,
    filePath,
    fileSize,
    md5Hash,
    mimeType,
    status: FILE_RECORD.STATUS.PROCESSING,
    recordCount: recordCount || null,
    description: description || null,
    metadata: metadata ? JSON.stringify(metadata) : null,
    taskId: taskId || null
  };
}

/**
 * 解析元数据
 * @param {string} metadataString JSON字符串格式的元数据
 * @returns {Object|null} 解析后的元数据对象
 */
export function parseMetadata(metadataString) {
  if (!metadataString) return null;

  try {
    return JSON.parse(metadataString);
  } catch (error) {
    console.error('解析元数据失败:', error);
    return null;
  }
}
