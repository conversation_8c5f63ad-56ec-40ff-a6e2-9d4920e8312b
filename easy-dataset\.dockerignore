# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build outputs
.next
out
dist
build

# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode
.idea
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local database and cache
local-db
.cache

# Documentation
README.md
README.zh-CN.md
ARCHITECTURE.md
docs/

# Git
.git
.gitignore

# Testing
coverage
.nyc_output

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Electron
electron/dist

# Temporary folders
tmp/
temp/
