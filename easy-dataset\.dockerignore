# 开发和构建文件
node_modules
.next
.vscode
.idea
dist
build

# 缓存文件
.next/cache
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 环境和配置文件
.env.local
.env.*.local
.DS_Store
Thumbs.db

# Git 相关
.git
.gitignore
README.md
README.zh-CN.md
ARCHITECTURE.md
IMPLEMENTATION_PLAN.md

# 测试和文档
test
tests
__tests__
docs
*.md
!package.json

# 本地数据库
local-db
prisma/*.sqlite
prisma/local-db

# 临时文件
tmp
temp
*.tmp
*.temp

# 编辑器配置
.vscode
.idea
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 开发工具
.eslintcache
tsconfig.tsbuildinfo

# 其他不需要的文件
*.tgz
*.tar.gz
coverage
.nyc_output
