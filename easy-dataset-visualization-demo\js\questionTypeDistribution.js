/**
 * 问题类型分布饼图
 * 显示基于标签的问题类型分布
 */

// 生成表示问题类型分布的虚拟数据
function generateQuestionTypeData() {
    // 定义问题类型及其对应的数量
    return [
        { value: 235, name: '是非问题', itemStyle: { color: '#5470c6' } },
        { value: 274, name: '事实问题', itemStyle: { color: '#91cc75' } },
        { value: 310, name: '解释问题', itemStyle: { color: '#fac858' } },
        { value: 335, name: '比较问题', itemStyle: { color: '#ee6666' } },
        { value: 400, name: '开放问题', itemStyle: { color: '#73c0de' } }
    ];
}

// 初始化ECharts饼图
function initQuestionTypeChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-type-distribution');
    if (!chartContainer) {
        console.error('找不到图表容器：question-type-distribution');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const questionTypeData = generateQuestionTypeData();
    
    // 计算总数，用于显示百分比
    const total = questionTypeData.reduce((sum, item) => sum + item.value, 0);
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题类型分布 - 饼图 (pie)',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                // 计算百分比
                const percent = ((params.value / total) * 100).toFixed(1);
                return `${params.name}: ${params.value} (${percent}%)`;
            }
        },
        legend: {
            orient: 'horizontal',
            bottom: '5%',
            left: 'center',
            itemWidth: 25,
            itemHeight: 14
        },
        series: [
            {
                name: '问题类型',
                type: 'pie',
                radius: ['40%', '70%'], // 环形饼图
                center: ['50%', '50%'],
                avoidLabelOverlap: true,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: true,
                    formatter: function(params) {
                        // 计算百分比
                        const percent = ((params.value / total) * 100).toFixed(1);
                        return `${params.name}\n${percent}%`;
                    }
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '16',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: true
                },
                data: questionTypeData
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 在文档加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
    initQuestionTypeChart();
}); 