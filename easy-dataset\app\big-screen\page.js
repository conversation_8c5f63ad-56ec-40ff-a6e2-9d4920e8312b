'use client';

import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';

// 动态导入 big-screen 组件，禁用 SSR
const BigScreenApp = dynamic(() => import('./BigScreenApp'), {
  ssr: false,
});

export default function BigScreenPage() {
  useEffect(() => {
    // 设置全屏样式
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'auto';

    return () => {
      // 清理样式
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.overflow = '';
    };
  }, []);

  return (
    <div style={{ width: '100vw', minHeight: '100vh' }}>
      <BigScreenApp />
    </div>
  );
}