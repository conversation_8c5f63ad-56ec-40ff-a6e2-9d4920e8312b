'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Chip,
  Stack
} from '@mui/material';
import DomainTagFilter from '@/components/datasets/DomainTagFilter';
import { useTranslation } from 'react-i18next';

/**
 * 领域标签选择器组件，用于数据集详情页面
 * @param {Object} props
 * @param {string} props.projectId - 项目ID
 * @param {string} props.value - 当前标签值，格式为逗号分隔的字符串
 * @param {function} props.onChange - 标签变更回调
 * @param {boolean} props.editing - 是否处于编辑状态
 * @param {React.ReactNode} props.editButton - 编辑按钮组件
 */
export default function DomainTagSelector({ projectId, value, onChange, editing, editButton }) {
  const [open, setOpen] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [tempSelectedTag, setTempSelectedTag] = useState(null);
  const { t } = useTranslation();

  // 当value变化时，更新selectedTags
  useEffect(() => {
    if (value) {
      // 将逗号分隔的字符串转换为标签数组
      const tagArray = value.split(',').map(tag => {
        const trimmedTag = tag.trim();
        // 如果标签包含 ">" 符号，说明是带有父标签前缀的格式，只取最后一部分
        if (trimmedTag.includes('>')) {
          return { label: trimmedTag.split('>').pop().trim() };
        }
        return { label: trimmedTag };
      }).filter(tag => tag.label);

      setSelectedTags(tagArray);
    } else {
      setSelectedTags([]);
    }
  }, [value]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setTempSelectedTag(null);
  };

  const handleTagSelect = (tag) => {
    setTempSelectedTag(tag);
  };

  const handleAddTag = () => {
    if (!tempSelectedTag) return;

    // 构建标签文本 - 只使用标签本身，不包括父标签前缀
    let tagText = '';
    if (tempSelectedTag.label) {
      tagText = tempSelectedTag.label;
    }

    if (!tagText) return;

    // 检查是否已存在相同标签
    const exists = selectedTags.some(tag => tag.label === tagText);
    if (!exists) {
      const newTags = [...selectedTags, { label: tagText }];
      setSelectedTags(newTags);
      // 将标签数组转换为逗号分隔的字符串
      onChange(newTags.map(tag => tag.label).join(', '));
    }

    setTempSelectedTag(null);
  };

  const handleRemoveTag = (tagToRemove) => {
    const newTags = selectedTags.filter(tag => tag.label !== tagToRemove.label);
    setSelectedTags(newTags);
    // 将标签数组转换为逗号分隔的字符串
    onChange(newTags.map(tag => tag.label).join(', '));
  };

  const handleClearTags = () => {
    setSelectedTags([]);
    onChange('');
  };

  if (!editing) {
    return (
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Typography variant="subtitle1" color="text.secondary" sx={{ mr: 1 }}>
            {t('datasets.domainTag')}
          </Typography>
          {editButton}
        </Box>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {selectedTags.length > 0 ? (
            selectedTags.map((tag, index) => (
              <Chip
                key={index}
                label={tag.displayLabel || tag.label}
                color="primary"
                variant="outlined"
                size="medium"
                sx={{ mb: 1 }}
              />
            ))
          ) : (
            <Typography variant="body2" color="text.secondary">
              {t('common.noData')}
            </Typography>
          )}
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 1 }}>
        {t('datasets.domainTag')}
      </Typography>
      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap sx={{ mb: 2 }}>
        {selectedTags.length > 0 ? (
          selectedTags.map((tag, index) => (
            <Chip
              key={index}
              label={tag.displayLabel || tag.label}
              color="primary"
              onDelete={() => handleRemoveTag(tag)}
              sx={{ borderRadius: 1, mb: 1 }}
            />
          ))
        ) : (
          <Typography variant="body2" color="text.secondary">
            {t('common.noSelected')}
          </Typography>
        )}
      </Stack>
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button variant="outlined" onClick={handleOpen} size="small">
          {t('common.add')}
        </Button>
        {selectedTags.length > 0 && (
          <Button variant="outlined" color="error" onClick={handleClearTags} size="small">
            {t('common.remove')}
          </Button>
        )}
      </Box>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>{t('datasets.selectDomainTag')}</DialogTitle>
        <DialogContent dividers>
          <DomainTagFilter
            projectId={projectId}
            onTagSelect={handleTagSelect}
            selectedTag={tempSelectedTag}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleAddTag}
            color="primary"
            variant="contained"
            disabled={!tempSelectedTag}
          >
            {t('common.add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
