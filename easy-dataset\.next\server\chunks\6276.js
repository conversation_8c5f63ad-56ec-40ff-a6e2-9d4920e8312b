"use strict";exports.id=6276,exports.ids=[6276],exports.modules={6142:(e,t,n)=>{n.r(t),n.d(t,{createProject:()=>u,deleteProject:()=>m,getProject:()=>p,getProjects:()=>f,getTaskConfig:()=>b,isExistByName:()=>g,updateProject:()=>w});var a=n(24330);n(60166);var r=n(92048),i=n.n(r),l=n(55315),o=n.n(l),c=n(67565);let s={textSplitMinLength:1500,textSplitMaxLength:2e3,questionGenerationLength:240,questionMaskRemovingProbability:60,huggingfaceToken:"",concurrencyLimit:5,visionConcurrencyLimit:5};var d=n(4342),h=n(42549);async function u(e){try{let t=(0,h.x0)(12),n=await (0,c.getProjectRoot)(),a=o().join(n,t);return await i().promises.mkdir(a,{recursive:!0}),await i().promises.mkdir(o().join(a,"files"),{recursive:!0}),await d.db.projects.create({data:{id:t,name:e.name,description:e.description,country:e.country}})}catch(e){throw console.error("Failed to create project in database"),e}}async function g(e){try{return await d.db.projects.count({where:{name:e}})>0}catch(e){throw console.error("Failed to get project by name in database"),e}}async function f(){try{return await d.db.projects.findMany({include:{_count:{select:{Datasets:!0,Questions:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get projects in database"),e}}async function p(e){try{return await d.db.projects.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get project by id in database"),e}}async function w(e,t){try{return delete t.projectId,await d.db.projects.update({where:{id:e},data:{...t}})}catch(e){throw console.error("Failed to update project in database"),e}}async function m(e){try{let t=await (0,c.getProjectRoot)(),n=o().join(t,e);return await d.db.projects.delete({where:{id:e}}),i().existsSync(n)&&await i().promises.rm(n,{recursive:!0}),!0}catch(e){return!1}}async function b(e){let t=await (0,c.getProjectRoot)(),n=o().join(t,e),a=o().join(n,"task-config.json");return await (0,c.readJsonFile)(a)||s}(0,n(40618).h)([u,g,f,p,w,m,b]),(0,a.j)("735bd40bd5e797892e593b8fddc2605b8d39c8fd",u),(0,a.j)("bd403a39d7d466f99a4386bb75622a1fd55c733f",g),(0,a.j)("2680d512486ca803a14da96551e1a84226778673",f),(0,a.j)("f9f198a77b5228a5545ccb8d1ddbac6437c363d5",p),(0,a.j)("a20b40853cd9266fb771e0109a9df77229473516",w),(0,a.j)("ed0d857f38cd63a93b7768748cc0f7b89e8627b9",m),(0,a.j)("8e0c652d3cc60dd97f2d2b56a785bf2d4b1ab3a5",b)},3800:(e,t,n)=>{n.r(t),n.d(t,{batchSaveTags:()=>p,createTag:()=>s,deleteTag:()=>h,getTags:()=>l,updateTag:()=>d});var a=n(24330);n(60166);var r=n(4342),i=n(50121);async function l(e){try{let t=await o(e);return(0,i.h)(t)}catch(e){return[]}}async function o(e,t=null){let n=await r.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of n){let n=await c(t.id);t.questionCount=await r.db.questions.count({where:{label:{in:n},projectId:e}}),t.child=await o(e,t.id)}return n}async function c(e){let t=[],n=[e];for(;n.length>0;){let e=n.shift(),a=await r.db.tags.findUnique({where:{id:e}});if(a){t.push(a.label);let i=await r.db.tags.findMany({where:{parentId:e},select:{id:!0}});n.push(...i.map(e=>e.id))}}return t}async function s(e,t,n){try{let a=await r.db.tags.findFirst({where:{projectId:e,label:t,parentId:n||null}});if(a)return console.log(`标签已存在: ${t}，返回现有标签`),a;let i={projectId:e,label:t};return n&&(i.parentId=n),await r.db.tags.create({data:i})}catch(a){if("P2002"===a.code&&a.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let a=await r.db.tags.findFirst({where:{projectId:e,label:t,parentId:n||null}});if(a)return a}throw console.error("Error insert tags db:",a),a}}async function d(e,t){try{let n=await r.db.tags.findUnique({where:{id:t}});if(!n)throw Error(`标签不存在: ${t}`);let a=n.label,i=n.projectId,l=await r.db.tags.update({where:{id:t},data:{label:e}});return a!==e&&(console.log(`标签名称从 "${a}" 更新为 "${e}"，开始同步更新相关数据`),await r.db.questions.updateMany({where:{label:a,projectId:i},data:{label:e}}),console.log(`已更新问题表中的标签: ${a} -> ${e}`),await r.db.datasets.updateMany({where:{questionLabel:a,projectId:i},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${a} -> ${e}`)),l}catch(e){throw console.error("Error update tags db:",e),e}}async function h(e){try{console.log(`开始删除标签: ${e}`);let t=await r.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let n=await u(e,t.projectId);for(let e of(console.log(`找到 ${n.length} 个子标签需要删除`),n.reverse()))await f(e.label,e.projectId),await g(e.label,e.projectId),await r.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await f(t.label,t.projectId),await g(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await r.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function u(e,t){let n=[];async function a(e){let i=await r.db.tags.findMany({where:{parentId:e,projectId:t}});if(i.length>0)for(let e of(n.push(...i),i))await a(e.id)}return await a(e),n}async function g(e,t){try{await r.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function f(e,t){try{await r.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function p(e,t){try{await r.db.tags.deleteMany({where:{projectId:e}}),await w(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function w(e,t,n=null){for(let a of t){let t=await r.db.tags.create({data:{projectId:e,label:a.label,parentId:n}});a.child&&a.child.length>0&&await w(e,a.child,t.id)}}(0,n(40618).h)([l,s,d,h,p]),(0,a.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",l),(0,a.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",s),(0,a.j)("745145798d6802bd295e673536ce529c25576c3c",d),(0,a.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",h),(0,a.j)("1e89c922534cbdd6a59e5783551aafd799be4734",p)},21314:e=>{e.exports={extractOutline:function(e){let t;let n=/^(#{1,6})\s+(.+?)(?:\s*\{#[\w-]+\})?\s*$/gm,a=[];for(;null!==(t=n.exec(e));){let e=t[1].length,n=t[2].trim();a.push({level:e,title:n,position:t.index})}return a},splitByHeadings:function(e,t){if(0===t.length)return[{heading:null,level:0,content:e,position:0}];let n=[];if(t[0].position>0){let a=e.substring(0,t[0].position).trim();a.length>0&&n.push({heading:null,level:0,content:a,position:0})}for(let a=0;a<t.length;a++){let r=t[a],i=a<t.length-1?t[a+1]:null,l=e.substring(r.position).split("\n")[0],o=r.position+l.length+1,c=i?i.position:e.length,s=e.substring(o,c).trim();n.push({heading:r.title,level:r.level,content:s,position:r.position})}return n}}},64042:(e,t,n)=>{function a(e,t){let n=e.content.split(/\n\n+/),a=[],r="";for(let e of n)if(e.length>t){r.length>0&&(a.push(r),r="");let n=e.match(/[^.!?。！？]+[.!?。！？]+/g)||[e],i="";for(let e of n)if((i+e).length<=t)i+=e;else if(i.length>0&&a.push(i),e.length>t)for(let n=0;n<e.length;n+=t)a.push(e.substr(n,t));else i=e;i.length>0&&(r=i)}else(r+"\n\n"+e).length<=t?r=r.length>0?r+"\n\n"+e:e:(a.push(r),r=e);return r.length>0&&a.push(r),a}e.exports={splitLongSection:a,processSections:function(e,t,r,i){e.forEach(e=>{"string"!=typeof e.content&&(e.content=String(e.content||""))});let l=[],o=null;for(let t of e){if(t.content.trim().length<r&&o){let e=`${o.content}

${t.heading?`${"#".repeat(t.level)} ${t.heading}
`:""}${t.content}`;if(e.length<=i){o.content=e,t.heading&&(o.headings=o.headings||[],o.headings.push({heading:t.heading,level:t.level,position:t.position}));continue}}o&&l.push(o),o={...t,headings:t.heading?[{heading:t.heading,level:t.level,position:t.position}]:[]}}o&&l.push(o);let c=[],s=null;for(let e=0;e<l.length;e++){let o=l[e],d=o.content.trim().length;if(d<r){s?(s.content+=`

${o.heading?`${"#".repeat(o.level)} ${o.heading}
`:""}${o.content}`,o.heading&&s.headings.push({heading:o.heading,level:o.level,position:o.position})):s={heading:o.heading,level:o.level,content:o.content,position:o.position,headings:[{heading:o.heading,level:o.level,position:o.position}]};let e=s.content.trim().length;if(e>=r){let r=n(64239).generateEnhancedSummary(s,t);if(e>i){let e=a(s,i);for(let t=0;t<e.length;t++)c.push({summary:`${r} - Part ${t+1}/${e.length}`,content:e[t]})}else c.push({summary:r,content:s.content});s=null}continue}if(s){let e=n(64239).generateEnhancedSummary(s,t);if(s.content.trim().length>i){let{result:t,lastChunk:n}=a(s,i,r);for(let n=0;n<t.length;n++)c.push({summary:`${e} - Part ${n+1}/${t.length}`,content:t[n]});if(n){s={...s,content:n};continue}}else c.push({summary:e,content:s.content});s=null}if(d>i){let e=a(o,i);!o.headings&&o.heading&&(o.headings=[{heading:o.heading,level:o.level,position:o.position}]);for(let a=0;a<e.length;a++){let r=e[a],i=n(64239).generateEnhancedSummary(o,t,a+1,e.length);c.push({summary:i,content:r})}}else{!o.headings&&o.heading&&(o.headings=[{heading:o.heading,level:o.level,position:o.position}]);let e=n(64239).generateEnhancedSummary(o,t),a=`${o.heading?`${"#".repeat(o.level)} ${o.heading}
`:""}${o.content}`;c.push({summary:e,content:a})}}if(s){if(c.length>0){let e=c[c.length-1],a=`${e.content}

${s.content}`;if(a.length<=i){let e=n(64239).generateEnhancedSummary({...s,content:a},t);c[c.length-1]={summary:e,content:a}}else{let e=n(64239).generateEnhancedSummary(s,t),a=`${s.heading?`${"#".repeat(s.level)} ${s.heading}
`:""}${s.content}`;c.push({summary:e,content:a})}}else{let e=n(64239).generateEnhancedSummary(s,t),a=`${s.heading?`${"#".repeat(s.level)} ${s.heading}
`:""}${s.content}`;c.push({summary:e,content:a})}}return c}}},64239:e=>{function t(e,t,n=null,a=null){if(!e.heading&&0===e.level||!e.headings&&!e.heading){let e=t.length>0&&1===t[0].level?t[0].title:"文档";return`${e} 前言`}if(e.headings&&e.headings.length>0){let r=[...e.headings].sort((e,t)=>e.level!==t.level?e.level-t.level:e.position-t.position),i=new Map;for(let e of r){if(!e.heading)continue;let n=t.findIndex(t=>t.title===e.heading&&t.level===e.level);if(-1===n){i.set(e.heading,e.heading);continue}let a=[],r=e.level-1;for(let e=n-1;e>=0&&r>0;e--)t[e].level===r&&(a.unshift(t[e].title),r--);a.push(e.heading);let l=a.join(" > ");i.set(l,l)}let l=Array.from(i.values()).sort((e,t)=>(e.match(/>/g)||[]).length-(t.match(/>/g)||[]).length||e.localeCompare(t));if(0===l.length)return e.heading?e.heading:"未命名段落";if(1===l.length){let e=l[0];return null!==n&&a>1&&(e+=` - Part ${n}/${a}`),e}let o="",c=l[0].split(" > ");for(let e=0;e<c.length-1;e++){let t=c.slice(0,e+1).join(" > "),n=!0;for(let e=1;e<l.length;e++)if(!l[e].startsWith(t+" > ")){n=!1;break}if(n){o=t+" > [";for(let e=0;e<l.length;e++){let n=l[e].substring(t.length+3);o+=(e>0?", ":"")+n}o+="]";break}}return o||(o=l.join(", ")),null!==n&&a>1&&(o+=` - Part ${n}/${a}`),o}if(!e.heading&&0===e.level)return"文档前言";let r=t.findIndex(t=>t.title===e.heading&&t.level===e.level);if(-1===r)return e.heading?e.heading:"未命名段落";let i=[],l=e.level-1;for(let e=r-1;e>=0&&l>0;e--)t[e].level===l&&(i.unshift(t[e].title),l--);let o="";return i.length>0&&(o=i.join(" > ")+" > "),o+=e.heading,null!==n&&a>1&&(o+=` - Part ${n}/${a}`),o}e.exports={generateEnhancedSummary:t,generateSummary:function(e,n,a=null,r=null){return t(e,n,a,r)}}},16365:e=>{e.exports={extractTableOfContents:function(e,t={}){let n;let{maxLevel:a=6,includeLinks:r=!0,flatList:i=!1}=t,l=/^(#{1,6})\s+(.+?)(?:\s*\{#[\w-]+\})?\s*$/gm,o=[];for(;null!==(n=l.exec(e));){let e=n[1].length;if(e>a)continue;let t=n[2].trim(),r=n.index,i=t.toLowerCase().replace(/\s+/g,"-").replace(/[^\w\-]/g,"").replace(/\-+/g,"-").replace(/^\-+|\-+$/g,"");o.push({level:e,title:t,position:r,anchorId:i,children:[]})}return i?o.map(e=>{let t={level:e.level,title:e.title,position:e.position};return r&&(t.link=`#${e.anchorId}`),t}):function(e,t){let n=[],a=[{level:0,children:n}];return e.forEach(e=>{let n={title:e.title,level:e.level,position:e.position,children:[]};for(t&&(n.link=`#${e.anchorId}`);a[a.length-1].level>=e.level;)a.pop();a[a.length-1].children.push(n),a.push(n)}),n}(o,r)},tocToMarkdown:function(e,t={}){let n;let{isNested:a=!0,includeLinks:r=!0}=t;return a?function e(t,n=0,a){let r="",i="  ".repeat(n);return Array.isArray(t)?t.forEach(t=>{let l=a&&t.link?`[${t.title}](${t.link})`:t.title;r+=`${i}- ${l}
`,t.children&&t.children.length>0&&(r+=e(t.children,n+1,a))}):console.warn("Warning: items is not an array in nestedTocToMarkdown"),r}(e,0,r):(n="",e.forEach(e=>{let t="  ".repeat(e.level-1),a=r&&e.link?`[${e.title}](${e.link})`:e.title;n+=`${t}- ${a}
`}),n)}}},57692:(e,t,n)=>{let a=n(21314),r=n(64042),i=n(64239),l=n(1191),o=n(70997),c=n(16365);e.exports={splitMarkdown:function(e,t,n){"string"!=typeof e&&(e=String(e||""));let i=a.extractOutline(e),l=a.splitByHeadings(e,i);return r.processSections(l,i,t,n).map(e=>({result:`> **📑 Summarization：** *${e.summary}*

---

${e.content}`,...e}))},combineMarkdown:l.combineMarkdown,saveToSeparateFiles:o.saveToSeparateFiles,extractTableOfContents:c.extractTableOfContents,tocToMarkdown:c.tocToMarkdown,parser:a,splitter:r,summary:i,formatter:l,fileWriter:o,toc:c}},70997:(e,t,n)=>{let a=n(92048),r=n(55315),{ensureDirectoryExists:i}=n(23157);e.exports={saveToSeparateFiles:function(e,t,n){let l=r.dirname(t),o=r.basename(t).replace(/\.[^/.]+$/,""),c=r.join(l,`${o}_parts`);i(c),function t(i){if(i>=e.length){n(null,c,e.length);return}let l=e[i],s=String(i+1).padStart(3,"0"),d=r.join(c,`${o}_part${s}.md`),h=`> **📑 Summarization：** *${l.summary}*

---

${l.content}`;a.writeFile(d,h,"utf8",e=>{if(e){n(e);return}t(i+1)})}(0)}}},1191:e=>{e.exports={combineMarkdown:function(e){let t="";for(let n=0;n<e.length;n++){let a=e[n];n>0&&(t+="\n\n---\n\n"),t+=`> **📑 Summarization：** *${a.summary}*

---

${a.content}`}return t}}},23157:(e,t,n)=>{let a=n(92048),r=n(55315);e.exports={ensureDirectoryExists:function(e){a.existsSync(e)||a.mkdirSync(e,{recursive:!0})},getFilenameWithoutExt:function(e){return r.basename(e).replace(/\.[^/.]+$/,"")}}},68928:(e,t,n)=>{let a,r,i,l,o;n.r(t),n.d(t,{getProjectChunks:()=>$,getProjectTocByName:()=>v,getProjectTocs:()=>j,splitProjectFile:()=>y});var c=n(24330);n(60166);var s=n(92048),d=n.n(s),h=n(55315),u=n.n(h),g=n(67565),f=n(6142),p=n(1491),w=n(33965);async function m(){if(!a){let e=await Promise.all([n.e(1965),n.e(895),n.e(5013)]).then(n.bind(n,10622));a=e.TokenTextSplitter,r=e.CharacterTextSplitter,i=e.RecursiveCharacterTextSplitter,l=(await Promise.all([n.e(1965),n.e(895)]).then(n.bind(n,70895))).Document,o=n(57692)}}async function b({projectPath:e,fileContent:t,fileName:n,projectId:c,fileId:s}){let h;await m();let g=u().join(e,"task-config.json");try{await d().promises.access(g);let e=await d().promises.readFile(g,"utf8");h=JSON.parse(e)}catch(e){h={textSplitMinLength:1500,textSplitMaxLength:2e3}}let f=h.textSplitMinLength||1500,p=h.textSplitMaxLength||2e3,w=h.chunkSize||1500,b=h.chunkOverlap||200,y=h.separator||"\n\n",$=h.separators||["|","##",">","-"],j=h.splitType||"js",v=h.splitType;if("text"===v){let e=new r({separator:y,chunkSize:w,chunkOverlap:b});return(await e.createDocuments([t])).map((e,t)=>({projectId:c,name:`${u().basename(n,u().extname(n))}-part-${t+1}`,fileId:s,fileName:n,content:e.pageContent,summary:"",size:e.pageContent.length}))}if("token"===v){let e=new a({chunkSize:w,chunkOverlap:b});return(await e.splitText(t)).map((e,t)=>({projectId:c,name:`${u().basename(n,u().extname(n))}-part-${t+1}`,fileId:s,fileName:n,content:e,summary:"",size:e.length}))}if("code"===v){new i({chunkSize:w,chunkOverlap:b,separators:$});let e=i.fromLanguage(j,{chunkSize:w,chunkOverlap:b});return(await e.createDocuments([t])).map((e,t)=>({projectId:c,name:`${u().basename(n,u().extname(n))}-part-${t+1}`,fileId:s,fileName:n,content:e.pageContent,summary:"",size:e.pageContent.length}))}{if("recursive"!==v)return o.splitMarkdown(t,f,p).map((e,t)=>({projectId:c,name:`${u().basename(n,u().extname(n))}-part-${t+1}`,fileId:s,fileName:n,content:e.content,summary:e.summary,size:e.content.length}));let e=new i({chunkSize:w,chunkOverlap:b,separators:$});return(await e.splitDocuments([new l({pageContent:t})])).map((e,t)=>({projectId:c,name:`${u().basename(n,u().extname(n))}-part-${t+1}`,fileId:s,fileName:n,content:e.pageContent,summary:"",size:e.pageContent.length}))}}async function y(e,t){let{fileName:n,fileId:a}=t;try{let t;await m();let r=await (0,g.getProjectRoot)(),i=u().join(r,e);try{t=(await (0,w.AG)(a)).content}catch(e){throw Error(`文件 ${n} 不存在或无法从OSS读取: ${e.message}`)}let l=await b({projectPath:i,fileContent:t,fileName:n,projectId:e,fileId:a});await (0,p.YS)(l);let c=o.extractTableOfContents(t),s=o.tocToMarkdown(c,{isNested:!0}),h=u().join(i,"toc");await (0,g.ensureDir)(h);let f=u().join(h,`${u().basename(n,u().extname(n))}-toc.json`);return await d().promises.writeFile(f,JSON.stringify(c,null,2)),{fileName:n,totalChunks:l.length,chunks:l,toc:s}}catch(e){throw console.error("文本分割出错:",e),e}}async function $(e,t){try{await m();let n=await (0,g.getProjectRoot)(),a=u().join(n,e),r=u().join(a,"toc"),i=await (0,f.getProject)(e),l=await (0,p.W8)(e,t),c={},s="";try{for(let e of(await d().promises.access(r),await d().promises.readdir(r)))if(e.endsWith("-toc.json")){let t=u().join(r,e),n=await d().promises.readFile(t,"utf8"),a=e.replace("-toc.json",".md");try{c[a]=JSON.parse(n),s+="# File："+a+"\n"+o.tocToMarkdown(c[a],{isNested:!0})+"\n"}catch(t){console.error(`解析TOC文件 ${e} 出错:`,t)}}}catch(e){}return{fileResult:{fileName:i.name+".md",totalChunks:l.length,chunks:l,toc:s},chunks:l}}catch(e){throw console.error("获取文本块出错:",e),e}}async function j(e){try{await m();let t=await (0,g.getProjectRoot)(),n=u().join(t,e),a=u().join(n,"toc"),r={},i="";try{for(let e of(await d().promises.access(a),await d().promises.readdir(a)))if(e.endsWith("-toc.json")){let t=u().join(a,e),n=await d().promises.readFile(t,"utf8"),l=e.replace("-toc.json",".md");try{r[l]=JSON.parse(n),i+="# File："+l+"\n"+o.tocToMarkdown(r[l],{isNested:!0})+"\n"}catch(t){console.error(`解析TOC文件 ${e} 出错:`,t)}}}catch(e){}return i}catch(e){throw console.error("获取文本块出错:",e),e}}async function v(e,t){try{await m();let n=await (0,g.getProjectRoot)(),a=u().join(n,e),r=u().join(a,"toc"),i={},l="";try{for(let e of(await d().promises.access(r),await d().promises.readdir(r)))if(e.endsWith(t.replace(".md","")+"-toc.json")){let t=u().join(r,e),n=await d().promises.readFile(t,"utf8"),a=e.replace("-toc.json",".md");try{i[a]=JSON.parse(n),l+="# File："+a+"\n"+o.tocToMarkdown(i[a],{isNested:!0})+"\n"}catch(t){console.error(`解析TOC文件 ${e} 出错:`,t)}}}catch(e){}return l}catch(e){throw console.error("获取文本块出错:",e),e}}(0,n(40618).h)([y,$,j,v]),(0,c.j)("33eec58dea7a417292bcc11c93ad64ce76f83304",y),(0,c.j)("ef4cfd79c31f2a0a1f1dd31903f00e6d6d7ee0ce",$),(0,c.j)("4b2a16d30317e328ef37e3e7c93b51641d7e30f4",j),(0,c.j)("ffc433472ebfd001c6a13b440df018cd18de556a",v)},50121:(e,t,n)=>{n.d(t,{E:()=>a,h:()=>function e(t,n=0,a=0){return Array.isArray(t)?t.map((t,r)=>{let i;if(0===n){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let n=Math.floor(e/10),a=e%10;return 1===n?0===a?"十":"十"+t[a]:t[n]+"十"+(0===a?"":t[a])}(r+1);i=`${e}、${t.label}`}else i=`${a+1}.${r+1} ${t.label}`;let l={...t,displayLabel:i,displayName:t.label};return t.child&&t.child.length>0&&(l.child=e(t.child,n+1,r)),t.children&&t.children.length>0&&(l.children=e(t.children,n+1,r)),l}):[]}});function a(e,t){return e&&Array.isArray(t)?function t(n){for(let a of n){if(a.label===e)return a;if(a.child&&a.child.length>0){let e=t(a.child);if(e)return e}}return null}(t):null}}};