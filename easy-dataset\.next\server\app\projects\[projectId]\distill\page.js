(()=>{var e={};e.id=508,e.ids=[508],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},78074:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),s(24705),s(58138),s(57213),s(35866);var i=s(23191),a=s(88716),l=s(37922),r=s.n(l),n=s(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d=["",{children:["projects",{children:["[projectId]",{children:["distill",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,24705)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\distill\\page.js"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58138)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\layout.js"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,57213)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\distill\\page.js"],u="/projects/[projectId]/distill/page",p={require:s,loadChunk:()=>Promise.resolve()},h=new i.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/projects/[projectId]/distill/page",pathname:"/projects/[projectId]/distill",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},69445:(e,t,s)=>{Promise.resolve().then(s.bind(s,55894))},98508:(e,t,s)=>{Promise.resolve().then(s.bind(s,66760))},55894:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ec});var i=s(10326),a=s(17577),l=s.n(a),r=s(70012),n=s(35047),o=s(36493),d=s(15431),c=s(75616),u=s(9861),p=s(60893),h=s(71728),g=s(30274),x=s(83708),m=s(48260),j=s(42265),Z=s(98139),f=s(84873),y=s(52386),b=s(84979),v=s(44099),C=s(92495),$=s(71411),T=s(24003),w=s(78969),S=s(25886),q=s(85560),P=s(5041),k=s(33182),I=s(6446),B=s(24437),L=s(76165),z=s(15717),D=s(56347),E=s(26644),M=s(42167);function F({question:e,level:t,onDelete:s,onEdit:a,onGenerateDataset:l,processing:n=!1}){let{t:o}=(0,r.$G)();return(0,i.jsxs)($.ZP,{sx:{pl:(t+1)*2,py:.75,borderLeft:"1px dashed rgba(0, 0, 0, 0.1)",ml:2,borderBottom:"1px solid",borderColor:"divider","&:hover":{bgcolor:"action.hover"}},secondaryAction:(0,i.jsxs)(h.Z,{sx:{display:"flex",alignItems:"center",gap:.5},children:[i.jsx(x.Z,{title:o("datasets.generateDataset"),children:i.jsx(m.Z,{size:"small",color:"primary",onClick:e=>l(e),disabled:n,children:n?i.jsx(Z.Z,{size:16}):i.jsx(y.Z,{fontSize:"small"})})}),i.jsx(x.Z,{title:o("domain.editQuestion"),children:i.jsx(m.Z,{size:"small",color:"secondary",onClick:e=>a(e),disabled:n,children:i.jsx(M.Z,{fontSize:"small"})})}),i.jsx(x.Z,{title:o("common.delete"),children:i.jsx(m.Z,{size:"small",color:"error",onClick:e=>s(e),disabled:n,children:i.jsx(E.Z,{fontSize:"small"})})})]}),children:[i.jsx(w.Z,{sx:{minWidth:32,color:"secondary.main"},children:i.jsx(D.Z,{fontSize:"small"})}),i.jsx(S.Z,{primary:(0,i.jsxs)(h.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(g.Z,{variant:"body2",sx:{whiteSpace:"normal",wordBreak:"break-word",paddingRight:"28px"},children:e.question}),e.answered&&i.jsx(q.Z,{size:"small",label:o("datasets.answered"),color:"success",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}})]})})]})}function W({tag:e,level:t=0,expanded:s=!1,onToggle:a,onMenuOpen:l,onGenerateQuestions:n,onGenerateSubTags:o,questions:d=[],loadingQuestions:c=!1,processingQuestions:u={},onDeleteQuestion:p,onEditQuestion:j,onGenerateDataset:y,allQuestions:v=[],tagQuestions:C={},children:D}){let{t:E}=(0,r.$G)(),M=e=>{let t=e.length;return e.forEach(e=>{e.children&&e.children.length>0&&(t+=M(e.children))}),t},W=e=>{let t=0;return e.forEach(e=>{C[e.id]&&C[e.id].length>0?t+=C[e.id].length:t+=v.filter(t=>t.label===e.label).length,e.children&&e.children.length>0&&(t+=W(e.children))}),t},R=(C[e.id]&&C[e.id].length>0?C[e.id].length:v.filter(t=>t.label===e.label).length)+(e.children?W(e.children||[]):0);return(0,i.jsxs)(h.Z,{sx:{my:.5},children:[i.jsx($.ZP,{disablePadding:!0,sx:{pl:2*t,borderLeft:t>0?"1px dashed rgba(0, 0, 0, 0.1)":"none",ml:t>0?2:0},children:(0,i.jsxs)(T.Z,{onClick:()=>a(e.id),sx:{borderRadius:1,py:.5},children:[i.jsx(w.Z,{sx:{minWidth:36},children:i.jsx(k.Z,{color:"primary",fontSize:"small"})}),i.jsx(S.Z,{primary:(0,i.jsxs)(h.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(g.Z,{sx:{fontWeight:"medium"},children:e.displayLabel||e.label}),e.children&&e.children.length>0&&i.jsx(q.Z,{size:"small",label:`${M(e.children)} ${E("distill.subTags")}`,color:"primary",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}}),R>0&&i.jsx(q.Z,{size:"small",label:`${R} ${E("distill.questions")}`,color:"secondary",variant:"outlined",sx:{height:20,fontSize:"0.7rem"}})]}),primaryTypographyProps:{component:"div"}}),(0,i.jsxs)(h.Z,{sx:{display:"flex",alignItems:"center",gap:.5},children:[i.jsx(x.Z,{title:E("distill.generateQuestions"),children:i.jsx(m.Z,{size:"small",onClick:t=>{t.stopPropagation(),n(e)},children:i.jsx(L.Z,{fontSize:"small"})})}),i.jsx(x.Z,{title:E("distill.addChildTag"),children:i.jsx(m.Z,{size:"small",onClick:t=>{t.stopPropagation(),o(e)},children:i.jsx(f.Z,{fontSize:"small"})})}),i.jsx(m.Z,{size:"small",onClick:t=>l(t,e),children:i.jsx(z.Z,{fontSize:"small"})}),e.children&&e.children.length>0?s?i.jsx(B.Z,{fontSize:"small"}):i.jsx(I.Z,{fontSize:"small"}):null]})]})}),e.children&&e.children.length>0&&i.jsx(P.Z,{in:s,timeout:"auto",unmountOnExit:!0,children:D}),s&&i.jsx(P.Z,{in:s,timeout:"auto",unmountOnExit:!0,children:i.jsx(b.Z,{disablePadding:!0,sx:{mt:.5,mb:1},children:c?(0,i.jsxs)($.ZP,{sx:{pl:(t+1)*2,py:.75},children:[i.jsx(Z.Z,{size:20}),i.jsx(g.Z,{variant:"body2",sx:{ml:2},children:E("common.loading")})]}):d&&d.length>0?d.map(e=>i.jsx(F,{question:e,level:t,processing:u[e.id],onDelete:t=>p(e.id,t),onEdit:t=>j(e,t),onGenerateDataset:t=>y(e.id,e.question,t)},e.id)):i.jsx($.ZP,{sx:{pl:(t+1)*2,py:1},children:i.jsx(g.Z,{variant:"body2",color:"text.secondary",children:E("distill.noQuestions")})})})})]},e.id)}var R=s(99544),Q=s(37841);function N({anchorEl:e,open:t,onClose:s,onEdit:a,onDelete:l}){let{t:n}=(0,r.$G)();return(0,i.jsxs)(R.Z,{anchorEl:e,open:t,onClose:s,children:[(0,i.jsxs)(Q.Z,{onClick:a,children:[i.jsx(w.Z,{children:i.jsx(M.Z,{fontSize:"small"})}),i.jsx(S.Z,{children:n("textSplit.editTag")})]}),(0,i.jsxs)(Q.Z,{onClick:l,children:[i.jsx(w.Z,{children:i.jsx(E.Z,{fontSize:"small"})}),i.jsx(S.Z,{children:n("common.delete")})]})]})}var G=s(69995),O=s(98117),A=s(10163);function H({open:e,onClose:t,onConfirm:s,title:a,cancelText:l="取消",confirmText:r="确认",confirmColor:n="error"}){return(0,i.jsxs)(G.Z,{open:e,onClose:t,"aria-labelledby":"confirm-dialog-title",children:[i.jsx(O.Z,{id:"confirm-dialog-title",children:a}),(0,i.jsxs)(A.Z,{children:[i.jsx(j.Z,{onClick:t,color:"primary",children:l}),i.jsx(j.Z,{onClick:s,color:n,autoFocus:!0,children:r})]})]})}var _=s(28591),V=s(14350);function U({open:e,onClose:t,tag:s,projectId:l,onSuccess:n}){let{t:o}=(0,r.$G)(),[d,c]=(0,a.useState)(""),[u,p]=(0,a.useState)(!1),[x,m]=(0,a.useState)(""),Z=()=>{c(""),m(""),p(!1)},f=()=>{Z(),t()},y=async()=>{if(!d.trim()){m(o("distill.tagNameRequired"));return}if(d.trim()===s?.label){f();return}try{p(!0),m("");let e=await v.Z.put(`/api/projects/${l}/tags`,{tags:{id:s.id,label:d.trim(),parentId:s.parentId}});e.data&&(n&&n(e.data.tags),f())}catch(e){console.error("编辑标签失败:",e),m(e.response?.data?.error||o("distill.editTagError"))}finally{p(!1)}};return(0,i.jsxs)(G.Z,{open:e,onClose:f,maxWidth:"sm",fullWidth:!0,children:[i.jsx(O.Z,{children:o("textSplit.editTag")}),i.jsx(_.Z,{children:(0,i.jsxs)(h.Z,{sx:{pt:1},children:[i.jsx(V.Z,{autoFocus:!0,fullWidth:!0,label:o("domain.tagName"),value:d,onChange:e=>c(e.target.value),onKeyPress:e=>{"Enter"===e.key&&y()},error:!!x,helperText:x,disabled:u,placeholder:o("domain.inputEditTagName")}),s&&(0,i.jsxs)(g.Z,{variant:"body2",color:"text.secondary",sx:{mt:2},children:[o("domain.originalTagName"),": ",s.label]})]})}),(0,i.jsxs)(A.Z,{children:[i.jsx(j.Z,{onClick:f,disabled:u,children:o("common.cancel")}),i.jsx(j.Z,{onClick:y,variant:"contained",disabled:u||!d.trim(),children:u?o("common.saving"):o("common.save")})]})]})}var K=s(19787);function J({open:e,onClose:t,question:s,projectId:l,tags:n=[],onSuccess:o}){let{t:d}=(0,r.$G)(),[c,u]=(0,a.useState)(""),[p,x]=(0,a.useState)([]),[m,Z]=(0,a.useState)(!1),[f,y]=(0,a.useState)(""),b=()=>{u(""),x([]),y(""),Z(!1)},C=()=>{b(),t()},$=async()=>{if(!c.trim()){y(d("distill.questionRequired"));return}if(0===p.length){y(d("distill.tagRequired"));return}let e=c.trim()!==s?.question,t=JSON.stringify(p.map(e=>e.id).sort())!==JSON.stringify((s?.tags||[s?.tag].filter(Boolean)).map(e=>e.id).sort());if(!e&&!t){C();return}try{Z(!0),y("");let e={question:c.trim(),tagIds:p.map(e=>e.id)},t=await v.Z.put(`/api/projects/${l}/questions/${s.id}`,e);t.data&&(o&&o(t.data),C())}catch(e){console.error("编辑问题失败:",e),y(e.response?.data?.error||d("distill.editQuestionError"))}finally{Z(!1)}};return(0,i.jsxs)(G.Z,{open:e,onClose:C,maxWidth:"md",fullWidth:!0,children:[i.jsx(O.Z,{children:d("domain.editQuestion")}),i.jsx(_.Z,{children:(0,i.jsxs)(h.Z,{sx:{pt:1},children:[i.jsx(V.Z,{autoFocus:!0,fullWidth:!0,label:d("domain.questionContent"),value:c,onChange:e=>u(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),$())},multiline:!0,rows:4,error:!!f&&f.includes(d("domain.questionRequired")),disabled:m,placeholder:d("domain.inputEditQuestionContent"),sx:{mb:2}}),i.jsx(K.Z,{multiple:!0,options:n.filter(e=>e.id&&e.label),getOptionLabel:e=>e.label||"",value:p,onChange:(e,t)=>{x(t)},isOptionEqualToValue:(e,t)=>e.id===t.id,renderTags:(e,t)=>e.map((e,s)=>(0,a.createElement)(q.Z,{variant:"outlined",label:e.label,...t({index:s}),key:e.id})),renderInput:e=>i.jsx(V.Z,{...e,label:d("domain.selectTags"),placeholder:d("domain.selectTagsPlaceholder"),error:!!f&&f.includes(d("domain.tagRequired")),disabled:m}),disabled:m}),f&&i.jsx(g.Z,{variant:"body2",color:"error",sx:{mt:1},children:f}),s&&(0,i.jsxs)(h.Z,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1},children:[(0,i.jsxs)(g.Z,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:[d("domain.originalQuestion"),":"]}),i.jsx(g.Z,{variant:"body2",sx:{mb:1},children:s.question}),(0,i.jsxs)(g.Z,{variant:"body2",color:"text.secondary",children:[d("domain.originalTags"),": ",(s.tags||[s.tag].filter(Boolean)).map(e=>e?.label).join(", ")]})]})]})}),(0,i.jsxs)(A.Z,{children:[i.jsx(j.Z,{onClick:C,disabled:m,children:d("common.cancel")}),i.jsx(j.Z,{onClick:$,variant:"contained",disabled:m||!c.trim()||0===p.length,children:m?d("common.saving"):d("common.save")})]})]})}let X=e=>e.length>0&&e[0].displayLabel?e:[...e].sort((e,t)=>{let s=e=>{let t=e.match(/^([\d.]+)\s/);return t?t[1]:null},i=s(e.label),a=s(t.label);if(i&&a){let e=i.split(".").map(e=>parseInt(e,10)),t=a.split(".").map(e=>parseInt(e,10));for(let s=0;s<Math.min(e.length,t.length);s++)if(e[s]!==t[s])return e[s]-t[s];return e.length-t.length}return i?-1:a?1:e.label.localeCompare(t.label,"zh-CN")}),Y=(0,a.forwardRef)(function({projectId:e,tags:t=[],onGenerateSubTags:s,onGenerateQuestions:l},n){let{t:o}=(0,r.$G)(),[d,c]=(0,a.useState)({}),[u,p]=(0,a.useState)({}),[x,m]=(0,a.useState)({}),[j,Z]=(0,a.useState)({}),[f,y]=(0,a.useState)(null),[$,T]=(0,a.useState)(null),[w,S]=(0,a.useState)([]),[q,P]=(0,a.useState)(!1),[k,I]=(0,a.useState)({}),[B,L]=(0,a.useState)(!1),[z,D]=(0,a.useState)(null),[E,M]=(0,a.useState)(!1),[F,R]=(0,a.useState)(null),[Q,G]=(0,a.useState)(!1),[O,A]=(0,a.useState)(null),[_,V]=(0,a.useState)(!1),[K,Y]=(0,a.useState)(null),[ee,et]=(0,a.useState)(null),[es,ei]=(0,a.useState)(""),{generateSingleDataset:ea}=(0,C.l)(),el=(0,a.useCallback)(async()=>{try{P(!0);let t=await v.Z.get(`/api/projects/${e}/questions/tree?isDistill=true`);S(t.data),console.log("获取问题统计信息成功:",{totalQuestions:t.data.length})}catch(e){console.error("获取问题统计信息失败:",e)}finally{P(!1)}},[e]);(0,a.useImperativeHandle)(n,()=>({fetchQuestionsStats:el}));let er=(0,a.useCallback)(async t=>{try{Z(e=>({...e,[t]:!0}));let s=await v.Z.get(`/api/projects/${e}/distill/questions/by-tag?tagId=${t}`);p(e=>({...e,[t]:s.data}))}catch(e){console.error("获取标签问题失败:",e)}finally{Z(e=>({...e,[t]:!1}))}},[e]);(0,a.useEffect)(()=>{e&&v.Z.get(`/api/projects/${e}`).then(e=>{et(e.data),ei(e.data.name||"")}).catch(e=>{console.error("获取项目信息失败:",e)})},[e]),(0,a.useEffect)(()=>{el()},[el]);let en=(0,a.useMemo)(()=>{if(!t||0===t.length)return[];let e={};t.forEach(t=>{e[t.id]={...t,children:[]}});let s=[];return t.forEach(t=>{t.parentId&&e[t.parentId]?e[t.parentId].children.push(e[t.id]):s.push(e[t.id])}),function e(t,s=0,i=0){return Array.isArray(t)?t.map((t,a)=>{let l;if(0===s){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let s=Math.floor(e/10),i=e%10;return 1===s?0===i?"十":"十"+t[i]:t[s]+"十"+(0===i?"":t[i])}(a+1);l=`${e}、${t.label}`}else l=`${i+1}.${a+1} ${t.label}`;let r={...t,displayLabel:l,displayName:t.label};return t.child&&t.child.length>0&&(r.child=e(t.child,s+1,a)),t.children&&t.children.length>0&&(r.children=e(t.children,s+1,a)),r}):[]}(s)},[t]),eo=(0,a.useCallback)(e=>{c(t=>({...t,[e]:!t[e]})),d[e]||u[e]||er(e)},[d,u,er]),ed=(e,t)=>{e.stopPropagation(),y(e.currentTarget),T(t)},ec=()=>{y(null),T(null)},eu=async e=>{console.log("标签编辑成功:",e),window.location.reload()},ep=(e,t)=>{t.stopPropagation(),console.log("打开问题编辑对话框",e),Y(e),V(!0)},eh=async e=>{console.log("问题编辑成功:",e),await el(),Object.keys(d).forEach(e=>{d[e]&&er(e)})},eg=()=>{M(!1)},ex=(e,t)=>{t.stopPropagation(),D(e),L(!0)},em=()=>{L(!1),D(null)},ej=async()=>{if(z)try{await v.Z.delete(`/api/projects/${e}/questions/${z}`),p(e=>{let t={...e};return Object.keys(t).forEach(e=>{t[e]=t[e].filter(e=>e.id!==z)}),t}),em()}catch(e){console.error("删除问题失败:",e)}},eZ=async(t,s,i)=>{i.stopPropagation(),I(e=>({...e,[t]:!0})),await ea({projectId:e,questionId:t,questionInfo:s}),I(e=>({...e,[t]:!1}))},ef=(0,a.useCallback)(e=>{if(!e)return"";let s=(e,i=[])=>{let a=[e.label,...i];if(!e.parentId)return es&&!a.includes(es)?[es,...a]:a;let l=t.find(t=>t.id===e.parentId);return l?s(l,a):es&&!a.includes(es)?[es,...a]:a},i=s(e);return es&&i.length>0&&i[0]!==es&&i.unshift(es),i.join(" > ")},[t,es]),ey=(e,t=0)=>{let a=X(e);return i.jsx(b.Z,{disablePadding:!0,sx:{px:2},children:a.map(e=>i.jsx(W,{tag:e,level:t,expanded:d[e.id],onToggle:eo,onMenuOpen:ed,onGenerateQuestions:e=>{(async()=>{await l(e,ef(e)),await el(),d[e.id]&&await er(e.id)})()},onGenerateSubTags:e=>s(e,ef(e)),questions:u[e.id]||[],loadingQuestions:j[e.id],processingQuestions:k,onDeleteQuestion:ex,onEditQuestion:ep,onGenerateDataset:eZ,allQuestions:w,tagQuestions:u,children:e.children&&e.children.length>0&&d[e.id]&&ey(e.children,t+1)},e.id))})};return(0,i.jsxs)(h.Z,{children:[en.length>0?ey(en):i.jsx(h.Z,{sx:{p:2,textAlign:"center"},children:i.jsx(g.Z,{variant:"body1",color:"text.secondary",children:o("distill.noTags")})}),i.jsx(N,{anchorEl:f,open:!!f,onClose:ec,onDelete:()=>{console.log("打开删除确认对话框",$),R($),M(!0),ec()},onEdit:()=>{console.log("打开编辑对话框",$),A($),G(!0),ec()}}),i.jsx(H,{open:E,onClose:eg,onConfirm:()=>{if(!F){console.log("没有要删除的标签信息");return}console.log("开始删除标签:",F.id,F.label),eg(),(async()=>{try{console.log("发送删除请求:",`/api/projects/${e}/tags?id=${F.id}`);let t=await v.Z.delete(`/api/projects/${e}/tags?id=${F.id}`);console.log("删除标签成功:",t.data),window.location.reload()}catch(e){console.error("删除标签失败:",e),console.error("错误详情:",e.response?e.response.data:"无响应数据"),alert(`删除标签失败: ${e.message}`)}})()},title:o("distill.deleteTagConfirmTitle"),cancelText:o("common.cancel"),confirmText:o("common.delete"),confirmColor:"error"}),i.jsx(H,{open:B,onClose:em,onConfirm:ej,title:o("questions.deleteConfirm"),cancelText:o("common.cancel"),confirmText:o("common.delete"),confirmColor:"error"}),i.jsx(U,{open:Q,onClose:()=>{G(!1),A(null)},tag:O,projectId:e,onSuccess:eu}),i.jsx(J,{open:_,onClose:()=>{V(!1),Y(null)},question:K,projectId:e,tags:t,onSuccess:eh})]})});var ee=s(27387),et=s(23297);function es({open:e,onClose:t,onGenerated:s,projectId:l,parentTag:n,tagPath:o,model:d}){let{t:c}=(0,r.$G)(),[x,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)(""),[C,$]=(0,a.useState)(5),[T,w]=(0,a.useState)([]),[S,P]=(0,a.useState)(""),[k,I]=(0,a.useState)(null),B=async()=>{try{f(!0),b("");let e=await v.Z.post(`/api/projects/${l}/distill/tags`,{parentTag:S,parentTagId:n?n.id:null,tagPath:o||S,count:C,model:d,language:et.Z.language});w(e.data)}catch(e){console.error("生成标签失败:",e),b(e.response?.data?.error||c("distill.generateTagsError"))}finally{f(!1)}},L=async()=>{s&&s(T),z()},z=()=>{w([]),b(""),$(5),t&&t()};return(0,i.jsxs)(G.Z,{open:e,onClose:z,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2}},children:[(0,i.jsxs)(O.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[i.jsx(g.Z,{variant:"h6",component:"div",children:n?c("distill.generateSubTagsTitle",{parentTag:n.label}):c("distill.generateRootTagsTitle")}),i.jsx(m.Z,{edge:"end",color:"inherit",onClick:z,"aria-label":"close",children:i.jsx(ee.Z,{})})]}),(0,i.jsxs)(_.Z,{dividers:!0,children:[y&&i.jsx(u.Z,{severity:"error",sx:{mb:2},children:y}),n&&o&&(0,i.jsxs)(h.Z,{sx:{mb:3},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[c("distill.tagPath"),":"]}),i.jsx(p.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:i.jsx(g.Z,{variant:"body1",children:o||n.label})})]}),(0,i.jsxs)(h.Z,{sx:{mb:3},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[c("distill.parentTag"),":"]}),i.jsx(V.Z,{fullWidth:!0,variant:"outlined",value:S,onChange:e=>P(e.target.value),placeholder:c("distill.parentTagPlaceholder"),disabled:x||!n,InputProps:{readOnly:!n},helperText:n?c("distill.parentTagHelp"):c("distill.rootTopicHelperText",{defaultValue:"使用项目名称作为顶级主题"})})]}),(0,i.jsxs)(h.Z,{sx:{mb:3},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[c("distill.tagCount"),":"]}),i.jsx(V.Z,{fullWidth:!0,variant:"outlined",type:"number",value:C,onChange:e=>{let t=parseInt(e.target.value);!isNaN(t)&&t>=1&&t<=100&&$(t)},inputProps:{min:1,max:100},disabled:x,helperText:c("distill.tagCountHelp")})]}),T.length>0&&(0,i.jsxs)(h.Z,{children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[c("distill.generatedTags"),":"]}),i.jsx(p.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:i.jsx(h.Z,{sx:{display:"flex",flexWrap:"wrap",gap:1},children:T.map((e,t)=>i.jsx(q.Z,{label:e.label,color:"primary",variant:"outlined"},t))})})]})]}),(0,i.jsxs)(A.Z,{sx:{p:2},children:[i.jsx(j.Z,{onClick:z,color:"inherit",children:c("common.cancel")}),T.length>0?i.jsx(j.Z,{onClick:L,color:"primary",variant:"contained",children:c("common.complete")}):i.jsx(j.Z,{variant:"contained",color:"primary",onClick:B,disabled:x||!S,startIcon:x&&i.jsx(Z.Z,{size:20,color:"inherit"}),children:x?c("common.generating"):c("distill.generateTags")})]})]})}var ei=s(99207);function ea({open:e,onClose:t,onGenerated:s,projectId:n,tag:o,tagPath:d,model:c}){let{t:x}=(0,r.$G)(),[f,y]=(0,a.useState)(!1),[C,T]=(0,a.useState)(""),[w,q]=(0,a.useState)(5),[P,k]=(0,a.useState)([]),I=async()=>{try{y(!0),T("");let e=await v.Z.post(`/api/projects/${n}/distill/questions`,{tagPath:d,currentTag:o.label,tagId:o.id,count:w,model:c,language:et.Z.language});k(e.data)}catch(e){console.error("生成问题失败:",e),T(e.response?.data?.error||x("distill.generateQuestionsError"))}finally{y(!1)}},B=async()=>{s&&s(P),L()},L=()=>{k([]),T(""),q(5),t&&t()};return(0,i.jsxs)(G.Z,{open:e,onClose:L,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2}},children:[(0,i.jsxs)(O.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[i.jsx(g.Z,{variant:"h6",component:"div",children:x("distill.generateQuestionsTitle",{tag:o?.label||x("distill.unknownTag")})}),i.jsx(m.Z,{edge:"end",color:"inherit",onClick:L,"aria-label":"close",children:i.jsx(ee.Z,{})})]}),(0,i.jsxs)(_.Z,{dividers:!0,children:[C&&i.jsx(u.Z,{severity:"error",sx:{mb:2},children:C}),(0,i.jsxs)(h.Z,{sx:{mb:3},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[x("distill.tagPath"),":"]}),i.jsx(p.Z,{variant:"outlined",sx:{p:2,borderRadius:1,backgroundColor:"background.paper"},children:i.jsx(g.Z,{variant:"body1",children:d||o?.label||x("distill.unknownTag")})})]}),(0,i.jsxs)(h.Z,{sx:{mb:3},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[x("distill.questionCount"),":"]}),i.jsx(V.Z,{fullWidth:!0,variant:"outlined",type:"number",value:w,onChange:e=>{let t=parseInt(e.target.value);!isNaN(t)&&t>=1&&t<=100&&q(t)},inputProps:{min:1,max:100},disabled:f,helperText:x("distill.questionCountHelp")})]}),P.length>0&&(0,i.jsxs)(h.Z,{children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",gutterBottom:!0,children:[x("distill.generatedQuestions"),":"]}),i.jsx(p.Z,{variant:"outlined",sx:{p:0,borderRadius:1,backgroundColor:"background.paper"},children:i.jsx(b.Z,{disablePadding:!0,children:P.map((e,t)=>(0,i.jsxs)(l().Fragment,{children:[t>0&&i.jsx(ei.Z,{}),i.jsx($.ZP,{children:i.jsx(S.Z,{primary:e.question,primaryTypographyProps:{style:{whiteSpace:"normal",wordBreak:"break-word"}}})})]},t))})})]})]}),(0,i.jsxs)(A.Z,{sx:{p:2},children:[i.jsx(j.Z,{onClick:L,color:"inherit",children:x("common.cancel")}),P.length>0?i.jsx(j.Z,{onClick:B,color:"primary",variant:"contained",children:x("common.complete")}):i.jsx(j.Z,{variant:"contained",color:"primary",onClick:I,disabled:f,startIcon:f&&i.jsx(Z.Z,{size:20,color:"inherit"}),children:f?x("common.generating"):x("distill.generateQuestions")})]})]})}function el({open:e,onClose:t,onStart:s,projectId:l,project:n,stats:o={}}){let{t:d}=(0,r.$G)(),[c,x]=(0,a.useState)(""),[m,Z]=(0,a.useState)(2),[f,y]=(0,a.useState)(10),[b,v]=(0,a.useState)(10),[C,$]=(0,a.useState)(0),[T,w]=(0,a.useState)(0),[S,q]=(0,a.useState)(0),[P,k]=(0,a.useState)(0),[I,B]=(0,a.useState)(0),[L,z]=(0,a.useState)("");return(0,i.jsxs)(G.Z,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,children:[i.jsx(O.Z,{children:d("distill.autoDistillTitle")}),i.jsx(_.Z,{children:(0,i.jsxs)(h.Z,{sx:{py:2,display:"flex",flexDirection:{xs:"column",md:"row"},gap:3},children:[(0,i.jsxs)(h.Z,{sx:{flex:1},children:[i.jsx(V.Z,{label:d("distill.distillTopic"),value:c,onChange:e=>x(e.target.value),fullWidth:!0,margin:"normal",required:!0,disabled:!0,helperText:d("distill.rootTopicHelperText")}),(0,i.jsxs)(h.Z,{sx:{mt:3,mb:2},children:[i.jsx(g.Z,{gutterBottom:!0,children:d("distill.tagLevels")}),i.jsx(V.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:5}},value:m,onChange:e=>{Z(Math.min(5,Math.max(1,Number(e.target.value))))},helperText:d("distill.tagLevelsHelper",{max:5})})]}),(0,i.jsxs)(h.Z,{sx:{mt:3,mb:2},children:[i.jsx(g.Z,{gutterBottom:!0,children:d("distill.tagsPerLevel")}),i.jsx(V.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:50}},value:f,onChange:e=>{y(Math.min(50,Math.max(1,Number(e.target.value))))},helperText:d("distill.tagsPerLevelHelper",{max:50})})]}),(0,i.jsxs)(h.Z,{sx:{mt:3,mb:2},children:[i.jsx(g.Z,{gutterBottom:!0,children:d("distill.questionsPerTag")}),i.jsx(V.Z,{type:"number",fullWidth:!0,InputProps:{inputProps:{min:1,max:50}},value:b,onChange:e=>{v(Math.min(50,Math.max(1,Number(e.target.value))))},helperText:d("distill.questionsPerTagHelper",{max:50})})]})]}),(0,i.jsxs)(h.Z,{sx:{flex:1,display:"flex",flexDirection:"column"},children:[(0,i.jsxs)(p.Z,{variant:"outlined",sx:{p:3,mt:1,borderRadius:2,flex:1,display:"flex",flexDirection:"column"},children:[i.jsx(g.Z,{variant:"h6",fontWeight:"bold",gutterBottom:!0,children:d("distill.estimationInfo")}),(0,i.jsxs)(h.Z,{sx:{flex:1,display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[(0,i.jsxs)(h.Z,{children:[(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2,mt:2},children:[(0,i.jsxs)(g.Z,{variant:"subtitle2",children:[d("distill.estimatedTags"),":"]}),i.jsx(g.Z,{variant:"subtitle1",fontWeight:"medium",children:C})]}),(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,i.jsxs)(g.Z,{variant:"subtitle2",children:[d("distill.estimatedQuestions"),":"]}),i.jsx(g.Z,{variant:"subtitle1",fontWeight:"medium",children:S})]}),i.jsx(ei.Z,{sx:{my:2}}),(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,i.jsxs)(g.Z,{variant:"subtitle2",children:[d("distill.currentTags"),":"]}),i.jsx(g.Z,{variant:"subtitle1",fontWeight:"medium",children:o.tagsCount||0})]}),(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[(0,i.jsxs)(g.Z,{variant:"subtitle2",children:[d("distill.currentQuestions"),":"]}),i.jsx(g.Z,{variant:"subtitle1",fontWeight:"medium",children:o.questionsCount||0})]})]}),(0,i.jsxs)(h.Z,{sx:{pt:2,borderTop:"1px dashed",borderColor:"divider"},children:[(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",color:"primary",children:[d("distill.newTags"),":"]}),i.jsx(g.Z,{variant:"h6",fontWeight:"bold",color:"primary.main",children:P})]}),(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,i.jsxs)(g.Z,{variant:"subtitle1",color:"primary",children:[d("distill.newQuestions"),":"]}),i.jsx(g.Z,{variant:"h6",fontWeight:"bold",color:"primary.main",children:I})]})]})]})]}),L&&i.jsx(u.Z,{severity:"error",sx:{mt:2},children:L})]})]})}),(0,i.jsxs)(A.Z,{children:[i.jsx(j.Z,{onClick:t,children:d("common.cancel")}),i.jsx(j.Z,{onClick:()=>{L||s({topic:c,levels:m,tagsPerLevel:f,questionsPerTag:b,estimatedTags:C,estimatedQuestions:S})},color:"primary",variant:"contained",disabled:!!L||!c,children:d("distill.startAutoDistill")})]})]})}var er=s(3735);function en({open:e,onClose:t,progress:s={}}){let{t:l}=(0,r.$G)(),n=(0,a.useRef)(null),o=()=>{let{tagsBuilt:e,tagsTotal:t,questionsBuilt:i,questionsTotal:a,datasetsBuilt:l,datasetsTotal:r}=s;return Math.min(100,Math.round((t?e/t*30:0)+(a?i/a*35:0)+(r?l/r*35:0)))};return(0,i.jsxs)(G.Z,{open:e,onClose:"completed"!==s.stage&&s.stage?null:t,maxWidth:"md",fullWidth:!0,children:[i.jsx(O.Z,{children:(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[l("distill.autoDistillProgress"),("completed"===s.stage||!s.stage)&&i.jsx(m.Z,{onClick:t,"aria-label":"close",children:i.jsx(ee.Z,{})})]})}),i.jsx(_.Z,{children:(0,i.jsxs)(h.Z,{sx:{py:1},children:[(0,i.jsxs)(h.Z,{sx:{mb:4},children:[i.jsx(g.Z,{variant:"h6",gutterBottom:!0,children:l("distill.overallProgress")}),(0,i.jsxs)(h.Z,{sx:{mb:2},children:[i.jsx(er.Z,{variant:"determinate",value:o(),sx:{height:10,borderRadius:5}}),i.jsx(h.Z,{sx:{display:"flex",justifyContent:"flex-end",mt:.5},children:(0,i.jsxs)(g.Z,{variant:"body2",color:"text.secondary",children:[o(),"%"]})})]}),(0,i.jsxs)(h.Z,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:[(0,i.jsxs)(p.Z,{variant:"outlined",sx:{p:2},children:[i.jsx(g.Z,{variant:"body2",color:"text.secondary",children:l("distill.tagsProgress")}),(0,i.jsxs)(g.Z,{variant:"h6",children:[s.tagsBuilt||0," / ",s.tagsTotal||0]})]}),(0,i.jsxs)(p.Z,{variant:"outlined",sx:{p:2},children:[i.jsx(g.Z,{variant:"body2",color:"text.secondary",children:l("distill.questionsProgress")}),(0,i.jsxs)(g.Z,{variant:"h6",children:[s.questionsBuilt||0," / ",s.questionsTotal||0]})]}),(0,i.jsxs)(p.Z,{variant:"outlined",sx:{p:2},children:[i.jsx(g.Z,{variant:"body2",color:"text.secondary",children:l("distill.datasetsProgress")}),(0,i.jsxs)(g.Z,{variant:"h6",children:[s.datasetsBuilt||0," / ",s.datasetsTotal||0]})]})]})]}),(0,i.jsxs)(h.Z,{sx:{mb:4},children:[i.jsx(g.Z,{variant:"h6",gutterBottom:!0,children:l("distill.currentStage")}),i.jsx(p.Z,{variant:"outlined",sx:{p:2,bgcolor:"primary.light",color:"primary.contrastText"},children:i.jsx(g.Z,{variant:"h6",children:(()=>{let{stage:e}=s;switch(e){case"level1":return l("distill.stageBuildingLevel1");case"level2":return l("distill.stageBuildingLevel2");case"level3":return l("distill.stageBuildingLevel3");case"level4":return l("distill.stageBuildingLevel4");case"level5":return l("distill.stageBuildingLevel5");case"questions":return l("distill.stageBuildingQuestions");case"datasets":return l("distill.stageBuildingDatasets");case"completed":return l("distill.stageCompleted");default:return l("distill.stageInitializing")}})()})})]}),(0,i.jsxs)(h.Z,{sx:{mb:2},children:[i.jsx(g.Z,{variant:"h6",gutterBottom:!0,children:l("distill.realTimeLogs")}),i.jsx(p.Z,{variant:"outlined",sx:{p:2,maxHeight:250,overflow:"auto",bgcolor:"grey.900",color:"grey.100",fontFamily:"monospace",fontSize:"0.875rem"},ref:n,children:s.logs?.length>0?s.logs.map((e,t)=>{let s="inherit";return(e.includes("成功")||e.includes("完成")||e.includes("Successfully"))&&(s="#4caf50"),(e.includes("失败")||e.toLowerCase().includes("error"))&&(s="#f44336"),i.jsx(h.Z,{sx:{mb:.5,color:s},children:e},t)}):i.jsx(g.Z,{variant:"body2",color:"grey.500",children:l("distill.waitingForLogs")})})]})]})})]})}class eo{async executeDistillTask(e){let{projectId:t,topic:s,levels:i,tagsPerLevel:a,questionsPerTag:l,model:r,language:n,concurrencyLimit:o=5,onProgress:d,onLog:c}=e;this.projectName="";try{d&&d({stage:"initializing",tagsTotal:0,tagsBuilt:0,questionsTotal:0,questionsBuilt:0,datasetsTotal:0,datasetsBuilt:0});try{let e=await v.Z.get(`/api/projects/${t}`);e&&e.data&&e.data.name?(this.projectName=e.data.name,this.addLog(c,`Using project name "${this.projectName}" as the top-level tag`)):(this.projectName=s,this.addLog(c,`Could not find project name, using topic "${s}" as the top-level tag`))}catch(e){this.projectName=s,this.addLog(c,`Failed to get project name, using topic "${s}" instead: ${e.message}`)}this.addLog(c,`Starting to build tag tree for "${s}", number of levels: ${i}, tags per level: ${a}, questions per tag: ${l}`),await this.buildTagTree({projectId:t,topic:s,levels:i,tagsPerLevel:a,model:r,language:n,onProgress:d,onLog:c}),await this.generateQuestionsForTags({projectId:t,levels:i,questionsPerTag:l,model:r,language:n,concurrencyLimit:o,onProgress:d,onLog:c}),await this.generateDatasetsForQuestions({projectId:t,model:r,language:n,concurrencyLimit:o,onProgress:d,onLog:c}),d&&d({stage:"completed"}),this.addLog(c,"Auto distillation task completed")}catch(e){throw console.error("自动蒸馏任务执行失败:",e),this.addLog(c,`Task execution error: ${e.message||"Unknown error"}`),e}}async buildTagTree(e){let{projectId:t,topic:s,levels:i,tagsPerLevel:a,model:l,language:r,onProgress:n,onLog:o}=e,d=this.projectName||s,c=async(e=null,u="",p=1)=>{if(n&&n({stage:`level${p}`}),p>i)return;let h=[];try{let s=await v.Z.get(`/api/projects/${t}/distill/tags/all`);h=e?s.data.filter(t=>t.parentId===e.id):s.data.filter(e=>!e.parentId)}catch(e){console.error(`获取${p}级标签失败:`,e),this.addLog(o,`Failed to get ${p} level tags: ${e.message}`);return}let g=Math.max(0,a-h.length);if(g>0){let i;let c=1===p?s:e?.label||"";this.addLog(o,`Tag tree level ${p}: Creating ${a} subtags for "${c}"...`),i=1===p?d:u?u.startsWith(d)?u:`${d} > ${u}`:d;try{let s=await v.Z.post(`/api/projects/${t}/distill/tags`,{parentTag:c,parentTagId:e?e.id:null,tagPath:i||c,count:g,model:l,language:r});n&&n({tagsBuilt:s.data.length,updateType:"increment"}),this.addLog(o,`Successfully created ${s.data.length} tags: ${s.data.map(e=>e.label).join(", ")}`),h=[...h,...s.data]}catch(e){console.error(`创建${p}级标签失败:`,e),this.addLog(o,`Failed to create ${p} level tags: ${e.message||"Unknown error"}`)}}if(p<i)for(let e of h){let t;t=u?`${u} > ${e.label}`:`${d} > ${e.label}`,await c(e,t,p+1)}};n&&n({tagsTotal:Math.pow(a,i)}),await c()}async generateQuestionsForTags(e){let{projectId:t,levels:s,questionsPerTag:i,model:a,language:l,concurrencyLimit:r=5,onProgress:n,onLog:o}=e;n&&n({stage:"questions"}),this.addLog(o,"Tag tree built, starting to generate questions for leaf tags...");try{let e=(await v.Z.get(`/api/projects/${t}/distill/tags/all`)).data,d=[],c={};e.forEach(e=>{e.parentId&&(c[e.parentId]||(c[e.parentId]=[]),c[e.parentId].push(e))}),e.forEach(t=>{c[t.id]||this.getTagDepth(t,e)!==s||d.push(t)}),this.addLog(o,`Found ${d.length} leaf tags, starting to generate questions...`);let u=(await v.Z.get(`/api/projects/${t}/questions/tree?isDistill=true`)).data,p=d.length*i;n&&n({questionsTotal:p});let h=[];for(let t of d){let s=this.getTagPath(t,e),a=u.filter(e=>e.label===t.label),l=Math.max(0,i-a.length);l>0?(h.push({tag:t,tagPath:s,needToCreate:l}),this.addLog(o,`Preparing to generate ${l} questions for tag "${t.label}"...`)):this.addLog(o,`Tag "${t.label}" already has ${a.length} questions, no need to generate new questions`)}this.addLog(o,`Total ${h.length} tags need questions, concurrency limit: ${r}`);for(let e=0;e<h.length;e+=r){let s=h.slice(e,e+r);await Promise.all(s.map(async e=>{let{tag:s,tagPath:i,needToCreate:r}=e;this.addLog(o,`Generating ${r} questions for tag "${s.label}"...`);try{let e=await v.Z.post(`/api/projects/${t}/distill/questions`,{tagPath:i,currentTag:s.label,tagId:s.id,count:r,model:a,language:l});n&&n({questionsBuilt:e.data.length,updateType:"increment"}),e.data.map(e=>e.question||e.content).slice(0,3).join("\n"),this.addLog(o,`Successfully generated ${e.data.length} questions for tag "${s.label}"`)}catch(e){console.error(`为标签 "${s.label}" 生成问题失败:`,e),this.addLog(o,`Failed to generate questions for tag "${s.label}": ${e.message||"Unknown error"}`)}})),this.addLog(o,`Completed batch ${Math.min(e+r,h.length)}/${h.length} of question generation`)}}catch(e){console.error("获取标签失败:",e),this.addLog(o,`Failed to get tags: ${e.message||"Unknown error"}`)}}async generateDatasetsForQuestions(e){let{projectId:t,model:s,language:i,concurrencyLimit:a=5,onProgress:l,onLog:r}=e;l&&l({stage:"datasets"}),this.addLog(r,"Question generation completed, starting to generate answers...");try{let e=(await v.Z.get(`/api/projects/${t}/questions/tree?isDistill=true`)).data,n=e.filter(e=>!e.answered),o=e.filter(e=>e.answered);l&&l({datasetsTotal:e.length,datasetsBuilt:o.length}),this.addLog(r,`Found ${n.length} unanswered questions, preparing to generate answers...`),this.addLog(r,`Dataset generation concurrency limit: ${a}`);for(let e=0;e<n.length;e+=a){let o=n.slice(e,e+a);await Promise.all(o.map(async e=>{let a=`${e.label} 下的问题ID:${e.id}`;this.addLog(r,`Generating answer for "${a}"...`);try{await this.generateSingleDataset({projectId:t,questionId:e.id,questionInfo:e,model:s,language:i}),l&&l({datasetsBuilt:1,updateType:"increment"}),this.addLog(r,`Successfully generated answer for question "${a}"`)}catch(t){console.error(`Failed to generate dataset for question "${e.id}":`,t),this.addLog(r,`Failed to generate answer for question "${a}": ${t.message||"Unknown error"}`)}})),this.addLog(r,`Completed batch ${Math.min(e+a,n.length)}/${n.length} of dataset generation`)}}catch(e){console.error("Failed to get questions:",e),this.addLog(r,`Failed to get questions: ${e.message||"Unknown error"}`)}}async generateSingleDataset({projectId:e,questionId:t,questionInfo:s,model:i,language:a}){try{let l=s;return l||(l=(await v.Z.get(`/api/projects/${e}/questions/${t}`)).data),(await v.Z.post(`/api/projects/${e}/datasets`,{projectId:e,questionId:t,model:i,language:a||"zh-CN"})).data}catch(e){throw console.error("Failed to generate dataset:",e),Error(`Failed to generate dataset: ${e.message}`)}}getTagDepth(e,t){let s=1,i=e;for(;i.parentId&&(s++,i=t.find(e=>e.id===i.parentId)););return s}getTagPath(e,t){let s=this.projectName||"",i=[],a=e;for(;a;)i.unshift(a.label),a=a.parentId?t.find(e=>e.id===a.parentId):null;return s&&i.length>0&&i[0]!==s&&i.unshift(s),i.join(" > ")}addLog(e,t){e&&"function"==typeof e&&e(t)}}let ed=new eo;function ec(){let{t:e,i18n:t}=(0,r.$G)(),{projectId:s}=(0,n.useParams)(),l=(0,o.useAtomValue)(d._),[b,C]=(0,a.useState)(null),[$,T]=(0,a.useState)(!1),[w,S]=(0,a.useState)(""),[q,P]=(0,a.useState)([]),[k,I]=(0,a.useState)(!1),[B,L]=(0,a.useState)(!1),[z,E]=(0,a.useState)(null),[M,F]=(0,a.useState)(""),[W,R]=(0,a.useState)(!1),[Q,N]=(0,a.useState)(!1),[G,O]=(0,a.useState)(!1),[A,H]=(0,a.useState)({tagsCount:0,questionsCount:0,datasetsCount:0}),[_,V]=(0,a.useState)({stage:"initializing",tagsTotal:0,tagsBuilt:0,questionsTotal:0,questionsBuilt:0,datasetsTotal:0,datasetsBuilt:0,logs:[]}),U=(0,a.useRef)(null),K=async()=>{try{T(!0);let e=await v.Z.get(`/api/projects/${s}/distill/tags/all`);P(e.data)}catch(t){console.error("获取标签列表失败:",t),S(e("common.fetchError"))}finally{T(!1)}},J=async()=>{try{let e=(await v.Z.get(`/api/projects/${s}/distill/tags/all`)).data.length,t=await v.Z.get(`/api/projects/${s}/questions/tree?isDistill=true`),i=t.data.length,a=t.data.filter(e=>e.answered).length;H({tagsCount:e,questionsCount:i,datasetsCount:a})}catch(e){console.error("获取蒸馏统计信息失败:",e)}},X=(t=null,s="")=>{if(!l||0===Object.keys(l).length){S(e("distill.selectModelFirst"));return}E(t),F(s),I(!0)},ee=async i=>{R(!1),N(!0),O(!0),V({stage:"initializing",tagsTotal:i.estimatedTags,tagsBuilt:A.tagsCount||0,questionsTotal:i.estimatedQuestions,questionsBuilt:A.questionsCount||0,datasetsTotal:i.estimatedQuestions,datasetsBuilt:A.datasetsCount||0,logs:[e("distill.autoDistillStarted",{time:new Date().toLocaleTimeString()})]});try{if(!l||0===Object.keys(l).length){ei(e("distill.selectModelFirst")),O(!1);return}await ed.executeDistillTask({projectId:s,topic:i.topic,levels:i.levels,tagsPerLevel:i.tagsPerLevel,questionsPerTag:i.questionsPerTag,model:l,language:t.language,concurrencyLimit:b?.taskConfig?.concurrencyLimit||5,onProgress:et,onLog:ei}),O(!1)}catch(e){console.error("自动蒸馏任务执行失败:",e),ei(`任务执行出错: ${e.message||"未知错误"}`),O(!1)}},et=e=>{V(t=>{let s={...t};return e.stage&&(s.stage=e.stage),e.tagsTotal&&(s.tagsTotal=e.tagsTotal),e.tagsBuilt&&("increment"===e.updateType?s.tagsBuilt+=e.tagsBuilt:s.tagsBuilt=e.tagsBuilt),e.questionsTotal&&(s.questionsTotal=e.questionsTotal),e.questionsBuilt&&("increment"===e.updateType?s.questionsBuilt+=e.questionsBuilt:s.questionsBuilt=e.questionsBuilt),e.datasetsTotal&&(s.datasetsTotal=e.datasetsTotal),e.datasetsBuilt&&("increment"===e.updateType?s.datasetsBuilt+=e.datasetsBuilt:s.datasetsBuilt=e.datasetsBuilt),s})},ei=e=>{V(t=>{let s=[...t.logs,e],i=s.length>200?s.slice(-200):s;return{...t,logs:i}})};return s?(0,i.jsxs)(c.Z,{maxWidth:"lg",sx:{mt:4,mb:8},children:[(0,i.jsxs)(p.Z,{elevation:0,sx:{p:3,borderRadius:2,border:"1px solid",borderColor:"divider"},children:[(0,i.jsxs)(h.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4,paddingLeft:"32px"},children:[(0,i.jsxs)(h.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(g.Z,{variant:"h5",component:"h1",fontWeight:"bold",children:e("distill.title")}),i.jsx(x.Z,{title:e("common.help"),children:i.jsx(m.Z,{size:"small",onClick:()=>{let e="en"===t.language?"https://docs.easy-dataset.com/ed/en/advanced/images-and-media":"https://docs.easy-dataset.com/jin-jie-shi-yong/images-and-media";window.open(e,"_blank")},sx:{color:"text.secondary"},children:i.jsx(D.Z,{fontSize:"small"})})})]}),(0,i.jsxs)(h.Z,{sx:{display:"flex",gap:2},children:[i.jsx(j.Z,{variant:"outlined",color:"primary",size:"large",onClick:()=>{if(!l||0===Object.keys(l).length){S(e("distill.selectModelFirst"));return}R(!0)},disabled:!l,startIcon:i.jsx(y.Z,{}),sx:{px:3,py:1},children:e("distill.autoDistillButton")}),i.jsx(j.Z,{variant:"contained",color:"primary",size:"large",onClick:()=>X(null),disabled:!l,startIcon:i.jsx(f.Z,{}),sx:{px:3,py:1},children:e("distill.generateRootTags")})]})]}),w&&i.jsx(u.Z,{severity:"error",sx:{mb:4,px:3,py:2},onClose:()=>S(""),children:w}),$?i.jsx(h.Z,{sx:{display:"flex",justifyContent:"center",p:6},children:i.jsx(Z.Z,{size:40})}):i.jsx(h.Z,{sx:{mt:2},children:i.jsx(Y,{ref:U,projectId:s,tags:q,onGenerateSubTags:X,onGenerateQuestions:(t,s)=>{if(!l||0===Object.keys(l).length){S(e("distill.selectModelFirst"));return}E(t),F(s),L(!0)}})})]}),k&&i.jsx(es,{open:k,onClose:()=>I(!1),onGenerated:()=>{K(),I(!1)},projectId:s,parentTag:z,tagPath:M,model:l}),B&&i.jsx(ea,{open:B,onClose:()=>L(!1),onGenerated:()=>{L(!1),K(),J(),U.current&&"function"==typeof U.current.fetchQuestionsStats&&U.current.fetchQuestionsStats()},projectId:s,tag:z,tagPath:M,model:l}),i.jsx(el,{open:W,onClose:()=>R(!1),onStart:ee,projectId:s,project:b,stats:A}),i.jsx(en,{open:Q,onClose:()=>{G?N(!1):(N(!1),K(),J(),U.current&&"function"==typeof U.current.fetchQuestionsStats&&U.current.fetchQuestionsStats())},progress:_})]}):i.jsx(c.Z,{maxWidth:"lg",sx:{mt:4},children:i.jsx(u.Z,{severity:"error",children:e("common.projectIdRequired")})})}},66760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var i=s(10326),a=s(18785),l=s(17577),r=s(71728),n=s(98139),o=s(30274),d=s(42265),c=s(35047),u=s(70012);function p({children:e,params:t}){let s=(0,c.useRouter)(),{projectId:p}=t,[h,g]=(0,l.useState)([]),[x,m]=(0,l.useState)(null),[j,Z]=(0,l.useState)([]),[f,y]=(0,l.useState)(!0),[b,v]=(0,l.useState)(null),[C]=(0,u.$G)();return f?(0,i.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh"},children:[i.jsx(n.Z,{}),i.jsx(o.Z,{sx:{mt:2},children:"加载项目数据..."})]}):b?(0,i.jsxs)(r.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh"},children:[(0,i.jsxs)(o.Z,{color:"error",children:[C("projects.fetchFailed"),": ",b]}),i.jsx(d.Z,{variant:"contained",onClick:()=>s.push("/"),sx:{mt:2},children:C("projects.backToHome")})]}):(0,i.jsxs)(i.Fragment,{children:[i.jsx(a.Z,{projects:h,currentProject:p}),i.jsx("main",{children:e})]})}},92495:(e,t,s)=>{"use strict";s.d(t,{l:()=>c});var i=s(17577),a=s(85999),l=s(23297),r=s(44099),n=s(36493),o=s(15431),d=s(70012);function c(){let e=(0,n.useAtomValue)(o._),{t}=(0,d.$G)();return{generateSingleDataset:(0,i.useCallback)(async({projectId:s,questionId:i,questionInfo:n})=>{if(!e)return a.A.error(t("models.configNotFound")),null;let o="zh-CN"===l.Z.language?"中文":"en";a.A.promise(r.Z.post(`/api/projects/${s}/datasets`,{questionId:i,model:e,language:o}),{loading:t("datasets.generating"),description:`问题：【${n}】`,position:"top-right",success:e=>"生成数据集成功",error:e=>t("datasets.generateFailed",{error:e.response?.data?.error})})},[e,t]),generateMultipleDataset:(0,i.useCallback)(async(t,s)=>{let i=0,n=s.length,o=a.A.loading(`正在处理请求 (${i}/${n})...`,{position:"top-right"}),d=async s=>{try{let d=(await r.Z.post(`/api/projects/${t}/datasets`,{questionId:s.id,model:e,language:"zh-CN"===l.Z.language?"中文":"en"})).data;return i++,a.A.success(`${s.question} 完成`,{position:"top-right"}),a.A.loading(`正在处理请求 (${i}/${n})...`,{id:o}),d}catch(e){throw i++,a.A.error(`${s.question} 失败`,{description:e.message,position:"top-right"}),a.A.loading(`正在处理请求 (${i}/${n})...`,{id:o}),e}};try{let e=await Promise.allSettled(s.map(e=>d(e)));return a.A.success(`全部请求处理完成 (成功: ${e.filter(e=>"fulfilled"===e.status).length}/${n})`,{id:o,position:"top-right"}),e}catch{}},[e,t])}}},84873:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"}),"Add")},27387:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},42167:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},24437:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"m12 8-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14z"}),"ExpandLess")},6446:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"}),"ExpandMore")},33182:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8z"}),"Folder")},56347:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M11 18h2v-2h-2zm1-16C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m0-14c-2.21 0-4 1.79-4 4h2c0-1.1.9-2 2-2s2 .9 2 2c0 2-3 1.75-3 5h2c0-2.25 3-2.5 3-5 0-2.21-1.79-4-4-4"}),"HelpOutline")},76165:(e,t,s)=>{"use strict";var i=s(39618);t.Z=void 0;var a=i(s(71133)),l=s(10326);t.Z=(0,a.default)((0,l.jsx)("path",{d:"M11.07 12.85c.77-1.39 2.25-2.21 3.11-3.44.91-1.29.4-3.7-2.18-3.7-1.69 0-2.52 1.28-2.87 2.34L6.54 6.96C7.25 4.83 9.18 3 11.99 3c2.35 0 3.96 1.07 4.78 2.41.7 1.15 1.11 3.3.03 4.9-1.2 1.77-2.35 2.31-2.97 3.45-.25.46-.35.76-.35 2.24h-2.89c-.01-.78-.13-2.05.48-3.15M14 20c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2"}),"QuestionMark")},5041:(e,t,s)=>{"use strict";s.d(t,{Z:()=>w});var i=s(91367),a=s(45353),l=s(17577),r=s(41135),n=s(15763),o=s(85810),d=s(88634),c=s(91703),u=s(2791),p=s(88820),h=s(78029),g=s(23743),x=s(37382),m=s(71685),j=s(97898);function Z(e){return(0,j.ZP)("MuiCollapse",e)}(0,m.Z)("MuiCollapse",["root","horizontal","vertical","entered","hidden","wrapper","wrapperInner"]);var f=s(10326);let y=["addEndListener","children","className","collapsedSize","component","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","orientation","style","timeout","TransitionComponent"],b=e=>{let{orientation:t,classes:s}=e,i={root:["root",`${t}`],entered:["entered"],hidden:["hidden"],wrapper:["wrapper",`${t}`],wrapperInner:["wrapperInner",`${t}`]};return(0,d.Z)(i,Z,s)},v=(0,c.ZP)("div",{name:"MuiCollapse",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,t[s.orientation],"entered"===s.state&&t.entered,"exited"===s.state&&!s.in&&"0px"===s.collapsedSize&&t.hidden]}})(({theme:e,ownerState:t})=>(0,a.Z)({height:0,overflow:"hidden",transition:e.transitions.create("height")},"horizontal"===t.orientation&&{height:"auto",width:0,transition:e.transitions.create("width")},"entered"===t.state&&(0,a.Z)({height:"auto",overflow:"visible"},"horizontal"===t.orientation&&{width:"auto"}),"exited"===t.state&&!t.in&&"0px"===t.collapsedSize&&{visibility:"hidden"})),C=(0,c.ZP)("div",{name:"MuiCollapse",slot:"Wrapper",overridesResolver:(e,t)=>t.wrapper})(({ownerState:e})=>(0,a.Z)({display:"flex",width:"100%"},"horizontal"===e.orientation&&{width:"auto",height:"100%"})),$=(0,c.ZP)("div",{name:"MuiCollapse",slot:"WrapperInner",overridesResolver:(e,t)=>t.wrapperInner})(({ownerState:e})=>(0,a.Z)({width:"100%"},"horizontal"===e.orientation&&{width:"auto",height:"100%"})),T=l.forwardRef(function(e,t){let s=(0,u.i)({props:e,name:"MuiCollapse"}),{addEndListener:d,children:c,className:m,collapsedSize:j="0px",component:Z,easing:T,in:w,onEnter:S,onEntered:q,onEntering:P,onExit:k,onExited:I,onExiting:B,orientation:L="vertical",style:z,timeout:D=p.x9.standard,TransitionComponent:E=n.ZP}=s,M=(0,i.Z)(s,y),F=(0,a.Z)({},s,{orientation:L,collapsedSize:j}),W=b(F),R=(0,g.Z)(),Q=(0,o.Z)(),N=l.useRef(null),G=l.useRef(),O="number"==typeof j?`${j}px`:j,A="horizontal"===L,H=A?"width":"height",_=l.useRef(null),V=(0,x.Z)(t,_),U=e=>t=>{if(e){let s=_.current;void 0===t?e(s):e(s,t)}},K=()=>N.current?N.current[A?"clientWidth":"clientHeight"]:0,J=U((e,t)=>{N.current&&A&&(N.current.style.position="absolute"),e.style[H]=O,S&&S(e,t)}),X=U((e,t)=>{let s=K();N.current&&A&&(N.current.style.position="");let{duration:i,easing:a}=(0,h.C)({style:z,timeout:D,easing:T},{mode:"enter"});if("auto"===D){let t=R.transitions.getAutoHeightDuration(s);e.style.transitionDuration=`${t}ms`,G.current=t}else e.style.transitionDuration="string"==typeof i?i:`${i}ms`;e.style[H]=`${s}px`,e.style.transitionTimingFunction=a,P&&P(e,t)}),Y=U((e,t)=>{e.style[H]="auto",q&&q(e,t)}),ee=U(e=>{e.style[H]=`${K()}px`,k&&k(e)}),et=U(I),es=U(e=>{let t=K(),{duration:s,easing:i}=(0,h.C)({style:z,timeout:D,easing:T},{mode:"exit"});if("auto"===D){let s=R.transitions.getAutoHeightDuration(t);e.style.transitionDuration=`${s}ms`,G.current=s}else e.style.transitionDuration="string"==typeof s?s:`${s}ms`;e.style[H]=O,e.style.transitionTimingFunction=i,B&&B(e)});return(0,f.jsx)(E,(0,a.Z)({in:w,onEnter:J,onEntered:Y,onEntering:X,onExit:ee,onExited:et,onExiting:es,addEndListener:e=>{"auto"===D&&Q.start(G.current||0,e),d&&d(_.current,e)},nodeRef:_,timeout:"auto"===D?null:D},M,{children:(e,t)=>(0,f.jsx)(v,(0,a.Z)({as:Z,className:(0,r.Z)(W.root,m,{entered:W.entered,exited:!w&&"0px"===O&&W.hidden}[e]),style:(0,a.Z)({[A?"minWidth":"minHeight"]:O},z),ref:V},t,{ownerState:(0,a.Z)({},F,{state:e}),children:(0,f.jsx)(C,{ownerState:(0,a.Z)({},F,{state:e}),className:W.wrapper,ref:N,children:(0,f.jsx)($,{ownerState:(0,a.Z)({},F,{state:e}),className:W.wrapperInner,children:c})})}))}))});T.muiSupportAuto=!0;let w=T},24003:(e,t,s)=>{"use strict";s.d(t,{Z:()=>b});var i=s(91367),a=s(45353),l=s(17577),r=s(41135),n=s(88634),o=s(11190),d=s(91703),c=s(27080),u=s(2791),p=s(81378),h=s(69408),g=s(37382),x=s(92992),m=s(1217),j=s(10326);let Z=["alignItems","autoFocus","component","children","dense","disableGutters","divider","focusVisibleClassName","selected","className"],f=e=>{let{alignItems:t,classes:s,dense:i,disabled:l,disableGutters:r,divider:o,selected:d}=e,c=(0,n.Z)({root:["root",i&&"dense",!r&&"gutters",o&&"divider",l&&"disabled","flex-start"===t&&"alignItemsFlexStart",d&&"selected"]},m.t,s);return(0,a.Z)({},s,c)},y=(0,d.ZP)(p.Z,{shouldForwardProp:e=>(0,c.Z)(e)||"classes"===e,name:"MuiListItemButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:s}=e;return[t.root,s.dense&&t.dense,"flex-start"===s.alignItems&&t.alignItemsFlexStart,s.divider&&t.divider,!s.disableGutters&&t.gutters]}})(({theme:e,ownerState:t})=>(0,a.Z)({display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${m.Z.selected}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,o.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity),[`&.${m.Z.focusVisible}`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.focusOpacity}))`:(0,o.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.focusOpacity)}},[`&.${m.Z.selected}:hover`]:{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:(0,o.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${e.vars.palette.action.selectedOpacity})`:(0,o.Fq)(e.palette.primary.main,e.palette.action.selectedOpacity)}},[`&.${m.Z.focusVisible}`]:{backgroundColor:(e.vars||e).palette.action.focus},[`&.${m.Z.disabled}`]:{opacity:(e.vars||e).palette.action.disabledOpacity}},t.divider&&{borderBottom:`1px solid ${(e.vars||e).palette.divider}`,backgroundClip:"padding-box"},"flex-start"===t.alignItems&&{alignItems:"flex-start"},!t.disableGutters&&{paddingLeft:16,paddingRight:16},t.dense&&{paddingTop:4,paddingBottom:4})),b=l.forwardRef(function(e,t){let s=(0,u.i)({props:e,name:"MuiListItemButton"}),{alignItems:n="center",autoFocus:o=!1,component:d="div",children:c,dense:p=!1,disableGutters:m=!1,divider:b=!1,focusVisibleClassName:v,selected:C=!1,className:$}=s,T=(0,i.Z)(s,Z),w=l.useContext(x.Z),S=l.useMemo(()=>({dense:p||w.dense||!1,alignItems:n,disableGutters:m}),[n,w.dense,p,m]),q=l.useRef(null);(0,h.Z)(()=>{o&&q.current&&q.current.focus()},[o]);let P=(0,a.Z)({},s,{alignItems:n,dense:S.dense,disableGutters:m,divider:b,selected:C}),k=f(P),I=(0,g.Z)(q,t);return(0,j.jsx)(x.Z.Provider,{value:S,children:(0,j.jsx)(y,(0,a.Z)({ref:I,href:T.href||T.to,component:(T.href||T.to)&&"div"===d?"button":d,focusVisibleClassName:(0,r.Z)(k.focusVisible,v),ownerState:P,className:(0,r.Z)(k.root,$)},T,{classes:k,children:c}))})})},24705:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(68570).createProxy)(String.raw`D:\office\niuma-dataset\easy-dataset\app\projects\[projectId]\distill\page.js#default`)},58138:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(68570).createProxy)(String.raw`D:\office\niuma-dataset\easy-dataset\app\projects\[projectId]\layout.js#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[8948,622,2797,2249,4350,8347,1411,9787,9474,8785],()=>s(78074));module.exports=i})();