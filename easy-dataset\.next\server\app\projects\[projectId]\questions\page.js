(()=>{var e={};e.id=6678,e.ids=[6678],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},15560:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(23897),s(58138),s(57213),s(35866);var n=s(23191),r=s(88716),a=s(37922),i=s.n(a),o=s(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["projects",{children:["[projectId]",{children:["questions",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23897)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\questions\\page.js"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58138)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\layout.js"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,57213)),"D:\\office\\niuma-dataset\\easy-dataset\\app\\layout.js"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\office\\niuma-dataset\\easy-dataset\\app\\projects\\[projectId]\\questions\\page.js"],u="/projects/[projectId]/questions/page",h={require:s,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/projects/[projectId]/questions/page",pathname:"/projects/[projectId]/questions",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},98508:(e,t,s)=>{Promise.resolve().then(s.bind(s,66760))},43234:(e,t,s)=>{Promise.resolve().then(s.bind(s,30781))},66760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var n=s(10326),r=s(18785),a=s(17577),i=s(71728),o=s(98139),l=s(30274),c=s(42265),d=s(35047),u=s(70012);function h({children:e,params:t}){let s=(0,d.useRouter)(),{projectId:h}=t,[x,p]=(0,a.useState)([]),[m,g]=(0,a.useState)(null),[j,f]=(0,a.useState)([]),[Z,b]=(0,a.useState)(!0),[y,v]=(0,a.useState)(null),[k]=(0,u.$G)();return Z?(0,n.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh"},children:[n.jsx(o.Z,{}),n.jsx(l.Z,{sx:{mt:2},children:"加载项目数据..."})]}):y?(0,n.jsxs)(i.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh"},children:[(0,n.jsxs)(l.Z,{color:"error",children:[k("projects.fetchFailed"),": ",y]}),n.jsx(c.Z,{variant:"contained",onClick:()=>s.push("/"),sx:{mt:2},children:k("projects.backToHome")})]}):(0,n.jsxs)(n.Fragment,{children:[n.jsx(r.Z,{projects:x,currentProject:h}),n.jsx("main",{children:e})]})}},30781:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eo});var n=s(10326),r=s(17577),a=s(70012),i=s(23743),o=s(75616),l=s(71728),c=s(98139),d=s(60893),u=s(30274),h=s(3735),x=s(42265),p=s(83708),m=s(1450),g=s(45112),j=s(17082),f=s(76971),Z=s(14350),b=s(57329),y=s(56390),v=s(37841),k=s(99207),q=s(69995),S=s(98117),C=s(28591),w=s(37104),z=s(10163),I=s(52386),$=s(26644),P=s(23297),A=s(5420),M=s(84873),D=s(85560),E=s(48260),T=s(4523),W=s(42167),G=s(92495);function O({questions:e=[],currentPage:t,totalQuestions:s=0,handlePageChange:i,selectedQuestions:o=[],onSelectQuestion:h,onDeleteQuestion:x,projectId:m,onEditQuestion:g,refreshQuestions:j}){let{t:b}=(0,a.$G)(),[y,v]=(0,r.useState)({}),{generateSingleDataset:q}=(0,G.l)(),S=e=>{let t=e.split("\n")[0].trim();return t.startsWith("# ")?t.substring(2):t.length>0?t.length>200?t.substring(0,200)+"...":t:b("chunks.defaultTitle")},C=e=>o.includes(e),w=async(e,t)=>{v(t=>({...t,[e]:!0})),await q({projectId:m,questionId:e,questionInfo:t}),v(t=>({...t,[e]:!1})),j()};return(0,n.jsxs)(l.Z,{style:{padding:"20px"},children:[(0,n.jsxs)(d.Z,{elevation:0,sx:{borderRadius:2,overflow:"hidden",boxShadow:"0 2px 4px rgba(0,0,0,0.05)"},children:[(0,n.jsxs)(l.Z,{sx:{px:2,py:1,display:"flex",alignItems:"center",bgcolor:"background.paper"},children:[n.jsx(u.Z,{variant:"body2",sx:{fontWeight:500,ml:1},children:b("datasets.question")}),(0,n.jsxs)(l.Z,{sx:{ml:"auto",display:"flex",alignItems:"center"},children:[n.jsx(u.Z,{variant:"body2",sx:{fontWeight:500,mr:2,display:{xs:"none",sm:"block"}},children:b("common.label")}),n.jsx(u.Z,{variant:"body2",sx:{fontWeight:500,width:150,mr:2,display:{xs:"none",md:"block"}},children:b("chunks.title")}),n.jsx(u.Z,{variant:"body2",sx:{fontWeight:500,width:100,textAlign:"center"},children:b("common.actions")})]})]}),n.jsx(k.Z,{}),e.map((t,s)=>{let r=C(t.id),a=t.id;return(0,n.jsxs)(l.Z,{children:[(0,n.jsxs)(l.Z,{sx:{px:2,py:1.5,display:"flex",alignItems:"center",bgcolor:r?"action.selected":"background.paper","&:hover":{bgcolor:"action.hover"}},children:[n.jsx(f.Z,{checked:r,onChange:()=>{h(a)},size:"small"}),(0,n.jsxs)(l.Z,{sx:{ml:1,flex:1,mr:2},children:[(0,n.jsxs)(u.Z,{variant:"body2",children:[t.question,t.datasetCount>0?n.jsx(D.Z,{label:b("datasets.answerCount",{count:t.datasetCount}),size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.75rem",maxWidth:150}}):null]}),(0,n.jsxs)(u.Z,{variant:"caption",color:"text.secondary",sx:{display:{xs:"block",sm:"none"}},children:[t.label||b("datasets.noTag")," • ID: ",(t.question||"").substring(0,8)]})]}),n.jsx(l.Z,{sx:{display:{xs:"none",sm:"block"},mr:2},children:t.label?n.jsx(D.Z,{label:t.label,size:"small",color:"primary",variant:"outlined",sx:{fontSize:"0.75rem",maxWidth:150}}):n.jsx(u.Z,{variant:"caption",color:"text.disabled",children:b("datasets.noTag")})}),n.jsx(l.Z,{sx:{width:150,mr:2,display:{xs:"none",md:"block"}},children:n.jsx(p.Z,{title:S(t.chunk.content),children:n.jsx(D.Z,{label:t.chunk.name,size:"small",variant:"outlined",color:"info",sx:{fontSize:"0.75rem",maxWidth:"100%",textOverflow:"ellipsis"}})})}),(0,n.jsxs)(l.Z,{sx:{width:120,display:"flex",justifyContent:"center"},children:[n.jsx(p.Z,{title:b("common.edit"),children:n.jsx(E.Z,{size:"small",color:"primary",onClick:()=>g({id:t.id,question:t.question,chunkId:t.chunkId,label:t.label||"other"}),disabled:y[a],children:n.jsx(W.Z,{fontSize:"small"})})}),n.jsx(p.Z,{title:b("datasets.generateDataset"),children:n.jsx(E.Z,{size:"small",color:"primary",onClick:()=>w(t.id,t.question),disabled:y[a],children:y[a]?n.jsx(c.Z,{size:16}):n.jsx(I.Z,{fontSize:"small"})})}),n.jsx(p.Z,{title:b("common.delete"),children:n.jsx(E.Z,{size:"small",color:"error",onClick:()=>x(t.id),disabled:y[a],children:n.jsx($.Z,{fontSize:"small"})})})]})]}),s<e.length-1&&n.jsx(k.Z,{})]},a)})]}),s>1&&(0,n.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",mt:3,mb:2},children:[n.jsx(T.Z,{count:s,page:t,onChange:i,color:"primary",showFirstButton:!0,showLastButton:!0,shape:"rounded",size:"medium"}),(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,n.jsxs)(u.Z,{variant:"body2",children:[b("common.jumpTo"),":"]}),n.jsx(Z.Z,{size:"small",type:"number",inputProps:{min:1,max:s,style:{padding:"4px 8px",width:"50px"}},onKeyPress:e=>{if("Enter"===e.key){let t=parseInt(e.target.value,10);t>=1&&t<=s&&(i(null,t),e.target.value="")}}})]})]})]})}var R=s(5041),F=s(84979),Q=s(71411),_=s(25886),L=s(6446),N=s(24437),V=s(33182),B=s(76165),U=s(44099);function H({tags:e=[],selectedQuestions:t=[],onSelectQuestion:s,onDeleteQuestion:i,onEditQuestion:o,projectId:c,searchTerm:h}){let{t:x}=(0,a.$G)(),[p,m]=(0,r.useState)({}),[g,j]=(0,r.useState)({}),[f,Z]=(0,r.useState)({}),{generateSingleDataset:b}=(0,G.l)(),[y,v]=(0,r.useState)([]),[k,q]=(0,r.useState)({}),S=(0,r.useCallback)(e=>{let t=!p[e];t&&!k[e]&&C(e),m(s=>({...s,[e]:t}))},[p,k,c]),C=(0,r.useCallback)(async e=>{try{let t=await U.Z.get(`/api/projects/${c}/questions/tree?tag=${encodeURIComponent(e)}${h?`&input=${h}`:""}`);v(e=>{let s=[...e];return t.data.forEach(e=>{let t=s.findIndex(t=>t.id===e.id);-1===t?s.push(e):s[t]=e}),s}),q(t=>({...t,[e]:!0}))}catch(t){console.error(`获取标签 "${e}" 的问题失败:`,t)}},[c,h,p]),w=(0,r.useCallback)(e=>t.includes(e),[t]),z=async(e,t)=>{Z(t=>({...t,[e]:!0})),await b({projectId:c,questionId:e,questionInfo:t}),Z(t=>({...t,[e]:!1}))},I=(0,r.useCallback)((e,t,r)=>{let a=e.id;return n.jsx(J,{question:e,index:t,total:r,isSelected:w(a),onSelect:s,onDelete:i,onGenerate:z,onEdit:o,isProcessing:f[a],t:x},a)},[w,s,i,z,f,x]),$=(0,r.useMemo)(()=>{let t={},s=e=>{let n=(g[e.label]||[]).length;if(e.child&&e.child.length>0)for(let t of e.child)n+=s(t);return t[e.label]=n,n};return e.forEach(s),t},[g,e]),P=(0,r.useCallback)((e,t=0)=>{let s=g[e.label]||[],r=s.length>0,a=e.child&&e.child.length>0,i=p[e.label],o=$[e.label]||0;return(0,n.jsxs)(l.Z,{children:[n.jsx(K,{tag:e,level:t,isExpanded:i,totalQuestions:o,onToggle:S,t:x}),i&&(0,n.jsxs)(R.Z,{in:!0,children:[a&&n.jsx(F.Z,{disablePadding:!0,children:e.child.map(e=>P(e,t+1))}),r&&n.jsx(F.Z,{disablePadding:!0,sx:{mt:a?1:0},children:s.map((e,t)=>I(e,t,s.length))})]})]},e.label)},[g,p,$,S,I,x]);return 0===e.length&&0===Object.keys(g).length?n.jsx(l.Z,{sx:{p:4,textAlign:"center"},children:n.jsx(u.Z,{variant:"body1",color:"text.secondary",children:x("datasets.noTagsAndQuestions")})}):n.jsx(d.Z,{elevation:0,sx:{border:"1px solid",borderColor:"divider",borderRadius:1,overflow:"auto",p:2,maxHeight:"75vh"},children:(0,n.jsxs)(F.Z,{disablePadding:!0,children:[(()=>{let e=g.uncategorized||[];return 0===e.length?null:(0,n.jsxs)(l.Z,{children:[(0,n.jsxs)(Q.ZP,{button:!0,onClick:()=>S("uncategorized"),sx:{py:1,bgcolor:"primary.light",color:"primary.contrastText","&:hover":{bgcolor:"primary.main"},borderRadius:"4px",mb:.5,pr:1},children:[n.jsx(V.Z,{fontSize:"small",sx:{mr:1,color:"inherit"}}),n.jsx(_.Z,{primary:(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center"},children:[n.jsx(u.Z,{variant:"body1",sx:{fontWeight:600,fontSize:"1rem"},children:x("datasets.uncategorized")}),n.jsx(D.Z,{label:x("datasets.questionCount",{count:e.length}),size:"small",sx:{ml:1,height:20,fontSize:"0.7rem",color:"#fff",backgroundColor:"#333"}})]})}),n.jsx(E.Z,{size:"small",edge:"end",sx:{color:"inherit"},children:p.uncategorized?n.jsx(N.Z,{}):n.jsx(L.Z,{})})]}),n.jsx(R.Z,{in:p.uncategorized,children:n.jsx(F.Z,{disablePadding:!0,children:e.map((t,s)=>I(t,s,e.length))})})]})})(),e.map(e=>P(e))]})})}let J=(0,r.memo)(({question:e,index:t,total:s,isSelected:r,onSelect:a,onDelete:i,onGenerate:o,onEdit:d,isProcessing:h,t:x})=>{let m=e.id;return(0,n.jsxs)(l.Z,{children:[(0,n.jsxs)(Q.ZP,{sx:{pl:4,py:1,borderRadius:"4px",ml:2,mr:1,mb:.5,bgcolor:r?"action.selected":"transparent","&:hover":{bgcolor:"action.hover"}},children:[n.jsx(f.Z,{checked:r,onChange:()=>a(m),size:"small"}),n.jsx(B.Z,{fontSize:"small",sx:{mr:1,color:"primary.main"}}),n.jsx(_.Z,{primary:(0,n.jsxs)(u.Z,{variant:"body2",sx:{fontWeight:400},children:[e.question,e.dataSites&&e.dataSites.length>0&&n.jsx(D.Z,{label:x("datasets.answerCount",{count:e.dataSites.length}),size:"small",color:"primary",variant:"outlined",sx:{ml:1,fontSize:"0.75rem",maxWidth:150}})]}),secondary:(0,n.jsxs)(u.Z,{variant:"caption",color:"text.secondary",sx:{mt:.5,display:"block"},children:[x("datasets.source"),": ",e.chunk?.name||e.chunkId||x("common.unknown")]})}),(0,n.jsxs)(l.Z,{children:[n.jsx(p.Z,{title:x("common.edit"),children:n.jsx(E.Z,{size:"small",sx:{mr:1},onClick:()=>d({question:e.question,chunkId:e.chunkId,label:e.label||"other"}),disabled:h,children:n.jsx(W.Z,{fontSize:"small"})})}),n.jsx(p.Z,{title:x("datasets.generateDataset"),children:n.jsx(E.Z,{size:"small",sx:{mr:1},onClick:()=>o(e.id,e.question),disabled:h,children:h?n.jsx(c.Z,{size:16}):n.jsx(I.Z,{fontSize:"small"})})}),n.jsx(p.Z,{title:x("common.delete"),children:n.jsx(E.Z,{size:"small",onClick:()=>i(e.question,e.chunkId),children:n.jsx($.Z,{fontSize:"small"})})})]})]}),t<s-1&&n.jsx(k.Z,{component:"li",variant:"inset",sx:{ml:6}})]},e.id)}),K=(0,r.memo)(({tag:e,level:t,isExpanded:s,totalQuestions:r,onToggle:a,t:i})=>(0,n.jsxs)(Q.ZP,{button:!0,onClick:()=>a(e.label),sx:{pl:2*t+1,py:1,bgcolor:0===t?"primary.light":"background.paper",color:0===t?"primary.contrastText":"inherit","&:hover":{bgcolor:0===t?"primary.main":"action.hover"},borderRadius:"4px",mb:.5,pr:1},children:[n.jsx(V.Z,{fontSize:"small",sx:{mr:1,color:0===t?"inherit":"primary.main"}}),n.jsx(_.Z,{primary:(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center"},children:[n.jsx(u.Z,{variant:"body1",sx:{fontWeight:0===t?600:400,fontSize:0===t?"1rem":"0.9rem"},children:e.label}),r>0&&n.jsx(D.Z,{label:i("datasets.questionCount",{count:r}),size:"small",color:0===t?"default":"primary",variant:0===t?"default":"outlined",sx:{ml:1,height:20,fontSize:"0.7rem",color:"#fff",backgroundColor:"#333"}})]})}),n.jsx(E.Z,{size:"small",edge:"end",sx:{color:0===t?"inherit":"action.active"},children:s?n.jsx(N.Z,{}):n.jsx(L.Z,{})})]}));var X=s(879),Y=s(46790),ee=s(19787);function et({open:e,onClose:t,onSubmit:s,initialData:i,projectId:o,tags:c,mode:d="create"}){let[u,h]=(0,r.useState)([]),{t:p}=(0,a.$G)(),m=e=>{let t=u.find(t=>t.id===e);return t?.name||e},[g,j]=(0,r.useState)({id:"",question:"",chunkId:"",label:""}),f=(e,t="")=>{let s=[],n=e=>{s.push({id:e.label,label:e.label,originalLabel:e.label}),e.child&&e.child.length>0&&e.child.forEach(e=>n(e))};return e.forEach(e=>n(e)),s.push({id:"other",label:p("datasets.uncategorized"),originalLabel:"other"}),s},b=(0,r.useMemo)(()=>f(c),[c,p]);return ee.Z,b.find(e=>e.id===g.label),b.find(e=>"other"===e.id),(0,n.jsxs)(q.Z,{open:e,onClose:t,maxWidth:"sm",fullWidth:!0,children:[n.jsx(S.Z,{children:"create"===d?p("questions.createQuestion"):p("questions.editQuestion")}),n.jsx(C.Z,{children:(0,n.jsxs)(l.Z,{sx:{display:"flex",flexDirection:"column",gap:2,mt:2},children:[n.jsx(Z.Z,{label:p("questions.questionContent"),multiline:!0,rows:4,fullWidth:!0,value:g.question,onChange:e=>j({...g,question:e.target.value})}),n.jsx(ee.Z,{fullWidth:!0,options:u,getOptionLabel:e=>m(e.id),value:u.find(e=>e.id===g.chunkId)||null,onChange:(e,t)=>j({...g,chunkId:t?t.id:""}),renderInput:e=>n.jsx(Z.Z,{...e,label:p("questions.selectChunk"),placeholder:p("questions.searchChunk")})}),n.jsx(ee.Z,{fullWidth:!0,options:b,getOptionLabel:e=>e.label,value:b.find(e=>e.id===g.label)||null,onChange:(e,t)=>j({...g,label:t?t.id:""}),renderInput:e=>n.jsx(Z.Z,{...e,label:p("questions.selectTag"),placeholder:p("questions.searchTag")})})]})}),(0,n.jsxs)(z.Z,{children:[n.jsx(x.Z,{onClick:t,children:p("common.cancel")}),n.jsx(x.Z,{onClick:()=>{s(g),t()},variant:"contained",disabled:!g.question||!g.chunkId,children:"create"===d?p("common.create"):p("common.save")})]})]})}var es=s(97533),en=s(85999),er=s(67595),ea=s(36493),ei=s(15431);function eo({params:e}){let{t}=(0,a.$G)(),s=(0,i.Z)(),{projectId:D}=e,[E,T]=(0,r.useState)(!0),[W,R]=(0,r.useState)({}),[F,Q]=(0,r.useState)(1),[_,L]=(0,r.useState)(10),[N,V]=(0,r.useState)("all"),[B,J]=(0,r.useState)("");(0,er.N)(B);let[K,ee]=(0,r.useState)([]),eo=(0,ea.useAtomValue)(ei._),{generateMultipleDataset:el}=(0,G.l)(),[ec,ed]=(0,r.useState)(0),[eu,eh]=(0,r.useState)([]),ex=async()=>{try{let e=await U.Z.get(`/api/projects/${D}/questions?page=${F}&size=10&status=${N}&input=${B}`);if(200!==e.status)throw Error(t("common.fetchError"));R(e.data||{});let s=await U.Z.get(`/api/projects/${D}/tags`);if(200!==s.status)throw Error(t("common.fetchError"));ee(s.data.tags||[]),T(!1)}catch(e){console.error(t("common.fetchError"),e),en.A.error(e.message)}},[ep,em]=(0,r.useState)(!1),{taskSettings:eg}=(0,Y.Z)(D),[ej,ef]=(0,r.useState)({total:0,completed:0,percentage:0,datasetCount:0}),{editDialogOpen:eZ,editMode:eb,editingQuestion:ey,handleOpenCreateDialog:ev,handleOpenEditDialog:ek,handleCloseDialog:eq,handleSubmitQuestion:eS}=function(e,t){let{t:s}=(0,a.$G)(),[n,i]=(0,r.useState)(!1),[o,l]=(0,r.useState)("create"),[c,d]=(0,r.useState)(null),u=()=>{i(!1),d(null)},h=async n=>{try{let r=await (0,es.ZP)(`/api/projects/${e}/questions`,{method:"create"===o?"POST":"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify("create"===o?{question:n.question,chunkId:n.chunkId,label:n.label}:{id:n.id,question:n.question,chunkId:n.chunkId,label:n.label})});if(!r.ok){let e=await r.json();throw Error(e.error||s("questions.operationFailed"))}let a=await r.json();t&&t(a),u()}catch(e){console.error("操作失败:",e)}};return{editDialogOpen:n,editMode:o,editingQuestion:c,handleOpenCreateDialog:()=>{l("create"),d(null),i(!0)},handleOpenEditDialog:e=>{l("edit"),d(e),i(!0)},handleCloseDialog:u,handleSubmitQuestion:h}}(D,e=>{ex(),en.A.success(t("questions.operationSuccess"))}),[eC,ew]=(0,r.useState)({open:!1,title:"",content:"",confirmAction:null}),ez=(e,t)=>{t?eh(t):eh(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},eI=async()=>{eu.length>0?eh([]):eh((await U.Z.get(`/api/projects/${D}/questions?status=${N}&input=${B}&selectedAll=1`)).data.map(e=>e.id))},e$=async(e,t,s)=>{let n=[],r=new Set,a=[...e];for(;a.length>0||r.size>0;){for(;r.size<s&&a.length>0;){let e=t(a.shift()).then(t=>(r.delete(e),t));r.add(e),n.push(e)}r.size>0&&await Promise.race(r)}return Promise.all(n)},eP=async()=>{if(0===eu.length){en.A.warning(t("questions.noQuestionsSelected"));return}if(!eo){en.A.warning(t("models.configNotFound"));return}try{ef({total:eu.length,completed:0,percentage:0,datasetCount:0}),em(!0),en.A.info(t("questions.batchGenerateStart",{count:eu.length}));let e=async e=>{try{console.log("开始生成数据集:",{questionId:e});let s="zh-CN"===P.Z.language?"中文":"en",n=await (0,es.ZP)(`/api/projects/${D}/datasets`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({questionId:e,model:eo,language:s})});if(!n.ok){let s=await n.json();return console.error(t("datasets.generateError"),s.error||t("datasets.generateFailed")),ef(e=>{let t=e.completed+1,s=Math.round(t/e.total*100);return{...e,completed:t,percentage:s}}),{success:!1,questionId:e,error:s.error||t("datasets.generateFailed")}}let r=await n.json();return ef(e=>{let t=e.completed+1,s=Math.round(t/e.total*100),n=e.datasetCount+1;return{...e,completed:t,percentage:s,datasetCount:n}}),console.log(`数据集生成成功: ${e}`),{success:!0,questionId:e,data:r.dataset}}catch(t){return console.error("生成数据集失败:",t),ef(e=>{let t=e.completed+1,s=Math.round(t/e.total*100);return{...e,completed:t,percentage:s}}),{success:!1,questionId:e,error:t.message}}},s=await e$(eu,e,eg.concurrencyLimit);ex();let n=s.filter(e=>e.success).length,r=s.filter(e=>!e.success).length;r>0?en.A.warning(t("datasets.partialSuccess",{successCount:n,total:eu.length,failCount:r})):en.A.success(t("common.success",{successCount:n}))}catch(e){console.error("生成数据集出错:",e),en.A.error(e.message||"生成数据集失败")}finally{setTimeout(()=>{em(!1),setTimeout(()=>{ef({total:0,completed:0,percentage:0,datasetCount:0})},500)},2e3)}},eA=e=>{ew({open:!0,title:t("common.confirmDelete"),content:t("common.confirmDeleteQuestion"),confirmAction:()=>eM(e)})},eM=async e=>{en.A.promise(U.Z.delete(`/api/projects/${D}/questions/${e}`),{loading:"数据删除中",success:s=>(eh(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e]),ex(),t("common.deleteSuccess")),error:e=>e.response?.data?.message||"删除失败"})},eD=e=>{eA(e)},eE=async()=>{try{if(!eo){en.A.error(t("questions.selectModelFirst",{defaultValue:"请先选择模型"}));return}let e=await U.Z.post(`/api/projects/${D}/tasks`,{taskType:"answer-generation",modelInfo:eo,language:P.Z.language});e.data?.code===0?en.A.success(t("tasks.createSuccess",{defaultValue:"后台任务已创建，系统将自动处理未生成答案的问题"})):en.A.error(t("tasks.createFailed",{defaultValue:"创建后台任务失败"}))}catch(e){console.error("创建任务失败:",e),en.A.error(t("tasks.createFailed",{defaultValue:"创建任务失败"})+": "+e.message)}},eT=()=>{if(0===eu.length){en.A.warning("请先选择问题");return}ew({open:!0,title:"确认批量删除问题",content:`您确定要删除选中的 ${eu.length} 个问题吗？此操作不可恢复。`,confirmAction:eW})},eW=async()=>{en.A.promise(U.Z.delete(`/api/projects/${D}/questions/batch-delete`,{data:{questionIds:eu}}),{loading:`正在删除 ${eu.length} 个问题...`,success:e=>(ex(),eh([]),`成功删除 ${eu.length} 个问题`),error:e=>e.response?.data?.message||"批量删除问题失败"})};return E?n.jsx(o.Z,{maxWidth:"lg",sx:{mt:4},children:n.jsx(l.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"70vh"},children:n.jsx(c.Z,{})})}):(0,n.jsxs)(o.Z,{maxWidth:"lg",sx:{mt:4,mb:8},children:[ep&&n.jsx(l.Z,{sx:{position:"fixed",top:0,left:0,right:0,bottom:0,width:"100vw",height:"100vh",backgroundColor:"rgba(0, 0, 0, 0.7)",zIndex:9999,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center"},children:(0,n.jsxs)(d.Z,{elevation:6,sx:{width:"90%",maxWidth:500,p:3,borderRadius:2,textAlign:"center"},children:[n.jsx(u.Z,{variant:"h6",sx:{mb:2,color:"primary.main",fontWeight:"bold"},children:t("datasets.generatingDataset")}),(0,n.jsxs)(l.Z,{sx:{mb:3},children:[(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center",mb:1},children:[(0,n.jsxs)(u.Z,{variant:"body1",sx:{mr:1},children:[ej.percentage,"%"]}),n.jsx(l.Z,{sx:{width:"100%"},children:n.jsx(h.Z,{variant:"determinate",value:ej.percentage,sx:{height:8,borderRadius:4},color:"primary"})})]}),(0,n.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",mt:2},children:[n.jsx(u.Z,{variant:"body2",children:t("questions.generatingProgress",{completed:ej.completed,total:ej.total})}),n.jsx(u.Z,{variant:"body2",color:"success.main",sx:{fontWeight:"medium"},children:t("questions.generatedCount",{count:ej.datasetCount})})]})]}),n.jsx(c.Z,{size:60,thickness:4,sx:{mb:2}}),n.jsx(u.Z,{variant:"body2",color:"text.secondary",children:t("questions.pleaseWait")})]})}),(0,n.jsxs)(l.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,n.jsxs)(u.Z,{variant:"h4",children:[t("questions.title")," (",W.total,")"]}),(0,n.jsxs)(l.Z,{sx:{display:"flex",gap:2},children:[n.jsx(x.Z,{variant:"outlined",color:eu.length>0?"error":"primary",startIcon:n.jsx($.Z,{}),onClick:()=>{eT()},disabled:0===eu.length,children:t("questions.deleteSelected")}),n.jsx(x.Z,{variant:"contained",startIcon:n.jsx(M.Z,{}),onClick:ev,children:t("questions.createQuestion")}),n.jsx(x.Z,{variant:"contained",startIcon:n.jsx(I.Z,{}),onClick:eP,disabled:0===eu.length,children:t("questions.batchGenerate")}),n.jsx(p.Z,{title:t("questions.autoGenerateDatasetTip",{defaultValue:"创建后台批量处理任务：自动查询待生成答案的问题并生成答案"}),children:n.jsx(x.Z,{variant:"outlined",color:"secondary",startIcon:n.jsx(I.Z,{}),onClick:eE,children:t("questions.autoGenerateDataset",{defaultValue:"自动生成数据集"})})})]})]}),(0,n.jsxs)(d.Z,{sx:{mb:4},children:[(0,n.jsxs)(m.Z,{value:ec,onChange:(e,t)=>{ed(t)},variant:"fullWidth",indicatorColor:"primary",sx:{borderBottom:1,borderColor:"divider"},children:[n.jsx(g.Z,{label:t("questions.listView")}),n.jsx(g.Z,{label:t("questions.treeView")})]}),n.jsx(l.Z,{sx:{p:2},children:(0,n.jsxs)(j.Z,{direction:{xs:"column",sm:"row"},spacing:2,alignItems:{xs:"stretch",sm:"center"},justifyContent:"space-between",children:[(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center"},children:[n.jsx(f.Z,{checked:eu.length>0&&eu.length===W?.total,indeterminate:eu.length>0&&eu.length<W?.total,onChange:eI}),(0,n.jsxs)(u.Z,{variant:"body2",sx:{ml:1},children:[eu.length>0?t("questions.selectedCount",{count:eu.length}):t("questions.selectAll"),"(",t("questions.totalCount",{count:W.total}),")"]})]}),(0,n.jsxs)(l.Z,{sx:{display:"flex",alignItems:"center",gap:2},children:[n.jsx(Z.Z,{placeholder:t("questions.searchPlaceholder"),variant:"outlined",size:"small",fullWidth:!0,sx:{width:{xs:"100%",sm:300}},value:B,onChange:e=>J(e.target.value),InputProps:{startAdornment:n.jsx(b.Z,{position:"start",children:n.jsx(A.Z,{fontSize:"small",color:"action"})})}}),(0,n.jsxs)(y.Z,{value:N,onChange:e=>V(e.target.value),size:"small",sx:{width:{xs:"100%",sm:200},bgcolor:"dark"===s.palette.mode?"rgba(255, 255, 255, 0.05)":"white",borderRadius:"8px","& .MuiOutlinedInput-notchedOutline":{borderColor:"dark"===s.palette.mode?"transparent":"rgba(0, 0, 0, 0.23)"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"dark"===s.palette.mode?"transparent":"rgba(0, 0, 0, 0.87)"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main"}},MenuProps:{PaperProps:{elevation:2,sx:{mt:1,borderRadius:2}}},children:[n.jsx(v.Z,{value:"all",children:t("questions.filterAll")}),n.jsx(v.Z,{value:"answered",children:t("questions.filterAnswered")}),n.jsx(v.Z,{value:"unanswered",children:t("questions.filterUnanswered")})]})]})]})}),n.jsx(k.Z,{}),n.jsx(X.Z,{value:ec,index:0,children:n.jsx(O,{questions:W.data,currentPage:F,totalQuestions:Math.ceil(W.total/_),handlePageChange:(e,t)=>Q(t),selectedQuestions:eu,onSelectQuestion:ez,onDeleteQuestion:eD,onEditQuestion:ek,refreshQuestions:ex,projectId:D})}),n.jsx(X.Z,{value:ec,index:1,children:n.jsx(H,{questions:W.data,tags:K,selectedQuestions:eu,onSelectQuestion:ez,onDeleteQuestion:eD,onEditQuestion:ek,projectId:D,searchTerm:B})})]}),(0,n.jsxs)(q.Z,{open:eC.open,onClose:()=>ew({...eC,open:!1}),"aria-labelledby":"alert-dialog-title","aria-describedby":"alert-dialog-description",children:[n.jsx(S.Z,{id:"alert-dialog-title",children:eC.title}),n.jsx(C.Z,{children:n.jsx(w.Z,{id:"alert-dialog-description",children:eC.content})}),(0,n.jsxs)(z.Z,{children:[n.jsx(x.Z,{onClick:()=>ew({...eC,open:!1}),color:"primary",children:t("common.cancel")}),n.jsx(x.Z,{onClick:()=>{ew({...eC,open:!1}),eC.confirmAction&&eC.confirmAction()},color:"error",variant:"contained",autoFocus:!0,children:t("common.confirmDelete")})]})]}),n.jsx(et,{open:eZ,onClose:eq,onSubmit:eS,initialData:ey,tags:K,mode:eb,projectId:D})]})}},879:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var n=s(10326),r=s(71728);function a({value:e,index:t,children:s}){return n.jsx(r.Z,{role:"tabpanel",hidden:e!==t,id:`domain-tabpanel-${t}`,"aria-labelledby":`domain-tab-${t}`,sx:{height:"100%"},children:e===t&&n.jsx(r.Z,{sx:{height:"100%"},children:s})})}},67595:(e,t,s)=>{"use strict";s.d(t,{N:()=>r});var n=s(17577);function r(e,t=500){let[s,r]=(0,n.useState)(e);return s}},92495:(e,t,s)=>{"use strict";s.d(t,{l:()=>d});var n=s(17577),r=s(85999),a=s(23297),i=s(44099),o=s(36493),l=s(15431),c=s(70012);function d(){let e=(0,o.useAtomValue)(l._),{t}=(0,c.$G)();return{generateSingleDataset:(0,n.useCallback)(async({projectId:s,questionId:n,questionInfo:o})=>{if(!e)return r.A.error(t("models.configNotFound")),null;let l="zh-CN"===a.Z.language?"中文":"en";r.A.promise(i.Z.post(`/api/projects/${s}/datasets`,{questionId:n,model:e,language:l}),{loading:t("datasets.generating"),description:`问题：【${o}】`,position:"top-right",success:e=>"生成数据集成功",error:e=>t("datasets.generateFailed",{error:e.response?.data?.error})})},[e,t]),generateMultipleDataset:(0,n.useCallback)(async(t,s)=>{let n=0,o=s.length,l=r.A.loading(`正在处理请求 (${n}/${o})...`,{position:"top-right"}),c=async s=>{try{let c=(await i.Z.post(`/api/projects/${t}/datasets`,{questionId:s.id,model:e,language:"zh-CN"===a.Z.language?"中文":"en"})).data;return n++,r.A.success(`${s.question} 完成`,{position:"top-right"}),r.A.loading(`正在处理请求 (${n}/${o})...`,{id:l}),c}catch(e){throw n++,r.A.error(`${s.question} 失败`,{description:e.message,position:"top-right"}),r.A.loading(`正在处理请求 (${n}/${o})...`,{id:l}),e}};try{let e=await Promise.allSettled(s.map(e=>c(e)));return r.A.success(`全部请求处理完成 (成功: ${e.filter(e=>"fulfilled"===e.status).length}/${o})`,{id:l,position:"top-right"}),e}catch{}},[e,t])}}},46790:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var n=s(17577),r=s(70012);let a={textSplitMinLength:1500,textSplitMaxLength:2e3,questionGenerationLength:240,questionMaskRemovingProbability:60,huggingfaceToken:"",concurrencyLimit:5,visionConcurrencyLimit:5};function i(e){let{t}=(0,r.$G)(),[s,i]=(0,n.useState)({...a}),[o,l]=(0,n.useState)(!0),[c,d]=(0,n.useState)(null),[u,h]=(0,n.useState)(!1);return{taskSettings:s,setTaskSettings:i,loading:o,error:c,success:u,setSuccess:h}}},97533:(e,t,s)=>{"use strict";s.d(t,{WY:()=>r,ZP:()=>a});let n=async(e,t={})=>{let s;let{retries:n=1,delay:r=0,onRetry:a=null}=t;for(let t=0;t<=n;t++)try{return await e()}catch(e){if(s=e,t===n)break;a&&"function"==typeof a&&a(e,t+1),r>0&&await new Promise(e=>setTimeout(e,r))}throw s},r=async(e,t={})=>{try{let s=await fetch(e,t),n=await s.json();if(!s.ok)throw Error(`Fetch Error: ${e} ${String(n.error||"Unknown error")}`);return n}catch(s){throw Error(`Fetch Error: ${e}  ${String(s)} ${t.errMsg||""}`)}},a=async(e,t={},s={})=>n(()=>fetch(e,t),s)},42167:(e,t,s)=>{"use strict";var n=s(39618);t.Z=void 0;var r=n(s(71133)),a=s(10326);t.Z=(0,r.default)((0,a.jsx)("path",{d:"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34a.9959.9959 0 0 0-1.41 0l-1.83 1.83 3.75 3.75z"}),"Edit")},33182:(e,t,s)=>{"use strict";var n=s(39618);t.Z=void 0;var r=n(s(71133)),a=s(10326);t.Z=(0,r.default)((0,a.jsx)("path",{d:"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8z"}),"Folder")},76165:(e,t,s)=>{"use strict";var n=s(39618);t.Z=void 0;var r=n(s(71133)),a=s(10326);t.Z=(0,r.default)((0,a.jsx)("path",{d:"M11.07 12.85c.77-1.39 2.25-2.21 3.11-3.44.91-1.29.4-3.7-2.18-3.7-1.69 0-2.52 1.28-2.87 2.34L6.54 6.96C7.25 4.83 9.18 3 11.99 3c2.35 0 3.96 1.07 4.78 2.41.7 1.15 1.11 3.3.03 4.9-1.2 1.77-2.35 2.31-2.97 3.45-.25.46-.35.76-.35 2.24h-2.89c-.01-.78-.13-2.05.48-3.15M14 20c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2"}),"QuestionMark")},5420:(e,t,s)=>{"use strict";var n=s(39618);t.Z=void 0;var r=n(s(71133)),a=s(10326);t.Z=(0,r.default)((0,a.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"}),"Search")},17082:(e,t,s)=>{"use strict";s.d(t,{Z:()=>S});var n=s(91367),r=s(45353),a=s(17577),i=s(41135),o=s(82483),l=s(97898),c=s(88634),d=s(27777),u=s(6333),h=s(70140),x=s(67240),p=s(31767),m=s(51561),g=s(10326);let j=["component","direction","spacing","divider","children","className","useFlexGap"],f=(0,x.Z)(),Z=(0,d.Z)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function b(e){return(0,u.Z)({props:e,name:"MuiStack",defaultTheme:f})}let y=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],v=({ownerState:e,theme:t})=>{let s=(0,r.Z)({display:"flex",flexDirection:"column"},(0,p.k9)({theme:t},(0,p.P$)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e})));if(e.spacing){let n=(0,m.hB)(t),r=Object.keys(t.breakpoints.values).reduce((t,s)=>(("object"==typeof e.spacing&&null!=e.spacing[s]||"object"==typeof e.direction&&null!=e.direction[s])&&(t[s]=!0),t),{}),a=(0,p.P$)({values:e.direction,base:r}),i=(0,p.P$)({values:e.spacing,base:r});"object"==typeof a&&Object.keys(a).forEach((e,t,s)=>{if(!a[e]){let n=t>0?a[s[t-1]]:"column";a[e]=n}}),s=(0,o.Z)(s,(0,p.k9)({theme:t},i,(t,s)=>e.useFlexGap?{gap:(0,m.NA)(n,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${y(s?a[s]:e.direction)}`]:(0,m.NA)(n,t)}}))}return(0,p.dt)(t.breakpoints,s)};var k=s(91703),q=s(2791);let S=function(e={}){let{createStyledComponent:t=Z,useThemeProps:s=b,componentName:o="MuiStack"}=e,d=()=>(0,c.Z)({root:["root"]},e=>(0,l.ZP)(o,e),{}),u=t(v);return a.forwardRef(function(e,t){let o=s(e),l=(0,h.Z)(o),{component:c="div",direction:x="column",spacing:p=0,divider:m,children:f,className:Z,useFlexGap:b=!1}=l,y=(0,n.Z)(l,j),v=d();return(0,g.jsx)(u,(0,r.Z)({as:c,ownerState:{direction:x,spacing:p,useFlexGap:b},ref:t,className:(0,i.Z)(v.root,Z)},y,{children:m?function(e,t){let s=a.Children.toArray(e).filter(Boolean);return s.reduce((e,n,r)=>(e.push(n),r<s.length-1&&e.push(a.cloneElement(t,{key:`separator-${r}`})),e),[])}(f,m):f}))})}({createStyledComponent:(0,k.ZP)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,q.i)({props:e,name:"MuiStack"})})},58138:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(68570).createProxy)(String.raw`D:\office\niuma-dataset\easy-dataset\app\projects\[projectId]\layout.js#default`)},23897:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});let n=(0,s(68570).createProxy)(String.raw`D:\office\niuma-dataset\easy-dataset\app\projects\[projectId]\questions\page.js#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[8948,622,2797,2249,4350,8347,1411,65,9787,9479,9474,8785],()=>s(15560));module.exports=n})();