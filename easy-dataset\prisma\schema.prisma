generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["darwin-arm64", "darwin", "windows", "debian-openssl-3.0.x", "linux-arm64-openssl-3.0.x", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Projects {
  id                   String        @id @default(nanoid(12))
  name                 String
  description          String
  country              String?       @db.VarChar(100)
  globalPrompt         String        @default("") @db.VarChar(2000)
  questionPrompt       String        @default("") @db.VarChar(3000)
  answerPrompt         String        @default("") @db.VarChar(2000)
  labelPrompt          String        @default("") @db.VarChar(2000)
  domainTreePrompt     String        @default("") @db.VarChar(2000)
  defaultModelConfigId String?
  test                 String        @default("") @db.VarChar(2000)
  createAt             DateTime      @default(now())
  updateAt             DateTime      @updatedAt
  Questions            Questions[]
  Datasets             Datasets[]
  Chunks               Chunks[]
  ModelConfig          ModelConfig[]
  UploadFiles          UploadFiles[]
  Tags                 Tags[]
  Task                 Task[]
  GaPairs              GaPairs[]
  FileRecords          FileRecords[]
}

model UploadFiles {
  id        String    @id @default(nanoid())
  project   Projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  fileName  String
  fileExt   String
  path      String
  size      Int
  md5       String
  createAt  DateTime  @default(now())
  updateAt  DateTime  @updatedAt
  GaPairs   GaPairs[]
}

model Chunks {
  id        String      @id @default(nanoid())
  name      String
  project   Projects    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  fileId    String
  fileName  String
  content   String      @db.LongText
  summary   String      @db.Text
  size      Int
  createAt  DateTime    @default(now())
  updateAt  DateTime    @updatedAt
  Questions Questions[]

  @@index([projectId])
}

model Tags {
  id        String   @id @default(nanoid())
  label     String
  project   Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  parentId  String?
  parent    Tags?    @relation("Tags", fields: [parentId], references: [id])
  children  Tags[]   @relation("Tags")

  @@unique([projectId, label, parentId])
}

model Questions {
  id        String   @id @default(nanoid())
  project   Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId String
  chunk     Chunks   @relation(fields: [chunkId], references: [id])
  chunkId   String
  gaPair    GaPairs? @relation(fields: [gaPairId], references: [id])
  gaPairId  String? // Optional: links question to the GA pair that generated it
  question  String   @db.LongText
  label     String   @db.LongText
  answered  Boolean  @default(false)
  createAt  DateTime @default(now())
  updateAt  DateTime @updatedAt

  @@index([projectId])
}

model Datasets {
  id            String   @id @default(nanoid())
  project       Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     String
  questionId    String
  question      String   @db.LongText
  answer        String   @db.LongText
  instruction   String?  @db.LongText
  chunkName     String
  chunkContent  String   @db.LongText
  model         String
  questionLabel String   @db.LongText
  cot           String   @db.LongText
  confirmed     Boolean  @default(false)
  createAt      DateTime @default(now())
  updateAt      DateTime @updatedAt

  @@index([projectId])
}

model LlmProviders {
  id        String      @id
  name      String
  apiUrl    String
  createAt  DateTime    @default(now())
  updateAt  DateTime    @updatedAt
  LlmModels LlmModels[]
}

model LlmModels {
  id         String       @id @default(nanoid())
  modelId    String
  modelName  String
  provider   LlmProviders @relation(fields: [providerId], references: [id])
  providerId String
  createAt   DateTime     @default(now())
  updateAt   DateTime     @updatedAt
}

model ModelConfig {
  id           String   @id @default(nanoid())
  project      Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId    String
  providerId   String
  providerName String
  endpoint     String   @db.LongText
  apiKey       String   @db.LongText
  modelId      String
  modelName    String
  type         String
  temperature  Float
  maxTokens    Int
  topP         Float
  topK         Float
  status       Int
  createAt     DateTime @default(now())
  updateAt     DateTime @updatedAt
}

model Task {
  id             String    @id @default(nanoid())
  project        Projects  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId      String
  taskType       String // 任务类型: text-processing, question-generation, answer-generation, data-distillation
  status         Int // 任务状态: 0-处理中, 1-已完成, 2-失败, 3-已中断
  startTime      DateTime  @default(now())
  endTime        DateTime?
  completedCount Int       @default(0)
  totalCount     Int       @default(0)
  modelInfo      String    @db.LongText // JSON格式存储，包含使用的模型信息
  language       String    @default("zh-CN")
  detail         String    @db.LongText // 任务详情
  note           String?   @db.LongText // 任务备注
  createAt       DateTime  @default(now())
  updateAt       DateTime  @updatedAt

  @@index([projectId])
}

model GaPairs {
  id            String      @id @default(nanoid())
  project       Projects    @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId     String
  uploadFile    UploadFiles @relation(fields: [fileId], references: [id], onDelete: Cascade)
  fileId        String
  pairNumber    Int // 1-5, representing the 5 generated pairs
  genreTitle    String // Genre name/title
  genreDesc     String      @db.LongText // Genre description
  audienceTitle String // Audience name/title
  audienceDesc  String      @db.LongText // Audience description
  isActive      Boolean     @default(true) // Whether this pair is active for use
  questions     Questions[] // Questions generated by this GA pair
  createAt      DateTime    @default(now())
  updateAt      DateTime    @updatedAt

  @@unique([fileId, pairNumber])
  @@index([projectId])
  @@index([fileId])
}

model FileRecords {
  id           String   @id @default(nanoid())
  project      Projects @relation(fields: [projectId], references: [id], onDelete: Cascade)
  projectId    String
  fileName     String   // 文件名
  originalName String?  // 原始文件名（用户上传时的文件名）
  fileExt      String   // 文件扩展名
  fileFormat   String   // 文件格式类型：excel, json, jsonl, csv, pdf, docx, markdown, txt
  operationType String  // 操作类型：import, export, upload
  filePath     String?  // 文件存储路径
  fileSize     Int      // 文件大小（字节）
  md5Hash      String?  // 文件MD5哈希值
  mimeType     String?  // MIME类型
  status       Int      @default(1) // 文件状态：0-处理中, 1-成功, 2-失败, 3-已删除
  recordCount  Int?     // 记录数量（对于数据集文件）
  description  String?  @db.Text // 文件描述或备注
  metadata     String?  @db.LongText // 额外元数据（JSON格式）
  taskId       String?  // 关联的任务ID（如果是通过任务处理的）
  createAt     DateTime @default(now())
  updateAt     DateTime @updatedAt

  @@index([projectId])
  @@index([operationType])
  @@index([fileFormat])
  @@index([status])
}
