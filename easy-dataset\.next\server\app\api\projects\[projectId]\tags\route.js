"use strict";(()=>{var e={};e.id=2204,e.ids=[2204],e.modules={53524:e=>{e.exports=require("@prisma/client")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},86192:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>j,requestAsyncStorage:()=>w,routeModule:()=>f,serverHooks:()=>y,staticGenerationAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{DELETE:()=>h,GET:()=>l,POST:()=>b,PUT:()=>u});var n=a(49303),o=a(88716),s=a(60670),i=a(87070),d=a(3800),c=a(3359);async function l(e,{params:t}){try{let{projectId:e}=t;if(!e)return i.NextResponse.json({error:"Project ID is required"},{status:400});let a=await (0,d.getTags)(e);return i.NextResponse.json({tags:a})}catch(e){return console.error("Failed to obtain the label tree:",String(e)),i.NextResponse.json({error:e.message||"Failed to obtain the label tree"},{status:500})}}async function u(e,{params:t}){try{let{projectId:a}=t;if(!a)return i.NextResponse.json({error:"Project ID is required"},{status:400});let{tags:r}=await e.json();if(void 0===r.id||null===r.id||""===r.id){console.log("createTag",r);let e=await (0,d.createTag)(a,r.label,r.parentId);return i.NextResponse.json({tags:e})}{let e=await (0,d.updateTag)(r.label,r.id);return i.NextResponse.json({tags:e})}}catch(e){return console.error("Failed to update tags:",String(e)),i.NextResponse.json({error:e.message||"Failed to update tags"},{status:500})}}async function b(e,{params:t}){try{let{projectId:a}=t;if(!a)return i.NextResponse.json({error:"Project ID is required"},{status:400});let{tagName:r}=await e.json();console.log("tagName",r);let n=await (0,c.uE)(a,r);return i.NextResponse.json(n)}catch(e){return console.error("Failed to obtain the label tree:",String(e)),i.NextResponse.json({error:e.message||"Failed to obtain the label tree"},{status:500})}}async function h(e,{params:t}){try{let{projectId:a}=t;if(!a)return i.NextResponse.json({error:"Project ID is required"},{status:400});let{searchParams:r}=new URL(e.url),n=r.get("id");if(!n)return i.NextResponse.json({error:"标签 ID 是必需的"},{status:400});console.log(`正在删除标签: ${n}`);let o=await (0,d.deleteTag)(n);return console.log(`删除标签成功: ${n}`),i.NextResponse.json({success:!0,message:"删除标签成功",data:o})}catch(e){return console.error("删除标签失败:",String(e)),i.NextResponse.json({error:e.message||"删除标签失败",success:!1},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/projects/[projectId]/tags/route",pathname:"/api/projects/[projectId]/tags",filename:"route",bundlePath:"app/api/projects/[projectId]/tags/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\[projectId]\\tags\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:p,serverHooks:y}=f,g="/api/projects/[projectId]/tags/route";function j(){return(0,s.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:p})}},4342:(e,t,a)=>{a.d(t,{db:()=>n});var r=a(53524);let n=globalThis.prisma||new r.PrismaClient({log:["error"]})},3359:(e,t,a)=>{a.d(t,{AW:()=>g,BQ:()=>d,Ec:()=>s,HQ:()=>q,Km:()=>j,VJ:()=>y,aJ:()=>h,n_:()=>w,pb:()=>i,tI:()=>o,uE:()=>l,updateQuestion:()=>p,wu:()=>c,xL:()=>b});var r=a(24330);a(60166);var n=a(4342);async function o(e,t=1,a=10,r,o){try{let s={projectId:e,...void 0!==r&&{answered:r},OR:[{question:{contains:o}},{label:{contains:o}}]},[i,d]=await Promise.all([n.db.questions.findMany({where:s,orderBy:{createAt:"desc"},include:{chunk:{select:{name:!0,content:!0}}},skip:(t-1)*a,take:a}),n.db.questions.count({where:s})]),c=await u(i.map(e=>e.id));return{data:i.map((e,t)=>({...e,datasetCount:c[t]})),total:d}}catch(e){throw console.error("Failed to get questions by projectId in database"),e}}async function s(e,t,a=!1){try{console.log("[getQuestionsForTree] 参数:",{projectId:e,input:t,isDistill:a});let r={projectId:e,question:{contains:t||""}};if(a){let t=await n.db.chunks.findFirst({where:{projectId:e,name:"Distilled Content"}});t&&(r.chunkId=t.id)}return await n.db.questions.findMany({where:r,select:{id:!0,label:!0,answered:!0},orderBy:{createAt:"desc"}})}catch(e){throw console.error("获取树形视图问题失败:",e),e}}async function i(e,t,a,r=!1){try{let o={projectId:e};if(a&&(o.question={contains:a}),"uncategorized"===t?o.label={in:[t,"其他","Other","other"]}:o.label={in:[t]},r){let t=await n.db.chunks.findFirst({where:{projectId:e,name:"Distilled Content"}});t&&(o.chunkId=t.id)}let s=await n.db.questions.findMany({where:o,include:{chunk:{select:{name:!0,content:!0}}},orderBy:{createAt:"desc"}}),i=await u(s.map(e=>e.id));return s.map((e,t)=>({...e,datasetCount:i[t]}))}catch(e){throw console.error(`根据标签获取问题失败 (${t}):`,e),e}}async function d(e){try{return await n.db.questions.findMany({where:{projectId:e},include:{chunk:{select:{name:!0,content:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get datasets ids in database"),e}}async function c(e,t,a){try{let r={projectId:e,...void 0!==t&&{answered:t},OR:[{question:{contains:a}},{label:{contains:a}}]};return await n.db.questions.findMany({where:r,select:{id:!0},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get datasets ids in database"),e}}async function l(e,t){try{return await n.db.questions.findMany({where:{projectId:e,label:t},include:{chunk:{select:{name:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get datasets ids in database"),e}}async function u(e){let t=(await n.db.datasets.groupBy({by:["questionId"],_count:{questionId:!0},where:{questionId:{in:e}}})).reduce((e,t)=>(e[t.questionId]=t._count.questionId,e),{});return e.map(e=>t[e]||0)}async function b(e){try{return await n.db.questions.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get questions by name in database"),e}}async function h(e){try{return await n.db.questions.count({where:{question:e}})>0}catch(e){throw console.error("Failed to get questions by name in database"),e}}async function f(e){try{return await n.db.questions.count({where:{projectId:e}})}catch(e){throw console.error("Failed to get questions count in database"),e}}async function w(e,t,a){try{let r=t.map(t=>({id:t.id,projectId:e,chunkId:a||t.chunkId,question:t.question,label:t.label}));return await n.db.questions.createMany({data:r})}catch(e){throw console.error("Failed to create questions in database"),e}}async function p(e,t){try{let{question:a,tagIds:r,...o}=t,s={...o};if(void 0!==a&&(s.question=a),r&&Array.isArray(r)&&r.length>0&&!t.label){let e=await n.db.tags.findUnique({where:{id:r[0]},select:{label:!0}});e&&(s.label=e.label)}return await n.db.questions.update({where:{id:e},data:s,include:{chunk:{select:{name:!0}}}})}catch(e){throw console.error("Failed to update questions in database"),e}}async function y(e,t,a,r=null){try{let o=t.map(t=>({id:t.id,projectId:e,chunkId:a||t.chunkId,question:t.question,label:t.label,gaPairId:r}));return await n.db.questions.createMany({data:o})}catch(e){throw console.error("Failed to create questions with GA pair in database"),e}}async function g(e,t){return await n.db.questions.findMany({where:{projectId:e,chunkId:t}})}async function j(e){try{return console.log(e),await n.db.datasets.deleteMany({where:{questionId:e}}),await n.db.questions.delete({where:{id:e}})}catch(e){throw console.error("Failed to delete questions by id in database"),e}}async function q(e){try{return await n.db.datasets.deleteMany({where:{questionId:{in:e}}}),await n.db.questions.deleteMany({where:{id:{in:e}}})}catch(e){throw console.error("Failed to delete batch questions in database"),e}}(0,a(40618).h)([o,s,i,d,c,l,b,h,f,w,p,y,g,j,q]),(0,r.j)("09fc735caa24ac2a6a2ea80ef743749a89e0509e",o),(0,r.j)("6dbc2d095fa7b7d169d3f0b1a0ee441860c2b72a",s),(0,r.j)("8a5569b9044a1c51b0b707f47a5ccbdce323c35b",i),(0,r.j)("817df8038d5814556ce6171496fca48dd54a2a7b",d),(0,r.j)("268bb19ce9bef6e19d3ebe8035be6069a967595f",c),(0,r.j)("844cd6ac4c715dca952fdcf247fe7dbef3851634",l),(0,r.j)("29abf6427b4db8c6b4c83d8014ad580fce59fce3",b),(0,r.j)("d66f985625e74f11b855b839106905edc264cd3b",h),(0,r.j)("412f996a83128f86af3455885f752d50e4f3865e",f),(0,r.j)("cbd11532dd53c2049c9470e76f7900aee41d7724",w),(0,r.j)("45267cfeee7be5007fcce8889b9ee3dad116b245",p),(0,r.j)("8d238c8865c872dbff5deea06583b87148daccd4",y),(0,r.j)("b8d0d1e8be77c4a52f239f4eba596e11983a9b1d",g),(0,r.j)("210f8b8fbf089dd3203ac511db68d4a69cf038d4",j),(0,r.j)("35b87f2dad540cdbbe7adbcf81b8c2ba2bd62f68",q)},3800:(e,t,a)=>{a.r(t),a.d(t,{batchSaveTags:()=>w,createTag:()=>c,deleteTag:()=>u,getTags:()=>s,updateTag:()=>l});var r=a(24330);a(60166);var n=a(4342),o=a(50121);async function s(e){try{let t=await i(e);return(0,o.h)(t)}catch(e){return[]}}async function i(e,t=null){let a=await n.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of a){let a=await d(t.id);t.questionCount=await n.db.questions.count({where:{label:{in:a},projectId:e}}),t.child=await i(e,t.id)}return a}async function d(e){let t=[],a=[e];for(;a.length>0;){let e=a.shift(),r=await n.db.tags.findUnique({where:{id:e}});if(r){t.push(r.label);let o=await n.db.tags.findMany({where:{parentId:e},select:{id:!0}});a.push(...o.map(e=>e.id))}}return t}async function c(e,t,a){try{let r=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return console.log(`标签已存在: ${t}，返回现有标签`),r;let o={projectId:e,label:t};return a&&(o.parentId=a),await n.db.tags.create({data:o})}catch(r){if("P2002"===r.code&&r.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let r=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return r}throw console.error("Error insert tags db:",r),r}}async function l(e,t){try{let a=await n.db.tags.findUnique({where:{id:t}});if(!a)throw Error(`标签不存在: ${t}`);let r=a.label,o=a.projectId,s=await n.db.tags.update({where:{id:t},data:{label:e}});return r!==e&&(console.log(`标签名称从 "${r}" 更新为 "${e}"，开始同步更新相关数据`),await n.db.questions.updateMany({where:{label:r,projectId:o},data:{label:e}}),console.log(`已更新问题表中的标签: ${r} -> ${e}`),await n.db.datasets.updateMany({where:{questionLabel:r,projectId:o},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${r} -> ${e}`)),s}catch(e){throw console.error("Error update tags db:",e),e}}async function u(e){try{console.log(`开始删除标签: ${e}`);let t=await n.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let a=await b(e,t.projectId);for(let e of(console.log(`找到 ${a.length} 个子标签需要删除`),a.reverse()))await f(e.label,e.projectId),await h(e.label,e.projectId),await n.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await f(t.label,t.projectId),await h(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await n.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function b(e,t){let a=[];async function r(e){let o=await n.db.tags.findMany({where:{parentId:e,projectId:t}});if(o.length>0)for(let e of(a.push(...o),o))await r(e.id)}return await r(e),a}async function h(e,t){try{await n.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function f(e,t){try{await n.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function w(e,t){try{await n.db.tags.deleteMany({where:{projectId:e}}),await p(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function p(e,t,a=null){for(let r of t){let t=await n.db.tags.create({data:{projectId:e,label:r.label,parentId:a}});r.child&&r.child.length>0&&await p(e,r.child,t.id)}}(0,a(40618).h)([s,c,l,u,w]),(0,r.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",s),(0,r.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",c),(0,r.j)("745145798d6802bd295e673536ce529c25576c3c",l),(0,r.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",u),(0,r.j)("1e89c922534cbdd6a59e5783551aafd799be4734",w)},50121:(e,t,a)=>{a.d(t,{E:()=>r,h:()=>function e(t,a=0,r=0){return Array.isArray(t)?t.map((t,n)=>{let o;if(0===a){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let a=Math.floor(e/10),r=e%10;return 1===a?0===r?"十":"十"+t[r]:t[a]+"十"+(0===r?"":t[r])}(n+1);o=`${e}、${t.label}`}else o=`${r+1}.${n+1} ${t.label}`;let s={...t,displayLabel:o,displayName:t.label};return t.child&&t.child.length>0&&(s.child=e(t.child,a+1,n)),t.children&&t.children.length>0&&(s.children=e(t.children,a+1,n)),s}):[]}});function r(e,t){return e&&Array.isArray(t)?function t(a){for(let r of a){if(r.label===e)return r;if(r.child&&r.child.length>0){let e=t(r.child);if(e)return e}}return null}(t):null}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,5972,8341],()=>a(86192));module.exports=r})();