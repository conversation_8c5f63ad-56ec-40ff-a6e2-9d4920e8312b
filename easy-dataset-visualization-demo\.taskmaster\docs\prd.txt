# 东盟十国地图数据可视化项目

## 项目概述
本项目旨在构建一个交互式地图可视化应用，允许用户通过点击东盟十国（东南亚国家联盟：文莱、柬埔寨、印度尼西亚、老挝、马来西亚、缅甸、菲律宾、新加坡、泰国和越南）的地图来切换和展示不同国家的数据信息。

## 技术要求
1. 使用ECharts库进行地图和数据可视化
2. 通过CDN方式引入ECharts（使用echarts.min.js）
3. 响应式设计，适应不同屏幕尺寸
4. 纯前端实现，无需后端支持

## 功能需求

### 核心功能
1. **交互式东盟地图**
   - 展示完整的东盟十国地图
   - 每个国家可点击，有悬停效果
   - 点击国家后高亮显示该国

2. **数据面板**
   - 根据选中的国家显示相关数据
   - 数据面板包含多个图表（柱状图、折线图等）
   - 面板应支持平滑过渡动画

3. **数据可视化图表**
   - 每个国家至少包含3种不同类型的数据图表
   - 支持基本的图表交互（如悬停显示详情）
   - 图表应有适当的标题和图例

### 附加功能
1. **数据筛选器**
   - 允许用户选择不同时间段的数据
   - 提供数据类型的筛选选项

2. **比较模式**
   - 允许用户选择两个国家进行数据对比
   - 对比视图应并排显示两国数据

3. **数据导出**
   - 支持将当前视图导出为图片
   - 可选导出基础数据为CSV格式

## 用户界面要求
1. 清晰的布局，左侧为地图，右侧为数据面板
2. 简洁现代的设计风格
3. 适当的颜色编码以区分不同国家
4. 响应式布局，在移动设备上可适当调整为上下布局

## 数据要求
1. 每个国家包含以下基础数据：
   - 人口统计数据
   - 经济指标（GDP、贸易额等）
   - 社会发展指标

2. 数据应包含多年信息，支持趋势分析
3. 所有数据应预先准备并内嵌在应用中

## 技术实现细节
1. 使用HTML5, CSS3和JavaScript
2. 使用ECharts 5.6.0版本，通过CDN引入：
   ```
   https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/echarts.min.js
   ```
3. 可选使用ECharts扩展：
   ```
   https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/extension/dataTool.min.js
   ```
4. 项目结构应清晰，代码应有适当注释
5. 所有交互应有适当的加载状态和错误处理

## 性能要求
1. 页面加载时间不超过3秒
2. 切换国家数据时响应时间不超过1秒
3. 流畅的动画过渡，无明显卡顿

## 兼容性要求
1. 支持主流现代浏览器（Chrome, Firefox, Safari, Edge）
2. 支持移动设备浏览器
3. 响应式设计，适应不同屏幕尺寸

## 交付物
1. 完整的HTML文件（index-东盟十国.html）
2. 所有必要的CSS和JavaScript文件
3. 示例数据文件
4. 简要的使用说明文档 