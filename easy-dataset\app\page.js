'use client';

import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';

// 动态导入 big-screen 组件，禁用 SSR
const BigScreenApp = dynamic(() => import('./big-screen/BigScreenApp'), {
  ssr: false,
});

export default function Home() {
  useEffect(() => {
    // 设置全屏样式
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflowX = 'hidden'; // 禁用横向滚动条
    document.body.style.overflowY = 'auto';   // 允许竖向滚动条

    return () => {
      // 清理样式
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.overflowX = '';
      document.body.style.overflowY = '';
    };
  }, []);

  return (
    <div style={{ width: '100%', minHeight: '100vh', overflowX: 'hidden' }}>
      <BigScreenApp />
    </div>
  );
}
