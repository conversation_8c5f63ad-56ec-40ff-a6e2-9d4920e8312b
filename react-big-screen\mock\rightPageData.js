// 右侧界面数据
module.exports = {
  'GET /api/rightPageData': {
    browseCategories: {
      data: [782, 621.2, 322.1, 525.3, 265, 224],
      indicator: [
        {
          name: '食物',
          max: 1000,
        },
        {
          name: '娱乐',
          max: 1000,
        },
        {
          name: '运动',
          max: 1000,
        },
        {
          name: '家居',
          max: 1000,
        },
        {
          name: '机械',
          max: 1000,
        },
        {
          name: '学习',
          max: 1000,
        },
      ],
    },
    userIdentityCategory: {
      data: [
        {
          name: '公务',
          value: 57,
        },
        {
          name: '学生',
          value: 167,
        },
        {
          name: '教师',
          value: 123,
        },
        {
          name: '军区',
          value: 55,
        },
        {
          name: '企业',
          value: 198,
        },
      ],
    },
    offline: {
      feedback: [
        {
          title: '服务质量',
          number: 90,
        },
        {
          title: '交互体验',
          number: 82,
        },
        {
          title: '安全防护',
          number: 85,
        },
      ],
      offlinePortalData: {
        data1: [80, 152, 101, 134, 90, 130],
        data2: [120, 182, 191, 210, 170, 110],
        data3: [110, 132, 201, 154, 150, 80],
        data4: [90, 142, 161, 114, 190, 170],
        xData: ['9:00', '12:00', '15:00', '18:00', '21:00', '00:00'],
        barData: [32.2, 60.0, 32.6, 36.4, 53.3, 35.0],
      },
    },
  },
};
