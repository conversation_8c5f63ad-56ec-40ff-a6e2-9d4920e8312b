/**
 * 答案质量分析图表
 * 分析答案质量，包括长度分布、生成时间和质量分数
 */

// 1. 答案长度分布图
function generateAnswerLengthData() {
    const data = [];
    
    // 使用正态分布生成更真实的数据
    const mean = 250; // 平均字符数
    const stdDev = 100; // 标准差
    const count = 500; // 样本数量
    
    for (let i = 0; i < count; i++) {
        // 使用Box-Muller变换生成正态分布的随机数
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();
        let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        
        // 将正态分布的随机数转换为答案长度
        let length = Math.round(z * stdDev + mean);
        
        // 确保长度为正数
        length = Math.max(50, length);
        
        data.push(length);
    }
    
    return data;
}

function initAnswerLengthChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('answer-length-distribution');
    if (!chartContainer) {
        console.error('找不到图表容器：answer-length-distribution');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const lengthData = generateAnswerLengthData();
    
    // 计算数据的最小值和最大值，用于确定直方图的范围
    const minLength = Math.min(...lengthData);
    const maxLength = Math.max(...lengthData);
    
    // 设置直方图的分箱数量
    const binCount = 20;
    const binSize = Math.ceil((maxLength - minLength) / binCount);
    
    // 创建分箱
    const bins = Array(binCount).fill(0);
    const binLabels = [];
    
    // 计算每个分箱的范围标签
    for (let i = 0; i < binCount; i++) {
        const start = minLength + i * binSize;
        const end = start + binSize - 1;
        binLabels.push(`${start}-${end}`);
    }
    
    // 将数据分配到各个分箱
    lengthData.forEach(length => {
        const binIndex = Math.min(
            Math.floor((length - minLength) / binSize),
            binCount - 1
        );
        bins[binIndex]++;
    });
    
    // 配置图表选项
    const option = {
        title: {
            text: '答案长度分布 - 直方图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const range = params.name.split('-');
                return `答案长度: ${params.name} 字符<br/>数量: ${params.value}`;
            }
        },
        xAxis: {
            type: 'category',
            data: binLabels,
            name: '字符数',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                rotate: 45,
                interval: Math.ceil(binCount / 10)
            }
        },
        yAxis: {
            type: 'value',
            name: '答案数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '答案数量',
                type: 'bar',
                data: bins,
                itemStyle: {
                    color: '#5470c6'
                },
                emphasis: {
                    itemStyle: {
                        color: '#3a56b4'
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 2. 答案生成时间分布
function generateAnswerTimeData() {
    const data = [];
    
    // 使用正态分布生成更真实的数据
    const mean = 2500; // 平均生成时间（毫秒）
    const stdDev = 1000; // 标准差
    const count = 500; // 样本数量
    
    for (let i = 0; i < count; i++) {
        // 使用Box-Muller变换生成正态分布的随机数
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();
        let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        
        // 将正态分布的随机数转换为生成时间
        let time = Math.round(z * stdDev + mean);
        
        // 确保时间为正数
        time = Math.max(500, time);
        
        data.push(time);
    }
    
    return data;
}

function initAnswerTimeChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('answer-generation-time');
    if (!chartContainer) {
        console.error('找不到图表容器：answer-generation-time');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const timeData = generateAnswerTimeData();
    
    // 计算数据的最小值和最大值，用于确定直方图的范围
    const minTime = Math.min(...timeData);
    const maxTime = Math.max(...timeData);
    
    // 设置直方图的分箱数量
    const binCount = 20;
    const binSize = Math.ceil((maxTime - minTime) / binCount);
    
    // 创建分箱
    const bins = Array(binCount).fill(0);
    const binLabels = [];
    
    // 计算每个分箱的范围标签
    for (let i = 0; i < binCount; i++) {
        const start = minTime + i * binSize;
        const end = start + binSize - 1;
        binLabels.push(`${start}-${end}`);
    }
    
    // 将数据分配到各个分箱
    timeData.forEach(time => {
        const binIndex = Math.min(
            Math.floor((time - minTime) / binSize),
            binCount - 1
        );
        bins[binIndex]++;
    });
    
    // 配置图表选项
    const option = {
        title: {
            text: '答案生成时间分布 - 直方图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const range = params.name.split('-');
                return `生成时间: ${params.name} 毫秒<br/>数量: ${params.value}`;
            }
        },
        xAxis: {
            type: 'category',
            data: binLabels,
            name: '生成时间 (毫秒)',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                rotate: 45,
                interval: Math.ceil(binCount / 10)
            }
        },
        yAxis: {
            type: 'value',
            name: '答案数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '答案数量',
                type: 'bar',
                data: bins,
                itemStyle: {
                    color: '#91cc75'
                },
                emphasis: {
                    itemStyle: {
                        color: '#5ba94c'
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 3. 答案质量评分分布
function generateAnswerQualityData() {
    // 生成1-10分的质量评分分布
    const data = [];
    
    // 使用正态分布生成更真实的数据
    const mean = 7.5; // 平均分（偏向高分）
    const stdDev = 1.5; // 标准差
    const count = 1000; // 样本数量
    
    for (let i = 0; i < count; i++) {
        // 使用Box-Muller变换生成正态分布的随机数
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();
        let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        
        // 将正态分布的随机数转换为1-10的质量评分
        let score = Math.round(z * stdDev + mean);
        score = Math.max(1, Math.min(10, score)); // 限制在1-10之间
        
        data.push(score);
    }
    
    return data;
}

function initAnswerQualityChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('answer-quality-distribution');
    if (!chartContainer) {
        console.error('找不到图表容器：answer-quality-distribution');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const qualityData = generateAnswerQualityData();
    
    // 计算每个评分的数量
    const scoreCount = Array(10).fill(0);
    qualityData.forEach(score => {
        scoreCount[score - 1]++;
    });
    
    // 配置图表选项
    const option = {
        title: {
            text: '答案质量评分分布 - 柱状图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                return `质量评分: ${params[0].name}<br/>答案数量: ${params[0].value}`;
            }
        },
        xAxis: {
            type: 'category',
            data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            name: '质量评分',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                interval: 0
            }
        },
        yAxis: {
            type: 'value',
            name: '答案数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '答案数量',
                type: 'bar',
                data: scoreCount,
                itemStyle: {
                    color: function(params) {
                        // 根据评分设置不同的颜色
                        const colors = [
                            '#ee6666', // 1-3分：红色
                            '#ee6666', 
                            '#ee6666',
                            '#fac858', // 4-6分：黄色
                            '#fac858',
                            '#fac858',
                            '#91cc75', // 7-10分：绿色
                            '#91cc75',
                            '#91cc75',
                            '#91cc75'
                        ];
                        return colors[params.dataIndex];
                    }
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 4. 问答对一致性评分可视化
function generateQAConsistencyData() {
    // 生成问答对一致性评分的数据
    // 创建多个评估维度
    const dimensions = [
        '事实准确性',
        '回答完整性',
        '上下文相关性',
        '逻辑连贯性',
        '语言表达'
    ];
    
    // 为每个维度生成5个模型的得分
    const models = ['GPT-4', 'Claude 3', 'Gemini', 'LLaMA 3', 'Mixtral'];
    const data = [];
    
    models.forEach(model => {
        const scores = dimensions.map(dimension => {
            // 生成70-100之间的随机分数
            return Math.floor(Math.random() * 30) + 70;
        });
        
        data.push({
            name: model,
            value: scores
        });
    });
    
    return {
        dimensions: dimensions,
        data: data
    };
}

function initQAConsistencyChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('qa-consistency');
    if (!chartContainer) {
        console.error('找不到图表容器：qa-consistency');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const consistencyData = generateQAConsistencyData();
    
    // 配置图表选项
    const option = {
        title: {
            text: '问答对一致性评分 - 雷达图 (radar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            data: consistencyData.data.map(item => item.name),
            bottom: '5%'
        },
        radar: {
            indicator: consistencyData.dimensions.map(dimension => {
                return { name: dimension, max: 100 };
            }),
            center: ['50%', '50%'],
            radius: '60%'
        },
        series: [
            {
                type: 'radar',
                data: consistencyData.data.map(item => {
                    return {
                        value: item.value,
                        name: item.name
                    };
                }),
                emphasis: {
                    lineStyle: {
                        width: 4
                    }
                }
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 在文档加载完成后初始化所有图表
document.addEventListener('DOMContentLoaded', function() {
    initAnswerLengthChart();
    initAnswerTimeChart();
    initAnswerQualityChart();
    initQAConsistencyChart();
}); 