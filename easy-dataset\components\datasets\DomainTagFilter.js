'use client';

import { useState, useEffect, useRef } from 'react';
import {
    Box,
    Typography,
    Collapse,
    List,
    ListItem,
    ListItemButton,
    Divider,
    Paper,
    IconButton,
    InputBase,
    ListItemIcon,
    ListItemText,
    useTheme,
    alpha
} from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import SearchIcon from '@mui/icons-material/Search';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import { DEFAULT_DOMAIN_TAGS } from '@/constant';
import axios from 'axios';

/**
 * 领域标签过滤组件
 * @param {Object} props
 * @param {string} props.projectId - 项目ID
 * @param {function} props.onTagSelect - 选择标签时的回调函数
 * @param {Object} props.selectedTag - 当前选中的标签
 */
export default function DomainTagFilter({ projectId, onTagSelect, selectedTag }) {
    const [tags, setTags] = useState([]);
    const [expandedTags, setExpandedTags] = useState({});
    const [loading, setLoading] = useState(false);
    const [searchValue, setSearchValue] = useState('');
    const theme = useTheme();
    const { t } = useTranslation();
    const searchRef = useRef(null);

    // 获取项目标签
    useEffect(() => {
        const fetchTags = async () => {
            if (!projectId) return;

            setLoading(true);
            try {
                // 尝试从项目获取标签
                const response = await axios.get(`/api/projects/${projectId}/tags`);
                if (response.data && response.data.tags && response.data.tags.length > 0) {
                    setTags(response.data.tags);
                } else {
                    // 如果项目没有标签，使用默认标签
                    setTags(DEFAULT_DOMAIN_TAGS);
                }
            } catch (error) {
                console.error('获取标签失败:', error);
                // 出错时使用默认标签
                setTags(DEFAULT_DOMAIN_TAGS);
            } finally {
                setLoading(false);
            }
        };

        fetchTags();
    }, [projectId]);

    // 聚焦搜索框
    useEffect(() => {
        if (searchRef.current) {
            searchRef.current.focus();
        }
    }, []);

    // 处理标签展开/折叠
    const handleToggleExpand = (tagId) => {
        setExpandedTags(prev => ({
            ...prev,
            [tagId]: !prev[tagId]
        }));
    };

    // 处理标签选择
    const handleSelectTag = (tag) => {
        if (tag) {
            // 如果有子标签，将父标签和子标签信息都包含进来
            if (tag.child && tag.child.length > 0) {
                const enhancedTag = {
                    ...tag,
                    isParentTag: true, // 标记为父标签
                    childTags: tag.child.map(child => child.label) // 保存所有子标签的标签名
                };
                onTagSelect(enhancedTag);
            } else {
                onTagSelect(tag);
            }
        } else {
            onTagSelect(null);
        }
    };

    // 处理子标签选择
    const handleSelectSubTag = (parentTag, subTag) => {
        // 创建包含完整路径的标签对象
        const fullTag = {
            ...subTag,
            parentLabel: parentTag.label
        };
        onTagSelect(fullTag);
    };

    // 处理搜索输入变化
    const handleSearchChange = (e) => {
        setSearchValue(e.target.value);
    };

    // 清除搜索
    const handleClearSearch = () => {
        setSearchValue('');
        if (searchRef.current) {
            searchRef.current.focus();
        }
    };

    // 过滤标签
    const filteredTags = searchValue.trim() === ''
        ? tags
        : tags.map(tag => {
            // 检查父标签是否匹配
            const parentMatch = tag.label.toLowerCase().includes(searchValue.toLowerCase());

            // 过滤匹配的子标签
            const matchingChildren = tag.child?.filter(child =>
                child.label.toLowerCase().includes(searchValue.toLowerCase())
            ) || [];

            // 如果父标签匹配或有匹配的子标签，则返回
            if (parentMatch || matchingChildren.length > 0) {
                return {
                    ...tag,
                    child: matchingChildren.length > 0 ? matchingChildren : tag.child
                };
            }
            return null;
        }).filter(Boolean);

    return (
        <Box>
            {/* 搜索框 */}
            <Paper
                sx={{
                    p: '2px 4px',
                    display: 'flex',
                    alignItems: 'center',
                    border: `1px solid ${theme.palette.divider}`,
                    borderRadius: 1,
                    mb: 2
                }}
            >
                <SearchIcon sx={{ color: 'action.active', ml: 1, fontSize: '1.2rem' }} />
                <InputBase
                    inputRef={searchRef}
                    sx={{ ml: 1, flex: 1, fontSize: '0.875rem' }}
                    placeholder={t('common.search')}
                    value={searchValue}
                    onChange={handleSearchChange}
                />
                {searchValue && (
                    <IconButton
                        size="small"
                        onClick={handleClearSearch}
                        sx={{ p: '5px' }}
                    >
                        <CloseIcon fontSize="small" />
                    </IconButton>
                )}
            </Paper>

            {/* 标签列表 */}
            <List dense sx={{ maxHeight: 300, overflow: 'auto' }}>
                <ListItem disablePadding>
                    <ListItemButton
                        onClick={() => handleSelectTag(null)}
                        sx={{
                            backgroundColor: !selectedTag ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                            '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.05),
                            }
                        }}
                    >
                        <ListItemText
                            primary={t('common.all')}
                            primaryTypographyProps={{
                                fontWeight: !selectedTag ? 600 : 400,
                                fontSize: '0.875rem'
                            }}
                        />
                    </ListItemButton>
                </ListItem>

                <Divider sx={{ my: 1 }} />

                {loading ? (
                    <ListItem>
                        <ListItemText primary={t('common.loading')} />
                    </ListItem>
                ) : filteredTags.length > 0 ? (
                    filteredTags.map((tag, index) => (
                        <Box key={tag.id || index}>
                            <ListItem disablePadding>
                                <ListItemButton
                                    onClick={() => handleSelectTag(tag)}
                                    sx={{
                                        backgroundColor: selectedTag?.label === tag.label ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.05),
                                        }
                                    }}
                                >
                                    <ListItemText
                                        primary={tag.label}
                                        primaryTypographyProps={{
                                            fontWeight: selectedTag?.label === tag.label ? 600 : 400,
                                            fontSize: '0.875rem'
                                        }}
                                    />
                                    {tag.child && tag.child.length > 0 && (
                                        <ListItemIcon sx={{ minWidth: 'auto' }}>
                                            <IconButton
                                                size="small"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleToggleExpand(tag.id || index);
                                                }}
                                                sx={{ padding: '2px' }}
                                            >
                                                {expandedTags[tag.id || index] ? (
                                                    <KeyboardArrowDownIcon fontSize="small" />
                                                ) : (
                                                    <KeyboardArrowRightIcon fontSize="small" />
                                                )}
                                            </IconButton>
                                        </ListItemIcon>
                                    )}
                                </ListItemButton>
                            </ListItem>

                            {/* 子标签折叠面板 */}
                            {tag.child && tag.child.length > 0 && (
                                <Collapse in={expandedTags[tag.id || index]} timeout="auto" unmountOnExit>
                                    <List component="div" disablePadding>
                                        {tag.child.map((subTag, subIndex) => (
                                            <ListItem key={subTag.id || subIndex} disablePadding>
                                                <ListItemButton
                                                    onClick={() => handleSelectSubTag(tag, subTag)}
                                                    sx={{
                                                        pl: 4,
                                                        backgroundColor: selectedTag?.label === subTag.label ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.primary.main, 0.05),
                                                        }
                                                    }}
                                                >
                                                    <ListItemText
                                                        primary={subTag.label}
                                                        primaryTypographyProps={{
                                                            fontWeight: selectedTag?.label === subTag.label ? 600 : 400,
                                                            fontSize: '0.875rem'
                                                        }}
                                                    />
                                                </ListItemButton>
                                            </ListItem>
                                        ))}
                                    </List>
                                </Collapse>
                            )}
                        </Box>
                    ))
                ) : (
                    <ListItem>
                        <ListItemText primary={t('common.noResults')} />
                    </ListItem>
                )}
            </List>
        </Box>
    );
}
