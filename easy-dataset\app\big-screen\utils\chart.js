/*
 * ECharts 组件基础部分
 * 传入 option 和渲染方式 renderer
 * */

import React, { PureComponent } from 'react';
import * as echarts from 'echarts';
import 'zrender/lib/svg/svg';
import { debounce } from './index'; // 一个节流函数

export default class Chart extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      width: '100%',
      height: '100%',
    };
    this.chart = null;
  }
  async componentDidMount() {
    try {
      // 初始化图表
      await this.initChart(this.el);
      // 将传入的配置(包含数据)注入
      this.setOption(this.props.option);
      // 添加点击事件监听
      this.addEventListeners();
      // 监听屏幕缩放，重新绘制 echart 图表
      window.addEventListener('resize', debounce(this.resize, 100));
      // 延迟添加自定义缩放事件监听，确保图表完全初始化
      setTimeout(() => {
        this.addCustomZoomListener();
      }, 100);
    } catch (error) {
      console.error('Chart component mount error:', error);
    }
  }

  componentDidUpdate() {
    // 每次更新组件都重置，但要确保chart已初始化
    if (this.chart) {
      this.setOption(this.props.option);
    }
  }

  componentWillUnmount() {
    // 组件卸载前卸载图表
    this.dispose();
  }
  render() {
    const { width, height } = this.state;

    return (
      <div
        className='default-chart'
        ref={el => (this.el = el)}
        style={{ width, height }}
      />
    );
  }
  initChart = el => {
    // renderer 用于配置渲染方式 可以是 svg 或者 canvas
    const renderer = this.props.renderer || 'canvas';

    return new Promise((resolve, reject) => {
      setTimeout(() => {
        try {
          if (!el) {
            reject(new Error('Chart container element is not available'));
            return;
          }

          this.chart = echarts.init(el, null, {
            renderer,
            width: 'auto',
            height: 'auto',
          });

          if (this.chart) {
            resolve();
          } else {
            reject(new Error('Failed to initialize ECharts instance'));
          }
        } catch (error) {
          console.error('ECharts initialization error:', error);
          reject(error);
        }
      }, 0);
    });
  };
  setOption = option => {
    if (!this.chart || !option) {
      return;
    }

    try {
      const notMerge = this.props.notMerge;
      const lazyUpdate = this.props.lazyUpdate;

      this.chart.setOption(option, notMerge, lazyUpdate);
    } catch (error) {
      console.error('ECharts setOption error:', error);
    }
  };
  dispose = () => {
    if (!this.chart) {
      return;
    }

    this.chart.dispose();
    this.chart = null;
  };
  resize = () => {
    this.chart && this.chart.resize();
  };
  getInstance = () => {
    return this.chart;
  };

  addEventListeners = () => {
    if (!this.chart) {
      return;
    }

    // 添加点击事件监听
    this.chart.on('click', (params) => {
      if (this.props.onChartClick) {
        this.props.onChartClick(params);
      }
    });
  };

  // 添加自定义缩放监听器
  addCustomZoomListener = () => {
    if (!this.chart || !this.el) {
      return;
    }

    // 监听鼠标滚轮事件
    this.el.addEventListener('wheel', (event) => {
      event.preventDefault(); // 阻止默认滚轮行为
      const option = this.chart.getOption();
      if (!option || !option.geo || !option.geo[0]) {
        return;
      }

      const geo = option.geo[0];
      const currentZoom = geo.zoom || 6.5;
      const delta = event.deltaY;

      // 计算新的缩放级别
      let newZoom;
      if (delta < 0) {
        // 向上滚动，放大
        newZoom = Math.min(currentZoom * 1.2, 15);
      } else {
        // 向下滚动，缩小
        newZoom = Math.max(currentZoom * 0.8, 5);
      }

      // 直接设置新的zoom值
      this.chart.setOption({
        geo: {
          zoom: newZoom,
          center: geo.center || [118.0, 9.0] // 保持中心点不变
        }
      }, false);

    }, { passive: false });
  };
}
