import pdf2md from '@opendocsg/pdf2md';
import { getUploadFileInfoByFileName } from '@/lib/db/upload-files';
import { downloadDocument, uploadDocument } from '@/lib/oss-simple';

export async function defaultProcessing(projectId, fileName) {
  console.log('executing default pdf conversion strategy......');

  try {
    // 1. 获取PDF文件信息
    const pdfFileInfo = await getUploadFileInfoByFileName(projectId, fileName);
    if (!pdfFileInfo) {
      throw new Error(`PDF file ${fileName} not found in database`);
    }

    // 2. 从OSS下载PDF文件
    const pdfResult = await downloadDocument(pdfFileInfo.id);
    // 确保content是Buffer格式（PDF文件应该返回Buffer）
    const pdfBuffer = Buffer.isBuffer(pdfResult.content) ? pdfResult.content : Buffer.from(pdfResult.content);

    // 3. 转换PDF为Markdown
    let markdownText;

    try {
      // 尝试使用默认的pdf2md转换
      markdownText = await pdf2md(pdfBuffer);

      // 检查转换结果是否包含乱码
      const hasGarbledText = /[��]/.test(markdownText) || markdownText.length < 100;

      if (hasGarbledText) {
        console.warn(`PDF转换结果可能包含乱码，尝试使用备用方案: ${fileName}`);

        // 备用方案：使用pdf2md-js库
        const { parsePdf } = await import('pdf2md-js');
        const fs = await import('fs');
        const os = await import('os');
        const path = await import('path');

        // 创建临时文件
        const tempDir = path.join(os.tmpdir(), `pdf-convert-${Date.now()}`);
        await fs.promises.mkdir(tempDir, { recursive: true });
        const tempPdfPath = path.join(tempDir, fileName);
        const tempMdPath = path.join(tempDir, fileName.replace('.pdf', '.md'));

        try {
          // 写入临时PDF文件
          await fs.promises.writeFile(tempPdfPath, pdfBuffer);

          // 使用pdf2md-js转换
          await parsePdf(tempPdfPath, {
            outputDir: tempDir,
            outputFormat: 'md'
          });

          // 读取转换结果
          if (await fs.promises.access(tempMdPath).then(() => true).catch(() => false)) {
            markdownText = await fs.promises.readFile(tempMdPath, 'utf-8');
          } else {
            console.warn(`备用方案也未能生成MD文件，使用原始结果: ${fileName}`);
          }
        } finally {
          // 清理临时文件
          try {
            await fs.promises.rm(tempDir, { recursive: true, force: true });
          } catch (cleanupError) {
            console.warn('清理临时文件失败:', cleanupError);
          }
        }
      }
    } catch (conversionError) {
      console.error(`PDF转换失败: ${fileName}`, conversionError);
      throw conversionError;
    }

    // 4. 生成MD文件名
    const convertName = fileName.replace(/\.([^.]*)$/, '') + '.md';

    // 5. 将MD文件上传到OSS
    const mdBuffer = Buffer.from(markdownText, 'utf-8');
    const uploadResult = await uploadDocument(projectId, convertName, mdBuffer);

    // 返回转换后的文件信息
    return {
      success: true,
      fileName: convertName,
      fileId: uploadResult.fileId,
      filePath: uploadResult.filePath
    };
  } catch (err) {
    console.error('pdf conversion failed:', err);
    throw err;
  }
}

export default {
  defaultProcessing
};
