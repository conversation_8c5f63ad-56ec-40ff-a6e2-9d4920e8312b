-- 回滚脚本：恢复标签序号的SQL脚本
-- 仅在执行了安全版本脚本并创建了备份表后使用

-- =====================================================
-- 回滚操作：从备份表恢复数据
-- =====================================================

-- 检查备份表是否存在
SELECT 
    TABLE_NAME,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('Tags_backup', 'Questions_backup', 'Datasets_backup');

-- 开始事务
START TRANSACTION;

-- 恢复 Tags 表
UPDATE Tags t
INNER JOIN Tags_backup tb ON t.id = tb.id
SET t.label = tb.label;

-- 恢复 Questions 表
UPDATE Questions q
INNER JOIN Questions_backup qb ON q.id = qb.id
SET q.label = qb.label;

-- 恢复 Datasets 表
UPDATE Datasets d
INNER JOIN Datasets_backup db ON d.id = db.id
SET d.questionLabel = db.questionLabel;

-- 提交事务
COMMIT;

-- =====================================================
-- 验证回滚结果
-- =====================================================

-- 检查恢复后的数据
SELECT 'Tags - Records with numbers' as check_type, COUNT(*) as count
FROM Tags 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 'Questions - Records with numbers' as check_type, COUNT(*) as count
FROM Questions 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 'Datasets - Records with numbers' as check_type, COUNT(*) as count
FROM Datasets 
WHERE 
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+';

-- 显示一些示例数据确认回滚成功
SELECT 'Tags' as table_name, id, label FROM Tags 
WHERE label REGEXP '^[一二三四五六七八九十]+、|^[0-9]+' 
LIMIT 5

UNION ALL

SELECT 'Questions' as table_name, id, label FROM Questions 
WHERE label REGEXP '^[一二三四五六七八九十]+、|^[0-9]+' 
LIMIT 5

UNION ALL

SELECT 'Datasets' as table_name, id, questionLabel as label FROM Datasets 
WHERE questionLabel REGEXP '^[一二三四五六七八九十]+、|^[0-9]+' 
LIMIT 5;
