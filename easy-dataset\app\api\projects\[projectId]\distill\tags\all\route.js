import { NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { addDisplayNumbers } from '@/lib/util/tag-display';

// 设置为强制动态路由，防止静态生成
export const dynamic = 'force-dynamic';

/**
 * 获取项目的所有蒸馏标签
 */
export async function GET(request, { params }) {
  try {
    const { projectId } = params;

    // 验证项目ID
    if (!projectId) {
      return NextResponse.json({ error: '项目ID不能为空' }, { status: 400 });
    }

    // 获取所有标签的平铺数组（用于蒸馏页面）
    const tags = await db.tags.findMany({
      where: { projectId },
      orderBy: [
        { parentId: 'asc' },
        { id: 'asc' }
      ]
    });

    // 为平铺的标签数组添加显示序号信息
    const tagsWithDisplayInfo = tags.map(tag => ({
      ...tag,
      // 添加显示名称字段，用于组件中的显示
      displayName: tag.label
    }));

    return NextResponse.json(tagsWithDisplayInfo);
  } catch (error) {
    console.error('获取蒸馏标签失败:', String(error));
    return NextResponse.json({ error: error.message || '获取蒸馏标签失败' }, { status: 500 });
  }
}
