/**
 * 问题生成分析图表
 * 显示问题类型、问题复杂度和问题生成时间的分布
 */

// 1. 问题类型分布饼图
function generateQuestionCategoryData() {
    return [
        { value: 320, name: '知识性问题', itemStyle: { color: '#5470c6' } },
        { value: 240, name: '理解性问题', itemStyle: { color: '#91cc75' } },
        { value: 180, name: '应用性问题', itemStyle: { color: '#fac858' } },
        { value: 150, name: '分析性问题', itemStyle: { color: '#ee6666' } },
        { value: 100, name: '评价性问题', itemStyle: { color: '#73c0de' } },
        { value: 80, name: '创造性问题', itemStyle: { color: '#3ba272' } }
    ];
}

function initQuestionCategoryChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-category-distribution');
    if (!chartContainer) {
        console.error('找不到图表容器：question-category-distribution');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const categoryData = generateQuestionCategoryData();
    
    // 计算总数，用于显示百分比
    const total = categoryData.reduce((sum, item) => sum + item.value, 0);
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题类型分布 - 饼图 (pie)',
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                const percent = ((params.value / total) * 100).toFixed(1);
                return `${params.name}: ${params.value} (${percent}%)`;
            }
        },
        legend: {
            orient: 'horizontal',
            bottom: 10,
            left: 'center',
            itemWidth: 25,
            itemHeight: 14
        },
        series: [
            {
                name: '问题类型',
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                data: categoryData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                label: {
                    formatter: '{b}: {d}%'
                }
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 2. 问题复杂度评分分布图
function generateQuestionComplexityData() {
    // 生成1-10分的复杂度评分分布
    const data = [];
    
    // 使用正态分布生成更真实的数据
    const mean = 5.5; // 平均分
    const stdDev = 1.5; // 标准差
    const count = 1000; // 样本数量
    
    for (let i = 0; i < count; i++) {
        // 使用Box-Muller变换生成正态分布的随机数
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();
        let z = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        
        // 将正态分布的随机数转换为1-10的复杂度评分
        let score = Math.round(z * stdDev + mean);
        score = Math.max(1, Math.min(10, score)); // 限制在1-10之间
        
        data.push(score);
    }
    
    return data;
}

function initQuestionComplexityChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-complexity-distribution');
    if (!chartContainer) {
        console.error('找不到图表容器：question-complexity-distribution');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const complexityData = generateQuestionComplexityData();
    
    // 计算每个评分的数量
    const scoreCount = Array(10).fill(0);
    complexityData.forEach(score => {
        scoreCount[score - 1]++;
    });
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题复杂度评分分布 - 柱状图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                return `复杂度评分: ${params[0].name}<br/>问题数量: ${params[0].value}`;
            }
        },
        xAxis: {
            type: 'category',
            data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
            name: '复杂度评分',
            nameLocation: 'middle',
            nameGap: 30,
            axisLabel: {
                interval: 0
            }
        },
        yAxis: {
            type: 'value',
            name: '问题数量',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '问题数量',
                type: 'bar',
                data: scoreCount,
                itemStyle: {
                    color: function(params) {
                        // 根据评分设置不同的颜色
                        const colors = [
                            '#91cc75', // 1-2分：绿色
                            '#91cc75', 
                            '#fac858', // 3-5分：黄色
                            '#fac858',
                            '#fac858',
                            '#ee6666', // 6-8分：红色
                            '#ee6666',
                            '#ee6666',
                            '#73c0de', // 9-10分：蓝色
                            '#73c0de'
                        ];
                        return colors[params.dataIndex];
                    }
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 3. 问题生成时间趋势图
function generateQuestionTimeData() {
    const data = [];
    const now = new Date();
    
    // 生成过去30天的数据
    for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(now.getDate() - i);
        
        // 生成随机的问题生成时间（毫秒）
        const baseTime = 500 + Math.random() * 1000; // 基础时间：500-1500毫秒
        
        // 添加一些波动，但保持整体趋势（例如，随着时间的推移，生成时间变短）
        let trend = i / 29; // 从1到0的趋势因子
        let randomFactor = Math.random() * 0.4 - 0.2; // -0.2到0.2的随机因子
        
        // 计算最终的生成时间
        const generationTime = baseTime * (0.7 + trend * 0.5 + randomFactor);
        
        data.push({
            date: date.toISOString().split('T')[0], // 格式化为YYYY-MM-DD
            time: Math.round(generationTime)
        });
    }
    
    return data;
}

function initQuestionTimeChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-generation-time');
    if (!chartContainer) {
        console.error('找不到图表容器：question-generation-time');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const timeData = generateQuestionTimeData();
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题生成时间趋势 - 折线图 (line)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return `${params[0].axisValue}<br/>平均生成时间: ${params[0].value} 毫秒`;
            }
        },
        xAxis: {
            type: 'category',
            data: timeData.map(item => item.date),
            axisLabel: {
                rotate: 45,
                formatter: function(value) {
                    // 简化日期显示
                    return value.substring(5); // 只显示MM-DD部分
                }
            },
            boundaryGap: false
        },
        yAxis: {
            type: 'value',
            name: '生成时间 (毫秒)',
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: [
            {
                name: '生成时间',
                type: 'line',
                data: timeData.map(item => item.time),
                smooth: true,
                lineStyle: {
                    width: 3,
                    color: '#5470c6'
                },
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#5470c6'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
                        { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
                    ])
                },
                markLine: {
                    data: [
                        {
                            type: 'average',
                            name: '平均值',
                            label: {
                                position: 'middle',
                                formatter: '平均: {c} ms'
                            }
                        }
                    ],
                    symbol: ['none', 'none'],
                    lineStyle: {
                        color: '#ee6666',
                        width: 2,
                        type: 'dashed'
                    }
                }
            }
        ],
        grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 4. 问题与文本块的覆盖率分析
function generateCoverageData() {
    // 生成10个文本块的覆盖率数据
    const data = [];
    
    for (let i = 1; i <= 10; i++) {
        // 生成每个文本块的覆盖率（30%-100%之间）
        const coverage = Math.floor(Math.random() * 70) + 30;
        
        data.push({
            textBlock: `文本块 ${i}`,
            coverage: coverage
        });
    }
    
    // 按覆盖率降序排序
    return data.sort((a, b) => b.coverage - a.coverage);
}

function initCoverageChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-coverage');
    if (!chartContainer) {
        console.error('找不到图表容器：question-coverage');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const coverageData = generateCoverageData();
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题与文本块的覆盖率 - 条形图 (bar)',
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                return `${params[0].name}<br/>覆盖率: ${params[0].value}%`;
            }
        },
        xAxis: {
            type: 'value',
            name: '覆盖率 (%)',
            nameLocation: 'middle',
            nameGap: 30,
            axisLine: {
                show: true
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: 'dashed'
                }
            },
            max: 100
        },
        yAxis: {
            type: 'category',
            data: coverageData.map(item => item.textBlock),
            axisLabel: {
                fontSize: 12
            }
        },
        series: [
            {
                name: '覆盖率',
                type: 'bar',
                data: coverageData.map(item => item.coverage),
                itemStyle: {
                    color: function(params) {
                        // 根据覆盖率设置不同的颜色
                        const value = params.value;
                        if (value < 50) return '#ee6666'; // 红色
                        if (value < 75) return '#fac858'; // 黄色
                        return '#91cc75'; // 绿色
                    }
                },
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{c}%'
                }
            }
        ],
        grid: {
            left: '5%',
            right: '10%',
            bottom: '5%',
            top: '15%',
            containLabel: true
        }
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 5. 问题相似度聚类可视化
function generateSimilarityData() {
    // 生成问题相似度聚类的虚拟数据
    const data = [];
    const categories = [
        { name: '定义类问题' },
        { name: '比较类问题' },
        { name: '因果类问题' },
        { name: '过程类问题' },
        { name: '评价类问题' }
    ];
    
    // 为每个类别生成一些节点
    let nodeId = 0;
    categories.forEach((category, categoryIndex) => {
        // 每个类别生成5-10个节点
        const nodeCount = Math.floor(Math.random() * 6) + 5;
        
        // 类别中心点
        const centerX = (categoryIndex - 2) * 200;
        const centerY = (categoryIndex % 2) * 200 - 100;
        
        for (let i = 0; i < nodeCount; i++) {
            // 在类别中心周围随机分布节点
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * 50 + 20;
            const x = centerX + Math.cos(angle) * distance;
            const y = centerY + Math.sin(angle) * distance;
            
            // 添加节点
            data.push({
                id: nodeId.toString(),
                name: `问题 ${nodeId + 1}`,
                category: categoryIndex,
                x: x,
                y: y,
                symbolSize: Math.random() * 10 + 10 // 节点大小
            });
            
            nodeId++;
        }
    });
    
    // 生成节点之间的连接（边）
    const links = [];
    const nodeCount = data.length;
    
    // 每个节点连接到同一类别中的一些其他节点
    data.forEach(node => {
        const categoryNodes = data.filter(n => n.category === node.category && n.id !== node.id);
        
        // 随机连接到1-3个同类别的节点
        const linkCount = Math.floor(Math.random() * 3) + 1;
        for (let i = 0; i < linkCount && i < categoryNodes.length; i++) {
            const targetIndex = Math.floor(Math.random() * categoryNodes.length);
            const target = categoryNodes[targetIndex];
            
            // 避免重复连接
            if (!links.some(link => 
                (link.source === node.id && link.target === target.id) || 
                (link.source === target.id && link.target === node.id)
            )) {
                links.push({
                    source: node.id,
                    target: target.id,
                    value: Math.random() * 0.5 + 0.5 // 相似度：0.5-1.0
                });
            }
        }
    });
    
    return {
        nodes: data,
        links: links,
        categories: categories
    };
}

function initSimilarityChart() {
    // 获取图表容器
    const chartContainer = document.getElementById('question-similarity-clustering');
    if (!chartContainer) {
        console.error('找不到图表容器：question-similarity-clustering');
        return;
    }
    
    // 初始化ECharts实例
    const chart = echarts.init(chartContainer);
    
    // 生成数据
    const graphData = generateSimilarityData();
    
    // 配置图表选项
    const option = {
        title: {
            text: '问题相似度聚类 - 关系图 (graph)',
            left: 'center'
        },
        tooltip: {
            formatter: function(params) {
                if (params.dataType === 'node') {
                    return `${params.name}<br/>类别: ${graphData.categories[params.data.category].name}`;
                }
                if (params.dataType === 'edge') {
                    return `相似度: ${params.data.value.toFixed(2)}`;
                }
            }
        },
        legend: {
            data: graphData.categories.map(category => category.name),
            bottom: 10,
            left: 'center'
        },
        series: [
            {
                type: 'graph',
                layout: 'none', // 使用预设的坐标
                data: graphData.nodes,
                links: graphData.links,
                categories: graphData.categories,
                roam: true, // 允许缩放和平移
                label: {
                    show: false
                },
                lineStyle: {
                    color: 'source',
                    curveness: 0.3
                },
                emphasis: {
                    focus: 'adjacency', // 高亮相邻节点和边
                    lineStyle: {
                        width: 3
                    }
                }
            }
        ]
    };
    
    // 渲染图表
    chart.setOption(option);
    
    // 响应窗口大小变化，调整图表大小
    window.addEventListener('resize', function() {
        chart.resize();
    });
    
    return chart;
}

// 在文档加载完成后初始化所有图表
document.addEventListener('DOMContentLoaded', function() {
    initQuestionCategoryChart();
    initQuestionComplexityChart();
    initQuestionTimeChart();
    initCoverageChart();
    initSimilarityChart();
}); 