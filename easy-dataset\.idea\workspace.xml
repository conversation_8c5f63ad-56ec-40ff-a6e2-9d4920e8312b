<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="918246f2-9a0a-4b4f-bed9-d9a7b01a7a5c" name="更改" comment="refactor(dataset-import): 重构领域标签创建逻辑">
      <change beforePath="$PROJECT_DIR$/.env.development" beforeDir="false" afterPath="$PROJECT_DIR$/.env.development" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Dockerfile.prod" beforeDir="false" afterPath="$PROJECT_DIR$/Dockerfile.prod" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zzp9LGNinEQPILUX04ky6L8n1h" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Node.js.DatasetList.js.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;javascript.nodejs.core.library.configured.version&quot;: &quot;18.20.6&quot;,
    &quot;javascript.nodejs.core.library.typings.version&quot;: &quot;18.19.120&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/office/niuma-dataset/react-big-screen&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build:dev.executor&quot;: &quot;Debug&quot;,
    &quot;npm.dev.executor&quot;: &quot;Debug&quot;,
    &quot;npm.start:dev.executor&quot;: &quot;Debug&quot;,
    &quot;ts.external.directory.path&quot;: &quot;E:\\study\\WebStorm\\WebStorm 2024.2.1\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\office\thai\easy-dataset\components\datasets" />
      <recent name="D:\office\thai\easy-dataset\lib\services\tasks" />
      <recent name="D:\office\thai\easy-dataset\components\tasks" />
      <recent name="D:\office\thai\easy-dataset\app\api\projects\[projectId]\datasets\import" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\office\thai\easy-dataset\components\datasets" />
    </key>
  </component>
  <component name="RunManager" selected="npm.build:dev">
    <configuration name="build:dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="start:dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="start:dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build:dev" />
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.start:dev" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-7c0b70fcd90d-JavaScript-WS-242.21829.149" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="918246f2-9a0a-4b4f-bed9-d9a7b01a7a5c" name="更改" comment="" />
      <created>1752743603021</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752743603021</updated>
      <workItem from="1752743604089" duration="2795000" />
      <workItem from="1752800676494" duration="11082000" />
      <workItem from="1752818112343" duration="4669000" />
      <workItem from="1752823309837" duration="8254000" />
      <workItem from="1753059703938" duration="13832000" />
      <workItem from="1753081847090" duration="5287000" />
      <workItem from="1753146099725" duration="703000" />
      <workItem from="1753146936551" duration="2739000" />
      <workItem from="1753150155760" duration="5324000" />
      <workItem from="1753155898753" duration="1772000" />
      <workItem from="1753164077322" duration="9385000" />
      <workItem from="1753232517315" duration="1207000" />
      <workItem from="1753233783843" duration="5987000" />
      <workItem from="1753250363499" duration="8003000" />
      <workItem from="1753318513266" duration="6056000" />
      <workItem from="1753336773283" duration="9754000" />
      <workItem from="1753349689276" duration="1502000" />
      <workItem from="1753405267831" duration="8395000" />
      <workItem from="1753424760066" duration="5041000" />
      <workItem from="1753430752431" duration="5904000" />
      <workItem from="1753664769567" duration="724000" />
      <workItem from="1753667821102" duration="18444000" />
      <workItem from="1753751246616" duration="8398000" />
      <workItem from="1753768843717" duration="2120000" />
      <workItem from="1753771854221" duration="4583000" />
      <workItem from="1753777462734" duration="4911000" />
      <workItem from="1753837432805" duration="7284000" />
      <workItem from="1753855195782" duration="10896000" />
      <workItem from="1753923724941" duration="6552000" />
      <workItem from="1753941745174" duration="13636000" />
      <workItem from="1754010178357" duration="120000" />
      <workItem from="1754292137720" duration="3844000" />
      <workItem from="1754383431301" duration="631000" />
      <workItem from="1754442061105" duration="4889000" />
      <workItem from="1754449220675" duration="3471000" />
      <workItem from="1754460125599" duration="2636000" />
      <workItem from="1754464603387" duration="7428000" />
      <workItem from="1754528518981" duration="7014000" />
      <workItem from="1754546492551" duration="10004000" />
    </task>
    <task id="LOCAL-00030" summary="refactor(big-screen): 左侧大屏数据展示修改">
      <option name="closed" value="true" />
      <created>1753338346338</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753338346338</updated>
    </task>
    <task id="LOCAL-00031" summary="feat(big-screen): 实现大屏页面左下角数据动态加载">
      <option name="closed" value="true" />
      <created>1753339532383</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753339532383</updated>
    </task>
    <task id="LOCAL-00032" summary="feat(big-screen): 地图展示真实统计数据">
      <option name="closed" value="true" />
      <created>1753340616271</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1753340616271</updated>
    </task>
    <task id="LOCAL-00033" summary="feat(big-screen): 左侧页面数据使用月度时间轴">
      <option name="closed" value="true" />
      <created>1753341327352</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1753341327352</updated>
    </task>
    <task id="LOCAL-00034" summary="feat(big-screen): 更新右侧页面数据上半部分展示逻辑">
      <option name="closed" value="true" />
      <created>1753345354168</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1753345354168</updated>
    </task>
    <task id="LOCAL-00035" summary="feat(big-screen): 增加离线门户数据的时间序列趋势">
      <option name="closed" value="true" />
      <created>1753346741153</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1753346741153</updated>
    </task>
    <task id="LOCAL-00036" summary="fix(big-screen): 修复大屏展示初始化数据及图表问题">
      <option name="closed" value="true" />
      <created>1753348257608</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1753348257608</updated>
    </task>
    <task id="LOCAL-00037" summary="refactor(api): 项目统计信息接口更新路由路径">
      <option name="closed" value="true" />
      <created>1753350414848</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1753350414848</updated>
    </task>
    <task id="LOCAL-00038" summary="refactor(llm): 优化 LLM 输出处理和错误处理">
      <option name="closed" value="true" />
      <created>1753413081426</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1753413081426</updated>
    </task>
    <task id="LOCAL-00039" summary="refactor(api): 优化项目不存在时的错误提示信息">
      <option name="closed" value="true" />
      <created>1753426332818</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1753426332818</updated>
    </task>
    <task id="LOCAL-00040" summary="refactor(api): 优化项目不存在时的错误提示信息">
      <option name="closed" value="true" />
      <created>1753427725780</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1753427725780</updated>
    </task>
    <task id="LOCAL-00041" summary="refactor(api): 优化项目不存在时的错误提示信息">
      <option name="closed" value="true" />
      <created>1753430209318</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1753430209318</updated>
    </task>
    <task id="LOCAL-00042" summary="feat(file): 添加文件记录功能- 新增文件记录操作统计">
      <option name="closed" value="true" />
      <created>1753684034034</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1753684034034</updated>
    </task>
    <task id="LOCAL-00043" summary="feat(datasets): 增加数据导入功能支持多种格式">
      <option name="closed" value="true" />
      <created>1753687144602</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1753687144602</updated>
    </task>
    <task id="LOCAL-00044" summary="refactor: 更新所有应用图标">
      <option name="closed" value="true" />
      <created>1753687431803</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1753687431803</updated>
    </task>
    <task id="LOCAL-00045" summary="feat(centerPage): 实现底部大屏数据部分真实统计">
      <option name="closed" value="true" />
      <created>1753690310798</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1753690310798</updated>
    </task>
    <task id="LOCAL-00046" summary="feat(statistics): 底部数据全部采用真实数据">
      <option name="closed" value="true" />
      <created>1753692473182</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1753692473182</updated>
    </task>
    <task id="LOCAL-00047" summary="refactor(big-screen): 重构大屏展示页面，将首页替换为大屏页面&#10;- 修改页面文本词">
      <option name="closed" value="true" />
      <created>1753695023509</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1753695023509</updated>
    </task>
    <task id="LOCAL-00048" summary="feat(big-screen): 实现左页上半部分真实数据展示">
      <option name="closed" value="true" />
      <created>1753754799765</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1753754799765</updated>
    </task>
    <task id="LOCAL-00049" summary="feat(big-screen): 领域分布统计真实数据">
      <option name="closed" value="true" />
      <created>1753756337151</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1753756337151</updated>
    </task>
    <task id="LOCAL-00050" summary="feat(big-screen): 数据统计概览功能曲线图真实数据">
      <option name="closed" value="true" />
      <created>1753757480607</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1753757480608</updated>
    </task>
    <task id="LOCAL-00051" summary="refactor(big-screen): 移除假设性问题数量计算，改为基于实际数据">
      <option name="closed" value="true" />
      <created>1753758099243</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1753758099243</updated>
    </task>
    <task id="LOCAL-00052" summary="refactor(big-screen): 移除假数据调用">
      <option name="closed" value="true" />
      <created>1753759952721</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1753759952722</updated>
    </task>
    <task id="LOCAL-00053" summary="feat(big-screen): 大屏组件集成数据缓存服务">
      <option name="closed" value="true" />
      <created>1753770546845</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1753770546845</updated>
    </task>
    <task id="LOCAL-00054" summary="feat(big-screen): 调整地图缩放比例限制和显示国家名称">
      <option name="closed" value="true" />
      <created>1753774676329</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1753774676330</updated>
    </task>
    <task id="LOCAL-00055" summary="refactor(big-screen): 调整地图中心点和缩放级别">
      <option name="closed" value="true" />
      <created>1753775291823</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1753775291823</updated>
    </task>
    <task id="LOCAL-00056" summary="refactor(big-screen): 去掉选中地图后顶部的国家名称">
      <option name="closed" value="true" />
      <created>1753776456114</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1753776456114</updated>
    </task>
    <task id="LOCAL-00057" summary="feat(oss): 实现阿里云OSS文件存储服务">
      <option name="closed" value="true" />
      <created>1753856929662</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1753856929662</updated>
    </task>
    <task id="LOCAL-00058" summary="feat(file-process): 修复文件分割和 Markdown 处理的相关问题">
      <option name="closed" value="true" />
      <created>1753927274632</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1753927274633</updated>
    </task>
    <task id="LOCAL-00059" summary="feat:将图片资源迁移至阿里云 OSS">
      <option name="closed" value="true" />
      <created>1753942324633</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1753942324633</updated>
    </task>
    <task id="LOCAL-00060" summary="refactor(big-screen): 优化右屏图表数据数据为空时的展示逻辑">
      <option name="closed" value="true" />
      <created>1753944926069</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1753944926069</updated>
    </task>
    <task id="LOCAL-00061" summary="refactor(big-screen): 修改流量峰值为数据峰值">
      <option name="closed" value="true" />
      <created>1753947331961</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1753947331961</updated>
    </task>
    <task id="LOCAL-00062" summary="refactor(big-screen): 优化项目数据表格展示效果">
      <option name="closed" value="true" />
      <created>1753948120532</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1753948120533</updated>
    </task>
    <task id="LOCAL-00063" summary="feat(leftPage): 优化用户态势图表展示逻辑&#10;- 增加对选中国家但无数据情况的处理，显示暂无数据&#10;- 对数据集按时间降序排序，取最新20条数据展示&#10;- 调整图表显示行数为10行">
      <option name="closed" value="true" />
      <created>1753950231639</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1753950231639</updated>
    </task>
    <task id="LOCAL-00064" summary="refactor(big-screen): 禁用拖动平移功能">
      <option name="closed" value="true" />
      <created>1753950926402</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1753950926402</updated>
    </task>
    <task id="LOCAL-00065" summary="feat(big-screen): 优化屏幕适配策略以支持更多设备类型">
      <option name="closed" value="true" />
      <created>1753952128917</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1753952128917</updated>
    </task>
    <task id="LOCAL-00066" summary="style(big-screen): 调整左右区域底部样式以与中间区域对齐">
      <option name="closed" value="true" />
      <created>1753952534203</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1753952534203</updated>
    </task>
    <task id="LOCAL-00067" summary="feat(big-screen): 实现地图组件的自定义缩放功能">
      <option name="closed" value="true" />
      <created>1753954265735</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1753954265735</updated>
    </task>
    <task id="LOCAL-00068" summary="actorref(big-screen): 优化布局稳定性">
      <option name="closed" value="true" />
      <created>1754451581514</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1754451581515</updated>
    </task>
    <task id="LOCAL-00069" summary="actorref(big-screen): 调整地图初始化比例">
      <option name="closed" value="true" />
      <created>1754452448875</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1754452448875</updated>
    </task>
    <task id="LOCAL-00070" summary="style(big-screen): 调整中心页面组件样式">
      <option name="closed" value="true" />
      <created>1754462210814</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1754462210816</updated>
    </task>
    <task id="LOCAL-00071" summary="refactor(flexible): 移除屏幕识别逻辑，统一适配参数">
      <option name="closed" value="true" />
      <created>1754470305012</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1754470305012</updated>
    </task>
    <task id="LOCAL-00072" summary="refactor(layout): 优化大屏布局方案,去掉底部滚动条">
      <option name="closed" value="true" />
      <created>1754535833076</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1754535833077</updated>
    </task>
    <task id="LOCAL-00073" summary="feat(datasets): 实现数据集取消确认功能">
      <option name="closed" value="true" />
      <created>1754536817203</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1754536817203</updated>
    </task>
    <task id="LOCAL-00074" summary="feat(tag): 优化标签展示和匹配逻辑">
      <option name="closed" value="true" />
      <created>1754548206591</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1754548206592</updated>
    </task>
    <task id="LOCAL-00075" summary="feat(tag): 修改标签数据获取接口，移除冗余的序号生成逻辑">
      <option name="closed" value="true" />
      <created>1754549817038</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1754549817038</updated>
    </task>
    <task id="LOCAL-00076" summary="refactor(distill): 重构标签处理逻辑">
      <option name="closed" value="true" />
      <created>1754553174016</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1754553174016</updated>
    </task>
    <task id="LOCAL-00077" summary="feat(big-screen): 优化顶部标题和底部界面展示">
      <option name="closed" value="true" />
      <created>1754555194915</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1754555194915</updated>
    </task>
    <task id="LOCAL-00078" summary="refactor(dataset-import): 重构领域标签创建逻辑">
      <option name="closed" value="true" />
      <created>1754556343125</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1754556343125</updated>
    </task>
    <option name="localTasksCounter" value="79" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat(big-screen): 调整地图缩放比例限制和显示国家名称" />
    <MESSAGE value="refactor(big-screen): 调整地图中心点和缩放级别" />
    <MESSAGE value="refactor(big-screen): 去掉选中地图后顶部的国家名称" />
    <MESSAGE value="feat(oss): 实现阿里云OSS文件存储服务" />
    <MESSAGE value="feat(file-process): 修复文件分割和 Markdown 处理的相关问题" />
    <MESSAGE value="feat:将图片资源迁移至阿里云 OSS" />
    <MESSAGE value="refactor(big-screen): 优化右屏图表数据数据为空时的展示逻辑" />
    <MESSAGE value="refactor(big-screen): 修改流量峰值为数据峰值" />
    <MESSAGE value="refactor(big-screen): 优化项目数据表格展示效果" />
    <MESSAGE value="feat(leftPage): 优化用户态势图表展示逻辑&#10;- 增加对选中国家但无数据情况的处理，显示暂无数据&#10;- 对数据集按时间降序排序，取最新20条数据展示&#10;- 调整图表显示行数为10行" />
    <MESSAGE value="refactor(big-screen): 禁用拖动平移功能" />
    <MESSAGE value="feat(big-screen): 优化屏幕适配策略以支持更多设备类型" />
    <MESSAGE value="style(big-screen): 调整左右区域底部样式以与中间区域对齐" />
    <MESSAGE value="feat(big-screen): 实现地图组件的自定义缩放功能" />
    <MESSAGE value="actorref(big-screen): 优化布局稳定性" />
    <MESSAGE value="actorref(big-screen): 调整地图初始化比例" />
    <MESSAGE value="style(big-screen): 调整中心页面组件样式" />
    <MESSAGE value="refactor(flexible): 移除屏幕识别逻辑，统一适配参数" />
    <MESSAGE value="refactor(layout): 优化大屏布局方案,去掉底部滚动条" />
    <MESSAGE value="feat(datasets): 实现数据集取消确认功能" />
    <MESSAGE value="feat(tag): 优化标签展示和匹配逻辑" />
    <MESSAGE value="feat(tag): 修改标签数据获取接口，移除冗余的序号生成逻辑" />
    <MESSAGE value="refactor(distill): 重构标签处理逻辑" />
    <MESSAGE value="feat(big-screen): 优化顶部标题和底部界面展示" />
    <MESSAGE value="refactor(dataset-import): 重构领域标签创建逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor(dataset-import): 重构领域标签创建逻辑" />
  </component>
</project>