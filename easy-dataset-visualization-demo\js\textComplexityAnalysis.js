/**
 * 文本复杂度分析图表
 * 分析文本复杂度，包括句子长度分布和词汇丰富度
 */

// 生成虚拟的文本复杂度数据
function generateTextComplexityData() {
  // 生成句子长度分布数据
  const sentenceLengthData = [];
  // 假设我们分析了100个文本块
  const textBlockCount = 100;
  
  // 创建句子长度的分布（字数范围从5到60）
  for (let length = 5; length <= 60; length += 5) {
    // 使用正态分布模拟句子长度分布，中心在25-30字左右
    let frequency;
    if (length <= 25) {
      frequency = Math.round(textBlockCount * 0.1 * (length / 25));
    } else if (length <= 35) {
      frequency = Math.round(textBlockCount * 0.2);
    } else {
      frequency = Math.round(textBlockCount * 0.2 * (1 - (length - 35) / 25));
    }
    
    // 添加一些随机波动
    frequency = Math.max(1, Math.round(frequency * (0.8 + Math.random() * 0.4)));
    
    sentenceLengthData.push({
      length: length,
      frequency: frequency,
      percentage: (frequency / textBlockCount * 100).toFixed(1)
    });
  }
  
  // 生成词汇丰富度数据
  // 假设我们有10个不同的文本类别
  const categories = [
    '技术文档', '新闻报道', '学术论文', 
    '科普文章', '故事小说', '法律文件', 
    '教育资源', '产品说明', '博客文章', '社交媒体'
  ];
  
  // 为每个类别生成词汇丰富度指标
  const vocabularyRichnessData = categories.map(category => {
    // 词汇丰富度指标：TTR (Type-Token Ratio)，介于0.3-0.8之间
    // 不同类型的文本有不同的基础TTR
    let baseTTR;
    switch(category) {
      case '学术论文': baseTTR = 0.7; break;
      case '技术文档': baseTTR = 0.65; break;
      case '法律文件': baseTTR = 0.6; break;
      case '科普文章': baseTTR = 0.55; break;
      case '教育资源': baseTTR = 0.5; break;
      case '新闻报道': baseTTR = 0.45; break;
      case '博客文章': baseTTR = 0.45; break;
      case '故事小说': baseTTR = 0.4; break;
      case '产品说明': baseTTR = 0.35; break;
      case '社交媒体': baseTTR = 0.3; break;
      default: baseTTR = 0.5;
    }
    
    // 添加随机波动
    const ttr = Math.min(0.8, Math.max(0.3, baseTTR + (Math.random() - 0.5) * 0.1));
    
    // 生成其他复杂度指标
    // 平均句长（字数）
    const avgSentenceLength = Math.round(15 + Math.random() * 20);
    
    // 平均段落句子数
    const avgSentencesPerParagraph = Math.round(3 + Math.random() * 5);
    
    // 复杂句子比例（含有从句或多个谓语的句子）
    const complexSentenceRatio = Math.min(0.9, Math.max(0.1, 0.3 + (Math.random() - 0.5) * 0.4));
    
    // 可读性分数（1-10，越高越容易阅读）
    const readabilityScore = Math.min(10, Math.max(1, 5 + (Math.random() - 0.5) * 6));
    
    return {
      category: category,
      ttr: parseFloat(ttr.toFixed(2)),
      avgSentenceLength: avgSentenceLength,
      avgSentencesPerParagraph: avgSentencesPerParagraph,
      complexSentenceRatio: parseFloat(complexSentenceRatio.toFixed(2)),
      readabilityScore: parseFloat(readabilityScore.toFixed(1))
    };
  });
  
  // 按TTR值排序
  vocabularyRichnessData.sort((a, b) => b.ttr - a.ttr);
  
  return {
    sentenceLengthData: sentenceLengthData,
    vocabularyRichnessData: vocabularyRichnessData
  };
}

// 初始化句子长度分布图表
function initSentenceLengthChart() {
  const { sentenceLengthData } = generateTextComplexityData();
  
  // 获取图表容器
  const chartDom = document.getElementById('text-complexity-analysis');
  const chart = echarts.init(chartDom);
  
  // 准备数据
  const xAxisData = sentenceLengthData.map(item => item.length);
  const seriesData = sentenceLengthData.map(item => item.frequency);
  
  // 配置图表选项
  const option = {
    title: {
      text: '句子长度分布 - 柱状图 (bar)',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        const data = params[0];
        const item = sentenceLengthData[data.dataIndex];
        return `句子长度: ${item.length}字<br/>频次: ${item.frequency}<br/>百分比: ${item.percentage}%`;
      }
    },
    toolbox: {
      feature: {
        dataZoom: {},
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      name: '句子长度（字数）',
      data: xAxisData,
      axisLabel: {
        interval: 1
      }
    },
    yAxis: {
      type: 'value',
      name: '频次'
    },
    series: [
      {
        name: '句子长度分布',
        type: 'bar',
        data: seriesData,
        itemStyle: {
          color: function(params) {
            // 根据句子长度设置不同颜色
            const length = sentenceLengthData[params.dataIndex].length;
            if (length <= 15) return '#91cc75'; // 短句
            if (length <= 30) return '#fac858'; // 中等句子
            return '#ee6666'; // 长句
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      },
      {
        name: '分布趋势',
        type: 'line',
        smooth: true,
        data: seriesData,
        symbolSize: 0,
        lineStyle: {
          width: 2,
          color: '#5470c6'
        },
        itemStyle: {
          color: '#5470c6'
        }
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化词汇丰富度雷达图
function initVocabularyRichnessChart() {
  const { vocabularyRichnessData } = generateTextComplexityData();
  
  // 获取图表容器
  const chartDom = document.getElementById('vocabulary-richness-chart');
  const chart = echarts.init(chartDom);
  
  // 准备雷达图数据
  const categories = vocabularyRichnessData.map(item => item.category);
  const ttrData = vocabularyRichnessData.map(item => item.ttr);
  
  // 配置图表选项
  const option = {
    title: {
      text: '词汇丰富度分析 - 柱状图',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    toolbox: {
      feature: {
        dataZoom: {},
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        interval: 0,
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: 'TTR值',
      min: 0,
      max: 0.8,
      interval: 0.1
    },
    series: [
      {
        name: '词汇丰富度 (TTR)',
        type: 'bar',
        data: ttrData,
        itemStyle: {
          color: function(params) {
            // 根据TTR值设置不同颜色
            const ttr = vocabularyRichnessData[params.dataIndex].ttr;
            if (ttr >= 0.6) return '#91cc75'; // 高词汇丰富度
            if (ttr >= 0.4) return '#fac858'; // 中等词汇丰富度
            return '#ee6666'; // 低词汇丰富度
          }
        },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}'
        }
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化文本复杂度综合评分雷达图
function initTextComplexityRadarChart() {
  const { vocabularyRichnessData } = generateTextComplexityData();
  
  // 选择前5个类别进行雷达图比较
  const selectedCategories = vocabularyRichnessData.slice(0, 5);
  
  // 获取图表容器
  const chartDom = document.getElementById('text-complexity-radar');
  const chart = echarts.init(chartDom);
  
  // 定义雷达图的指标维度
  const indicators = [
    { name: '词汇丰富度', max: 0.8 },
    { name: '平均句长', max: 40 },
    { name: '段落复杂度', max: 10 },
    { name: '复杂句比例', max: 1 },
    { name: '可读性', max: 10 }
  ];
  
  // 准备雷达图数据
  const seriesData = selectedCategories.map(item => {
    return {
      value: [
        item.ttr,
        item.avgSentenceLength / 40, // 归一化
        item.avgSentencesPerParagraph / 10, // 归一化
        item.complexSentenceRatio,
        item.readabilityScore / 10 // 归一化
      ],
      name: item.category
    };
  });
  
  // 配置图表选项
  const option = {
    title: {
      text: '文本复杂度多维度分析 - 雷达图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      data: selectedCategories.map(item => item.category),
      bottom: 0
    },
    radar: {
      indicator: indicators,
      center: ['50%', '50%'],
      radius: '65%'
    },
    series: [{
      type: 'radar',
      data: seriesData
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
  initSentenceLengthChart();
  initVocabularyRichnessChart();
  initTextComplexityRadarChart();
}); 