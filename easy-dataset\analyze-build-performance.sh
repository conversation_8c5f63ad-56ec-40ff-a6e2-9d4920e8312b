#!/bin/bash

# Docker构建性能分析脚本
# 分析构建过程中的时间消耗和优化建议

set -e

echo "🔍 Docker构建性能分析工具"
echo "================================"

# 检查Docker版本和BuildKit支持
echo "📋 环境检查:"
echo "Docker版本: $(docker --version)"
echo "BuildKit状态: ${DOCKER_BUILDKIT:-未启用}"

# 分析构建上下文大小
echo ""
echo "📊 构建上下文分析:"
echo "正在计算构建上下文大小..."

# 创建临时tar文件来模拟Docker构建上下文
temp_tar=$(mktemp)
tar -czf "$temp_tar" --exclude-from=.dockerignore . 2>/dev/null || tar -czf "$temp_tar" .

context_size=$(du -h "$temp_tar" | cut -f1)
context_size_bytes=$(stat -f%z "$temp_tar" 2>/dev/null || stat -c%s "$temp_tar")

echo "构建上下文大小: $context_size"
rm "$temp_tar"

# 分析大文件
echo ""
echo "📁 大文件分析 (>10MB):"
find . -type f -size +10M -not -path "./node_modules/*" -not -path "./.next/*" -not -path "./.git/*" | head -10 | while read -r file; do
    size=$(du -h "$file" | cut -f1)
    echo "  $size - $file"
done

# 分析目录大小
echo ""
echo "📂 主要目录大小:"
for dir in node_modules .next public prisma app components; do
    if [ -d "$dir" ]; then
        size=$(du -sh "$dir" 2>/dev/null | cut -f1)
        echo "  $size - $dir/"
    fi
done

# 检查.dockerignore文件
echo ""
echo "🚫 .dockerignore 检查:"
if [ -f ".dockerignore" ]; then
    echo "✅ .dockerignore 文件存在"
    ignored_count=$(wc -l < .dockerignore)
    echo "   包含 $ignored_count 条忽略规则"
else
    echo "❌ .dockerignore 文件不存在 - 这会显著增加构建时间！"
fi

# 分析依赖
echo ""
echo "📦 依赖分析:"
if [ -f "package.json" ]; then
    deps_count=$(jq '.dependencies | length' package.json 2>/dev/null || echo "无法解析")
    dev_deps_count=$(jq '.devDependencies | length' package.json 2>/dev/null || echo "无法解析")
    echo "生产依赖: $deps_count"
    echo "开发依赖: $dev_deps_count"
fi

# 性能建议
echo ""
echo "💡 优化建议:"
echo "================================"

if [ ! -f ".dockerignore" ]; then
    echo "🔥 高优先级: 创建 .dockerignore 文件"
    echo "   - 排除 node_modules, .next, .git 等目录"
    echo "   - 预计可减少构建时间 60-80%"
fi

if [ "$context_size_bytes" -gt 1073741824 ]; then  # 1GB
    echo "🔥 高优先级: 构建上下文过大 ($context_size)"
    echo "   - 检查并排除不必要的大文件"
    echo "   - 使用 .dockerignore 排除缓存目录"
fi

echo "🔧 中等优先级: 使用多阶段构建优化"
echo "   - 分离依赖安装和应用构建"
echo "   - 使用专门的生产依赖阶段"

echo "⚡ 低优先级: 启用构建缓存"
echo "   - 使用 DOCKER_BUILDKIT=1"
echo "   - 配置远程缓存 (如果使用CI/CD)"

# 估算优化效果
echo ""
echo "📈 预期优化效果:"
echo "================================"
if [ ! -f ".dockerignore" ]; then
    echo "添加 .dockerignore: 减少 60-80% 构建时间"
fi
echo "多阶段构建优化: 减少 20-40% 构建时间"
echo "启用 BuildKit: 减少 10-20% 构建时间"
echo "使用构建缓存: 减少 50-90% 重复构建时间"

echo ""
echo "🎯 建议的构建命令:"
echo "DOCKER_BUILDKIT=1 docker build -f Dockerfile.optimized -t easy-dataset:optimized ."

echo ""
echo "✅ 分析完成！"
