/**
 * 文本块主题聚类 (t-SNE) 可视化
 * 使用t-SNE算法将文本块嵌入可视化为2D空间中的聚类
 */

// 生成模拟的文本块嵌入数据
function generateTextEmbeddingData() {
  // 定义不同的主题类别
  const topics = [
    { name: '技术文档', color: '#5470c6' },
    { name: '科学研究', color: '#91cc75' },
    { name: '教育资源', color: '#fac858' },
    { name: '新闻报道', color: '#ee6666' },
    { name: '产品说明', color: '#73c0de' },
    { name: '学术论文', color: '#3ba272' }
  ];
  
  // 为每个主题生成多个文本块的嵌入点
  const data = [];
  let id = 1;
  
  topics.forEach((topic, topicIndex) => {
    // 为每个主题生成一个中心点
    const centerX = (Math.random() * 2 - 1) * 8;
    const centerY = (Math.random() * 2 - 1) * 8;
    
    // 每个主题生成10-25个文本块
    const count = Math.floor(Math.random() * 15) + 10;
    
    for (let i = 0; i < count; i++) {
      // 在中心点周围生成散点，模拟t-SNE聚类效果
      // 使用正态分布使点更集中在中心附近
      const angle = Math.random() * Math.PI * 2;
      const radius = Math.random() * Math.random() * 2; // 使用二次方使点更集中
      
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      // 生成文本块的模拟数据
      const textLength = Math.floor(Math.random() * 300) + 100; // 100-400字符
      const wordCount = Math.floor(textLength / 5); // 假设平均每个词5个字符
      
      data.push({
        id: id++,
        x: x,
        y: y,
        topic: topic.name,
        topicId: topicIndex,
        color: topic.color,
        textLength: textLength,
        wordCount: wordCount,
        // 模拟文本块的前20个字符
        preview: `文本块#${id} (${topic.name})...`
      });
    }
  });
  
  return {
    topics: topics,
    data: data
  };
}

// 初始化t-SNE聚类可视化
function initTextClusteringChart() {
  const { topics, data } = generateTextEmbeddingData();
  
  // 获取图表容器
  const chartDom = document.getElementById('text-theme-clustering');
  const chart = echarts.init(chartDom);
  
  // 准备系列数据，每个主题一个系列
  const series = topics.map((topic, index) => {
    const topicData = data.filter(item => item.topicId === index);
    return {
      name: topic.name,
      type: 'scatter',
      data: topicData.map(item => [item.x, item.y, item.textLength / 50]),
      symbolSize: function(val) {
        return val[2]; // 使用文本长度作为点的大小
      },
      itemStyle: {
        color: topic.color
      }
    };
  });
  
  // 配置图表选项
  const option = {
    title: {
      text: '文本块主题聚类可视化 - t-SNE散点图 (scatter)',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const dataIndex = params.dataIndex;
        const topicData = data.filter(item => item.topicId === params.seriesIndex);
        const item = topicData[dataIndex];
        return `<div>
          <b>${item.preview}</b><br/>
          主题: ${item.topic}<br/>
          文本长度: ${item.textLength} 字符<br/>
          词数: ${item.wordCount}
        </div>`;
      }
    },
    legend: {
      data: topics.map(topic => topic.name),
      bottom: 10
    },
    toolbox: {
      feature: {
        dataZoom: {},
        restore: {},
        saveAsImage: {}
      }
    },
    grid: {
      top: '10%',
      bottom: '15%',
      left: '3%',
      right: '7%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: 't-SNE维度1',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: 't-SNE维度2',
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    series: series
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 添加点击事件，模拟查看文本块详情
  chart.on('click', function(params) {
    const dataIndex = params.dataIndex;
    const topicData = data.filter(item => item.topicId === params.seriesIndex);
    const item = topicData[dataIndex];
    console.log('点击文本块:', item);
    // 这里可以添加弹窗显示文本块详情的代码
  });
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化文本块相似度网络图
function initTextSimilarityNetwork() {
  const { topics, data } = generateTextEmbeddingData();
  
  // 获取图表容器
  const chartDom = document.getElementById('text-similarity-network');
  const chart = echarts.init(chartDom);
  
  // 准备节点数据
  const nodes = data.map(item => ({
    id: item.id.toString(),
    name: item.preview,
    symbolSize: item.textLength / 50,
    value: item.wordCount,
    category: item.topicId,
    x: item.x * 10,
    y: item.y * 10,
    itemStyle: {
      color: item.color
    }
  }));
  
  // 准备边数据（连接相似的文本块）
  const edges = [];
  // 为每个节点创建2-4个连接到同主题其他节点的边
  nodes.forEach(node => {
    const sameCategory = nodes.filter(n => 
      n.category === node.category && n.id !== node.id
    );
    
    // 随机选择2-4个同类节点创建连接
    const connectionCount = Math.floor(Math.random() * 3) + 2;
    const shuffled = sameCategory.sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, Math.min(connectionCount, sameCategory.length));
    
    selected.forEach(target => {
      // 计算相似度（0.5-1.0之间的随机值）
      const similarity = 0.5 + Math.random() * 0.5;
      
      // 只添加相似度大于0.7的连接，避免图表过于复杂
      if (similarity > 0.7) {
        edges.push({
          source: node.id,
          target: target.id,
          value: similarity.toFixed(2),
          lineStyle: {
            width: similarity * 3,
            opacity: similarity * 0.7
          }
        });
      }
    });
  });
  
  // 配置图表选项
  const option = {
    title: {
      text: '文本块相似度 - 网络图 (graph)',
      left: 'center'
    },
    tooltip: {
      formatter: function(params) {
        if (params.dataType === 'node') {
          return `<div>
            <b>${params.data.name}</b><br/>
            主题: ${topics[params.data.category].name}<br/>
            词数: ${params.data.value}
          </div>`;
        } else {
          return `相似度: ${params.data.value}`;
        }
      }
    },
    legend: {
      data: topics.map(topic => topic.name),
      bottom: 10
    },
    toolbox: {
      feature: {
        dataZoom: {},
        restore: {},
        saveAsImage: {}
      }
    },
    series: [{
      type: 'graph',
      layout: 'none', // 使用预先计算的位置
      data: nodes,
      links: edges,
      categories: topics.map(topic => ({
        name: topic.name
      })),
      roam: true,
      label: {
        show: false
      },
      emphasis: {
        focus: 'adjacency',
        lineStyle: {
          width: 5
        }
      }
    }]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
  initTextClusteringChart();
  initTextSimilarityNetwork();
}); 