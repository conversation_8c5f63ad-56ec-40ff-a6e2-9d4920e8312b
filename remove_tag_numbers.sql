-- SQL脚本：去除datasets表、tags表、questions表中标签的序号
-- 执行前请备份数据库！

-- =====================================================
-- 1. 更新 Tags 表中的 label 字段，去除序号前缀
-- =====================================================

-- 去除中文数字序号前缀（如：一、二、三、等）
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE label REGEXP '^[一二三四五六七八九十]+、';

-- 去除阿拉伯数字序号前缀（如：1、2、3、等）
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE label REGEXP '^[0-9]+、';

-- 去除小数点格式序号前缀（如：1.1、1.2、2.1、等）
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\.[0-9]+\\s+';

-- 去除纯数字序号前缀（如：1 、2 、3 、等，后面跟空格）
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\s+';

-- 去除"领域"后缀（可选，如果不需要可以注释掉）
-- UPDATE Tags 
-- SET label = TRIM(REGEXP_REPLACE(label, '领域$', ''))
-- WHERE label LIKE '%领域';

-- =====================================================
-- 2. 更新 Questions 表中的 label 字段，去除序号前缀
-- =====================================================

-- 去除中文数字序号前缀
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE label REGEXP '^[一二三四五六七八九十]+、';

-- 去除阿拉伯数字序号前缀
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE label REGEXP '^[0-9]+、';

-- 去除小数点格式序号前缀
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\.[0-9]+\\s+';

-- 去除纯数字序号前缀
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\s+';

-- =====================================================
-- 3. 更新 Datasets 表中的 questionLabel 字段，去除序号前缀
-- =====================================================

-- 去除中文数字序号前缀
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE questionLabel REGEXP '^[一二三四五六七八九十]+、';

-- 去除阿拉伯数字序号前缀
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+、\\s*', ''))
WHERE questionLabel REGEXP '^[0-9]+、';

-- 去除小数点格式序号前缀
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+';

-- 去除纯数字序号前缀
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\s+', ''))
WHERE questionLabel REGEXP '^[0-9]+\\s+';

-- =====================================================
-- 4. 验证更新结果的查询语句
-- =====================================================

-- 查看 Tags 表更新后的结果
SELECT 
    id, 
    label, 
    parentId,
    projectId
FROM Tags 
ORDER BY projectId, parentId, label;

-- 查看 Questions 表更新后的结果（显示前10条）
SELECT 
    id, 
    question, 
    label, 
    projectId
FROM Questions 
ORDER BY createAt DESC 
LIMIT 10;

-- 查看 Datasets 表更新后的结果（显示前10条）
SELECT 
    id, 
    question, 
    questionLabel, 
    projectId
FROM Datasets 
ORDER BY createAt DESC 
LIMIT 10;

-- =====================================================
-- 5. 检查是否还有遗漏的序号格式
-- =====================================================

-- 检查 Tags 表中是否还有序号格式的标签
SELECT 
    id, 
    label, 
    projectId
FROM Tags 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
ORDER BY projectId, label;

-- 检查 Questions 表中是否还有序号格式的标签
SELECT 
    id, 
    label, 
    projectId
FROM Questions 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
ORDER BY projectId, label
LIMIT 20;

-- 检查 Datasets 表中是否还有序号格式的标签
SELECT 
    id, 
    questionLabel, 
    projectId
FROM Datasets 
WHERE 
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+'
ORDER BY projectId, questionLabel
LIMIT 20;

-- =====================================================
-- 6. 统计更新影响的记录数
-- =====================================================

-- 统计各表中标签的数量
SELECT 
    'Tags' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT label) as unique_labels
FROM Tags
UNION ALL
SELECT 
    'Questions' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT label) as unique_labels
FROM Questions
UNION ALL
SELECT 
    'Datasets' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT questionLabel) as unique_labels
FROM Datasets;
