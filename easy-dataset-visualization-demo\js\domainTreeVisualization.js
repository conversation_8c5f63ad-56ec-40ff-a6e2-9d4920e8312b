/**
 * 领域树可视化
 * 使用树状图可视化领域树结构
 */

// 生成虚拟的领域树数据
function generateDomainTreeData() {
  // 创建一个示例领域树结构
  const treeData = {
    name: '知识领域',
    children: [
      {
        name: '计算机科学',
        value: 1200,
        children: [
          {
            name: '编程语言',
            value: 450,
            children: [
              { name: 'JavaScript', value: 120 },
              { name: 'Python', value: 150 },
              { name: 'Java', value: 100 },
              { name: 'C++', value: 80 }
            ]
          },
          {
            name: '人工智能',
            value: 350,
            children: [
              { name: '机器学习', value: 150 },
              { name: '深度学习', value: 120 },
              { name: '自然语言处理', value: 80 }
            ]
          },
          {
            name: '数据库',
            value: 200,
            children: [
              { name: '关系型数据库', value: 100 },
              { name: 'NoSQL', value: 100 }
            ]
          },
          {
            name: '网络安全',
            value: 200
          }
        ]
      },
      {
        name: '数学',
        value: 800,
        children: [
          {
            name: '代数',
            value: 200,
            children: [
              { name: '线性代数', value: 120 },
              { name: '抽象代数', value: 80 }
            ]
          },
          {
            name: '几何',
            value: 180,
            children: [
              { name: '欧几里得几何', value: 80 },
              { name: '微分几何', value: 100 }
            ]
          },
          {
            name: '统计学',
            value: 220,
            children: [
              { name: '描述统计学', value: 100 },
              { name: '推断统计学', value: 120 }
            ]
          },
          {
            name: '微积分',
            value: 200
          }
        ]
      },
      {
        name: '自然科学',
        value: 1000,
        children: [
          {
            name: '物理学',
            value: 350,
            children: [
              { name: '经典力学', value: 100 },
              { name: '量子力学', value: 120 },
              { name: '相对论', value: 80 },
              { name: '热力学', value: 50 }
            ]
          },
          {
            name: '化学',
            value: 300,
            children: [
              { name: '有机化学', value: 150 },
              { name: '无机化学', value: 100 },
              { name: '物理化学', value: 50 }
            ]
          },
          {
            name: '生物学',
            value: 350,
            children: [
              { name: '分子生物学', value: 120 },
              { name: '生态学', value: 80 },
              { name: '进化生物学', value: 100 },
              { name: '遗传学', value: 50 }
            ]
          }
        ]
      },
      {
        name: '人文社科',
        value: 900,
        children: [
          {
            name: '历史',
            value: 250,
            children: [
              { name: '古代史', value: 100 },
              { name: '近代史', value: 150 }
            ]
          },
          {
            name: '哲学',
            value: 200,
            children: [
              { name: '形而上学', value: 70 },
              { name: '伦理学', value: 80 },
              { name: '逻辑学', value: 50 }
            ]
          },
          {
            name: '心理学',
            value: 250,
            children: [
              { name: '认知心理学', value: 100 },
              { name: '发展心理学', value: 80 },
              { name: '临床心理学', value: 70 }
            ]
          },
          {
            name: '社会学',
            value: 200,
            children: [
              { name: '组织社会学', value: 100 },
              { name: '城市社会学', value: 100 }
            ]
          }
        ]
      }
    ]
  };
  
  return treeData;
}

// 初始化树状图可视化
function initDomainTreeChart() {
  const treeData = generateDomainTreeData();
  
  // 获取图表容器
  const chartDom = document.getElementById('domain-tree-chart');
  const chart = echarts.init(chartDom);
  
  // 配置图表选项
  const option = {
    title: {
      text: '领域树结构可视化 - 树状图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const data = params.data;
        let result = `<div><b>${data.name}</b>`;
        if (data.value) {
          result += `<br/>文本块数量: ${data.value}`;
        }
        return result + '</div>';
      }
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        type: 'tree',
        data: [treeData],
        top: '10%',
        bottom: '10%',
        layout: 'orthogonal',
        symbol: 'emptyCircle',
        symbolSize: 7,
        orient: 'vertical',
        expandAndCollapse: true,
        initialTreeDepth: 2,
        label: {
          position: 'top',
          rotate: 0,
          verticalAlign: 'middle',
          align: 'center',
          fontSize: 12
        },
        leaves: {
          label: {
            position: 'right',
            verticalAlign: 'middle',
            align: 'left'
          }
        },
        emphasis: {
          focus: 'descendant'
        },
        animationDuration: 550,
        animationDurationUpdate: 750
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化旭日图可视化
function initDomainSunburstChart() {
  const treeData = generateDomainTreeData();
  
  // 获取图表容器
  const chartDom = document.getElementById('domain-sunburst-chart');
  const chart = echarts.init(chartDom);
  
  // 配置图表选项
  const option = {
    title: {
      text: '领域分布 - 旭日图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const data = params.data;
        let result = `<div><b>${data.name}</b>`;
        if (data.value) {
          result += `<br/>文本块数量: ${data.value}`;
        }
        return result + '</div>';
      }
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        type: 'sunburst',
        data: treeData.children,
        radius: ['15%', '90%'],
        center: ['50%', '55%'],
        label: {
          rotate: 'radial',
          minAngle: 15
        },
        levels: [
          {},
          {
            r0: '15%',
            r: '45%',
            itemStyle: {
              borderWidth: 2
            },
            label: {
              rotate: 'tangential'
            }
          },
          {
            r0: '45%',
            r: '70%',
            label: {
              align: 'right'
            }
          },
          {
            r0: '70%',
            r: '90%',
            label: {
              position: 'outside',
              padding: 3,
              silent: false
            },
            itemStyle: {
              borderWidth: 3
            }
          }
        ]
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 初始化树图可视化（另一种布局）
function initDomainRadialTreeChart() {
  const treeData = generateDomainTreeData();
  
  // 获取图表容器
  const chartDom = document.getElementById('domain-radial-tree');
  const chart = echarts.init(chartDom);
  
  // 配置图表选项
  const option = {
    title: {
      text: '领域结构 - 径向树图',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const data = params.data;
        let result = `<div><b>${data.name}</b>`;
        if (data.value) {
          result += `<br/>文本块数量: ${data.value}`;
        }
        return result + '</div>';
      }
    },
    toolbox: {
      feature: {
        restore: {},
        saveAsImage: {}
      }
    },
    series: [
      {
        type: 'tree',
        data: [treeData],
        top: '5%',
        bottom: '5%',
        left: '5%',
        right: '5%',
        layout: 'radial',
        symbol: 'emptyCircle',
        symbolSize: 7,
        initialTreeDepth: 2,
        animationDurationUpdate: 750,
        emphasis: {
          focus: 'descendant'
        }
      }
    ]
  };
  
  // 渲染图表
  chart.setOption(option);
  
  // 响应式调整
  window.addEventListener('resize', () => {
    chart.resize();
  });
}

// 页面加载完成后初始化图表
document.addEventListener('DOMContentLoaded', function() {
  initDomainTreeChart();
  initDomainSunburstChart();
  initDomainRadialTreeChart();
}); 