apiVersion: v1
kind: Service
metadata:
  name: easy-dataset-prod
  labels:
    cloud.sealos.io/app-deploy-manager: easy-dataset-prod
spec:
  ports:
    - port: 1717
      targetPort: 1717
      name: qsqxlvoaqcqp
      protocol: TCP
  selector:
    app: easy-dataset-prod
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: easy-dataset-prod
  annotations:
    originImageName: easy-dataset/prod/app:${tag}
    deploy.cloud.sealos.io/minReplicas: '1'
    deploy.cloud.sealos.io/maxReplicas: '1'
    deploy.cloud.sealos.io/resize: 0Gi
  labels:
    app: easy-dataset-prod
    cloud.sealos.io/app-deploy-manager: easy-dataset-prod
spec:
  replicas: 1
  revisionHistoryLimit: 1
  selector:
    matchLabels:
      app: easy-dataset-prod
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: easy-dataset-prod
        restartTime: '**************'
    spec:
      automountServiceAccountToken: false
      imagePullSecrets:
        - name: easy-dataset-prod
      containers:
        - name: easy-dataset-prod
          image: gui-ygzkgxit.sealosgzg.site/easy-dataset/prod/app:${tag}
          env: []
          resources:
            requests:
              cpu: 100m
              memory: 204Mi
            limits:
              cpu: 1000m
              memory: 2048Mi
          ports:
            - containerPort: 1717
              name: qsqxlvoaqcqp
          imagePullPolicy: Always
          volumeMounts: []
      volumes: []
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: network-jsjteroywtlo
  labels:
    cloud.sealos.io/app-deploy-manager: easy-dataset-prod
    cloud.sealos.io/app-deploy-manager-domain: mymwbackgrhc
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 32m
    nginx.ingress.kubernetes.io/ssl-redirect: 'false'
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
    nginx.ingress.kubernetes.io/client-body-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-buffer-size: 64k
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/server-snippet: |
      client_header_buffer_size 64k;
      large_client_header_buffers 4 128k;
spec:
  rules:
    - host: mymwbackgrhc.sealosgzg.site
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: easy-dataset-prod
                port:
                  number: 1717
  tls:
    - hosts:
        - mymwbackgrhc.sealosgzg.site
      secretName: wildcard-cert
---
apiVersion: v1
kind: Secret
metadata:
  name: easy-dataset-prod
data:
  .dockerconfigjson: >-
    ************************************************************************************************************************************************************************************************************************************
type: kubernetes.io/dockerconfigjson
