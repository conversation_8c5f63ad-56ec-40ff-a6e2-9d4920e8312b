{"/_not-found/page": "/_not-found", "/api/llm/fetch-models/route": "/api/llm/fetch-models", "/api/llm/providers/route": "/api/llm/providers", "/api/check-update/route": "/api/check-update", "/api/llm/model/route": "/api/llm/model", "/api/projects/[projectId]/chunks/[chunkId]/questions/route": "/api/projects/[projectId]/chunks/[chunkId]/questions", "/api/projects/[projectId]/batch-generateGA/route": "/api/projects/[projectId]/batch-generateGA", "/api/projects/[projectId]/chunks/[chunkId]/route": "/api/projects/[projectId]/chunks/[chunkId]", "/api/projects/[projectId]/chunks/batch-edit/route": "/api/projects/[projectId]/chunks/batch-edit", "/api/projects/[projectId]/chunks/route": "/api/projects/[projectId]/chunks", "/api/projects/[projectId]/config/route": "/api/projects/[projectId]/config", "/api/projects/[projectId]/chunks/name/route": "/api/projects/[projectId]/chunks/name", "/api/projects/[projectId]/custom-split/route": "/api/projects/[projectId]/custom-split", "/api/projects/[projectId]/datasets/[datasetId]/token-count/route": "/api/projects/[projectId]/datasets/[datasetId]/token-count", "/api/projects/[projectId]/datasets/[datasetId]/route": "/api/projects/[projectId]/datasets/[datasetId]", "/api/projects/[projectId]/datasets/batch-auto-tag/route": "/api/projects/[projectId]/datasets/batch-auto-tag", "/api/projects/[projectId]/datasets/route": "/api/projects/[projectId]/datasets", "/api/projects/[projectId]/distill/questions/by-tag/route": "/api/projects/[projectId]/distill/questions/by-tag", "/api/projects/[projectId]/datasets/import/route": "/api/projects/[projectId]/datasets/import", "/api/projects/[projectId]/distill/tags/route": "/api/projects/[projectId]/distill/tags", "/api/projects/[projectId]/datasets/optimize/route": "/api/projects/[projectId]/datasets/optimize", "/api/projects/[projectId]/distill/questions/route": "/api/projects/[projectId]/distill/questions", "/api/projects/[projectId]/file-records/route": "/api/projects/[projectId]/file-records", "/api/projects/[projectId]/files/[fileId]/ga-pairs/route": "/api/projects/[projectId]/files/[fileId]/ga-pairs", "/api/projects/[projectId]/generate-questions/route": "/api/projects/[projectId]/generate-questions", "/api/projects/[projectId]/llamaFactory/checkConfig/route": "/api/projects/[projectId]/llamaFactory/checkConfig", "/api/projects/[projectId]/init-tags/route": "/api/projects/[projectId]/init-tags", "/api/projects/[projectId]/model-config/[modelConfigId]/route": "/api/projects/[projectId]/model-config/[modelConfigId]", "/api/projects/[projectId]/huggingface/upload/route": "/api/projects/[projectId]/huggingface/upload", "/api/projects/[projectId]/model-config/route": "/api/projects/[projectId]/model-config", "/api/projects/[projectId]/llamaFactory/generate/route": "/api/projects/[projectId]/llamaFactory/generate", "/api/projects/[projectId]/models/[modelId]/route": "/api/projects/[projectId]/models/[modelId]", "/api/projects/[projectId]/models/route": "/api/projects/[projectId]/models", "/api/projects/[projectId]/playground/chat/route": "/api/projects/[projectId]/playground/chat", "/api/projects/[projectId]/questions/[questionId]/route": "/api/projects/[projectId]/questions/[questionId]", "/api/projects/[projectId]/playground/chat/stream/route": "/api/projects/[projectId]/playground/chat/stream", "/api/projects/[projectId]/questions/tree/route": "/api/projects/[projectId]/questions/tree", "/api/projects/[projectId]/preview/[fileId]/route": "/api/projects/[projectId]/preview/[fileId]", "/api/projects/[projectId]/questions/batch-delete/route": "/api/projects/[projectId]/questions/batch-delete", "/api/projects/[projectId]/route": "/api/projects/[projectId]", "/api/projects/[projectId]/questions/route": "/api/projects/[projectId]/questions", "/api/projects/[projectId]/split/route": "/api/projects/[projectId]/split", "/api/projects/[projectId]/tasks/list/route": "/api/projects/[projectId]/tasks/list", "/api/projects/[projectId]/statistics/route": "/api/projects/[projectId]/statistics", "/api/projects/[projectId]/tasks/route": "/api/projects/[projectId]/tasks", "/api/projects/[projectId]/tasks/[taskId]/route": "/api/projects/[projectId]/tasks/[taskId]", "/api/projects/[projectId]/tags/route": "/api/projects/[projectId]/tags", "/api/projects/batch-init-tags/route": "/api/projects/batch-init-tags", "/api/projects/delete-directory/route": "/api/projects/delete-directory", "/api/projects/open-directory/route": "/api/projects/open-directory", "/api/projects/migrate/route": "/api/projects/migrate", "/api/projects/unmigrated/route": "/api/projects/unmigrated", "/api/update/route": "/api/update", "/page": "/", "/big-screen/page": "/big-screen", "/dataset-square/page": "/dataset-square", "/projects/page": "/projects", "/api/llm/ollama/models/route": "/api/llm/ollama/models", "/api/projects/[projectId]/datasets/export/route": "/api/projects/[projectId]/datasets/export", "/api/projects/[projectId]/distill/tags/all/route": "/api/projects/[projectId]/distill/tags/all", "/api/projects/[projectId]/files/route": "/api/projects/[projectId]/files", "/api/projects/route": "/api/projects", "/api/statistics/dashboard/route": "/api/statistics/dashboard", "/api/statistics/global/route": "/api/statistics/global", "/api/statistics/file-records/route": "/api/statistics/file-records", "/api/statistics/timeline/route": "/api/statistics/timeline", "/projects/[projectId]/datasets/page": "/projects/[projectId]/datasets", "/projects/[projectId]/distill/page": "/projects/[projectId]/distill", "/projects/[projectId]/page": "/projects/[projectId]", "/projects/[projectId]/datasets/[datasetId]/page": "/projects/[projectId]/datasets/[datasetId]", "/projects/[projectId]/playground/page": "/projects/[projectId]/playground", "/projects/[projectId]/text-split/page": "/projects/[projectId]/text-split", "/projects/[projectId]/questions/page": "/projects/[projectId]/questions", "/projects/[projectId]/settings/page": "/projects/[projectId]/settings", "/projects/[projectId]/tasks/page": "/projects/[projectId]/tasks"}