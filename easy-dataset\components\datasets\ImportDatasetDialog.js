import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Paper,
  IconButton,
  useTheme,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Divider,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  TextField,
  Grid,
  RadioGroup,
  Radio
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import DescriptionIcon from '@mui/icons-material/Description';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import axios from 'axios';
import { toast } from 'sonner';

const ImportDatasetDialog = ({ open, onClose, projectId, onImportSuccess }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileFormat, setFileFormat] = useState('excel');
  const [importOptions, setImportOptions] = useState({
    importQuestion: true, // A列：问题 - 必填
    importAnswer: true, // B列：答案 - 必填
    importDomainTag: true, // C列：领域标签
    importCot: true, // D列：思维链
    importInstruction: true, // E列：指令
    autoDetectDomainTag: false, // 自动识别领域标签
    customInstruction: '', // 自定义指令内容
    useCustomInstruction: false, // 是否使用自定义指令
    // 新增格式选择选项（与导出功能一致）
    formatType: 'alpaca', // 格式类型：alpaca, sharegpt, custom
    customFields: {
      questionField: 'question',
      answerField: 'answer',
      cotField: 'cot',
      includeLabels: false,
      includeChunk: false
    }
  });

  // 处理文件选择
  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      const fileName = selectedFile.name.toLowerCase();
      let isValidType = false;

      switch (fileFormat) {
        case 'excel':
          isValidType = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
          break;
        case 'json':
          isValidType = fileName.endsWith('.json');
          break;
        case 'jsonl':
          isValidType = fileName.endsWith('.jsonl') || fileName.endsWith('.json');
          break;
        case 'csv':
          isValidType = fileName.endsWith('.csv');
          break;
        default:
          isValidType = false;
      }

      if (isValidType) {
        setFile(selectedFile);
        setError('');
      } else {
        setError(t('datasets.import.invalidFileType'));
        setFile(null);
      }
    }
  };

  // 处理文件拖放
  const handleDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();

    const droppedFile = event.dataTransfer.files[0];
    if (droppedFile) {
      const fileName = droppedFile.name.toLowerCase();
      let isValidType = false;

      switch (fileFormat) {
        case 'excel':
          isValidType = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
          break;
        case 'json':
          isValidType = fileName.endsWith('.json');
          break;
        case 'jsonl':
          isValidType = fileName.endsWith('.jsonl') || fileName.endsWith('.json');
          break;
        case 'csv':
          isValidType = fileName.endsWith('.csv');
          break;
        default:
          isValidType = false;
      }

      if (isValidType) {
        setFile(droppedFile);
        setError('');
      } else {
        setError(t('datasets.import.invalidFileType'));
        setFile(null);
      }
    }
  };

  // 处理文件格式变更
  const handleFileFormatChange = (event) => {
    setFileFormat(event.target.value);
    setFile(null); // 清除已选择的文件
  };

  // 防止默认拖放行为
  const handleDragOver = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  // 移除选择的文件
  const handleRemoveFile = () => {
    setFile(null);
  };

  // 处理导入选项变更
  const handleImportOptionChange = (event) => {
    const { name, checked, value } = event.target;

    // 问题和答案是必填的，不允许取消勾选
    if ((name === 'importQuestion' || name === 'importAnswer') && !checked) {
      return;
    }

    // 根据不同勾选情况处理领域标签逻辑
    let newOptions = { ...importOptions };

    if (name === 'customInstruction') {
      // 处理自定义指令内容输入
      newOptions = {
        ...newOptions,
        [name]: value
      };
    } else if (name === 'format') {
      // 处理格式类型选择（与导出功能一致）
      newOptions = {
        ...newOptions,
        formatType: value
      };
    } else if (name.startsWith('customFields.')) {
      // 处理自定义字段映射
      const fieldName = name.replace('customFields.', '');
      newOptions = {
        ...newOptions,
        customFields: {
          ...newOptions.customFields,
          [fieldName]: value
        }
      };
    } else if (name === 'includeLabels' || name === 'includeChunk') {
      // 处理自定义格式的标签和文本块选项
      newOptions = {
        ...newOptions,
        customFields: {
          ...newOptions.customFields,
          [name]: checked
        }
      };
    } else if (name === 'autoDetectDomainTag') {
      // 更新自动识别标签选项
      newOptions = {
        ...newOptions,
        [name]: checked
      };
    } else {
      // 其他选项直接更新
      newOptions = {
        ...newOptions,
        [name]: checked
      };
    }

    setImportOptions(newOptions);
  };

  // 获取当前文件格式的接受类型
  const getAcceptType = () => {
    switch (fileFormat) {
      case 'excel':
        return '.xlsx,.xls';
      case 'json':
        return '.json';
      case 'jsonl':
        return '.jsonl,.json';
      case 'csv':
        return '.csv';
      default:
        return '.xlsx,.xls,.json,.jsonl,.csv';
    }
  };

  // 获取当前文件格式的支持格式提示
  const getSupportedFormatsText = () => {
    switch (fileFormat) {
      case 'excel':
        return '.xlsx, .xls';
      case 'json':
        return '.json';
      case 'jsonl':
        return '.jsonl, .json';
      case 'csv':
        return '.csv';
      default:
        return '.xlsx, .xls, .json, .jsonl, .csv';
    }
  };

  // 提交导入请求
  const handleImport = async () => {
    if (!file) {
      setError(t('datasets.import.noFileSelected'));
      return;
    }

    setLoading(true);
    setError('');
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileFormat', fileFormat);

    // 添加导入选项到请求中
    Object.keys(importOptions).forEach(key => {
      if (key === 'customFields') {
        // 处理嵌套的customFields对象
        Object.keys(importOptions.customFields).forEach(fieldKey => {
          formData.append(`customFields.${fieldKey}`, importOptions.customFields[fieldKey]);
        });
      } else {
        formData.append(key, importOptions[key]);
      }
    });

    try {
      const response = await axios.post(`/api/projects/${projectId}/datasets/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data.success) {
        toast.success(response.data.message);
        onImportSuccess();
        onClose();
      } else {
        setError(response.data.error || t('datasets.import.unknownError'));
      }
    } catch (error) {
      console.error('导入数据集失败:', error);
      setError(error.response?.data?.error || error.message || t('datasets.import.unknownError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? null : onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2
        }
      }}
    >
      <DialogTitle>{t('datasets.import.title')}</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            {t('datasets.import.description')}
          </Typography>

          <Box sx={{ mb: 2, mt: 2 }}>
            <FormControl fullWidth>
              <InputLabel id="file-format-select-label">{t('export.fileFormat')}</InputLabel>
              <Select
                labelId="file-format-select-label"
                id="file-format-select"
                value={fileFormat}
                label={t('export.fileFormat')}
                onChange={handleFileFormatChange}
              >
                <MenuItem value="excel">Excel</MenuItem>
                <MenuItem value="json">JSON</MenuItem>
                <MenuItem value="jsonl">JSONL</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
              </Select>
            </FormControl>
          </Box>

          <Typography variant="body2" color="text.secondary" gutterBottom>
            {t('datasets.import.formatDescription')}
          </Typography>

          {fileFormat === 'excel' && (
            <>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - A列: {t('datasets.question')}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - B列: {t('datasets.answer')}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - C列: {t('datasets.domainTag')}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - D列: {t('datasets.cot')} ({t('export.includeCOT')})
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - E列: {t('datasets.instruction')}
              </Typography>
            </>
          )}

          {(fileFormat === 'json' || fileFormat === 'jsonl' || fileFormat === 'csv') && (
            <>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('datasets.import.jsonFormat')}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom sx={{ fontFamily: 'monospace', backgroundColor: 'background.paper', p: 1 }}>
                {`{
  "question": "问题内容",
  "domainTag": "领域标签",
  "answer": "答案内容",
  "cot": "思维链内容"
}`}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {t('datasets.import.supportedFields')}:
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - {t('datasets.import.questionFields')}: question, instruction, input, prompt
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - {t('datasets.import.answerFields')}: answer, output, response
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - {t('datasets.import.tagFields')}: domainTag, label, questionLabel, tag
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                - {t('datasets.import.cotFields')}: cot, complexCOT, chainOfThought, thinking
              </Typography>
            </>
          )}
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {!file ? (
          <Paper
            variant="outlined"
            sx={{
              p: 3,
              textAlign: 'center',
              borderStyle: 'dashed',
              borderWidth: 2,
              borderRadius: 2,
              borderColor: theme.palette.divider,
              backgroundColor: theme.palette.background.default,
              '&:hover': {
                borderColor: theme.palette.primary.main,
                backgroundColor: theme.palette.action.hover
              },
              cursor: 'pointer'
            }}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => document.getElementById('file-input').click()}
          >
            <input
              id="file-input"
              type="file"
              accept={getAcceptType()}
              onChange={handleFileChange}
              style={{ display: 'none' }}
            />
            <CloudUploadIcon sx={{ fontSize: 48, color: theme.palette.text.secondary, mb: 1 }} />
            <Typography variant="h6" gutterBottom>
              {t('datasets.import.dragAndDrop')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('datasets.import.or')}
            </Typography>
            <Button
              variant="outlined"
              component="span"
              sx={{ mt: 2 }}
              startIcon={<DescriptionIcon />}
            >
              {t('datasets.import.browseFiles')}
            </Button>
            <Typography variant="caption" display="block" color="text.secondary" sx={{ mt: 2 }}>
              {t('datasets.import.supportedFormats')}: {getSupportedFormatsText()}
            </Typography>
          </Paper>
        ) : (
          <Box>
            <Paper
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 2
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <DescriptionIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
                <Typography variant="body1" noWrap sx={{ maxWidth: '400px' }}>
                  {file.name}
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
                  {(file.size / 1024).toFixed(2)} KB
                </Typography>
              </Box>
              <IconButton onClick={handleRemoveFile} disabled={loading}>
                <DeleteIcon />
              </IconButton>
            </Paper>

            <Divider sx={{ my: 2 }} />

            {fileFormat === 'excel' && (
              <>
                <Typography variant="subtitle1" gutterBottom>
                  {t('datasets.import.selectColumns')}
                </Typography>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importQuestion}
                        onChange={handleImportOptionChange}
                        name="importQuestion"
                        disabled={true} // 问题是必填的，不能取消选择
                      />
                    }
                    label={`A列: ${t('datasets.question')} (${t('common.required')})`}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importAnswer}
                        onChange={handleImportOptionChange}
                        name="importAnswer"
                        disabled={true} // 答案是必填的，不能取消选择
                      />
                    }
                    label={`B列: ${t('datasets.answer')} (${t('common.required')})`}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importDomainTag}
                        onChange={handleImportOptionChange}
                        name="importDomainTag"
                      />
                    }
                    label={`C列: ${t('datasets.domainTag')}`}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.autoDetectDomainTag}
                        onChange={handleImportOptionChange}
                        name="autoDetectDomainTag"
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <span>{t('datasets.import.autoDetectDomainTag')}</span>
                        <Tooltip title={t('datasets.import.autoDetectDomainTagHelp')}>
                          <HelpOutlineIcon fontSize="small" sx={{ ml: 1 }} />
                        </Tooltip>
                      </Box>
                    }
                  />

                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importCot}
                        onChange={handleImportOptionChange}
                        name="importCot"
                      />
                    }
                    label={`D列: ${t('datasets.cot')} (${t('export.includeCOT')})`}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importInstruction}
                        onChange={handleImportOptionChange}
                        name="importInstruction"
                      />
                    }
                    label={`E列: ${t('datasets.instruction')}`}
                  />
                </FormGroup>

                <Divider sx={{ my: 2 }} />

                {/* 自定义指令选项 */}
                <Typography variant="subtitle2" gutterBottom>
                  {t('datasets.instructionSettings')}
                </Typography>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.useCustomInstruction}
                        onChange={handleImportOptionChange}
                        name="useCustomInstruction"
                      />
                    }
                    label={t('datasets.useCustomInstruction')}
                  />
                  {importOptions.useCustomInstruction && (
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      value={importOptions.customInstruction}
                      onChange={handleImportOptionChange}
                      name="customInstruction"
                      label={t('datasets.customInstructionContent')}
                      placeholder={t('datasets.customInstructionPlaceholder')}
                      sx={{ mt: 1 }}
                    />
                  )}
                </FormGroup>
              </>
            )}

            {(fileFormat === 'json' || fileFormat === 'jsonl' || fileFormat === 'csv') && (
              <>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  {t('export.format')}
                </Typography>

                <FormControl component="fieldset" sx={{ mb: 2 }}>
                  <RadioGroup
                    aria-label="format"
                    name="format"
                    value={importOptions.formatType}
                    onChange={handleImportOptionChange}
                    row
                  >
                    <FormControlLabel value="alpaca" control={<Radio />} label="Alpaca" />
                    <FormControlLabel value="sharegpt" control={<Radio />} label="ShareGPT" />
                    <FormControlLabel value="custom" control={<Radio />} label={t('export.customFormat')} />
                  </RadioGroup>
                </FormControl>

                {importOptions.formatType === 'custom' && (
                  <Box sx={{ mb: 2, pl: 2, borderLeft: `1px solid ${theme.palette.divider}` }}>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('export.customFormatSettings')}
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label={t('export.questionFieldName')}
                          value={importOptions.customFields.questionField}
                          onChange={handleImportOptionChange}
                          name="customFields.questionField"
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label={t('export.answerFieldName')}
                          value={importOptions.customFields.answerField}
                          onChange={handleImportOptionChange}
                          name="customFields.answerField"
                          margin="normal"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          size="small"
                          label={t('export.cotFieldName')}
                          value={importOptions.customFields.cotField}
                          onChange={handleImportOptionChange}
                          name="customFields.cotField"
                          margin="normal"
                        />
                      </Grid>
                    </Grid>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={importOptions.customFields.includeLabels}
                          onChange={handleImportOptionChange}
                          name="includeLabels"
                          size="small"
                        />
                      }
                      label={t('export.includeLabels')}
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={importOptions.customFields.includeChunk}
                          onChange={handleImportOptionChange}
                          name="includeChunk"
                          size="small"
                        />
                      }
                      label={t('export.includeChunk')}
                    />
                  </Box>
                )}

                <FormGroup sx={{ mt: 2 }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.importDomainTag}
                        onChange={handleImportOptionChange}
                        name="importDomainTag"
                      />
                    }
                    label={t('datasets.domainTag')}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.autoDetectDomainTag}
                        onChange={handleImportOptionChange}
                        name="autoDetectDomainTag"
                      />
                    }
                    label={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <span>{t('datasets.import.autoDetectDomainTag')}</span>
                        <Tooltip title={t('datasets.import.autoDetectDomainTagHelp')}>
                          <HelpOutlineIcon fontSize="small" sx={{ ml: 1 }} />
                        </Tooltip>
                      </Box>
                    }
                  />
                </FormGroup>

                {/* 自定义指令选项 */}
                <Divider sx={{ my: 2 }} />
                <Typography variant="subtitle2" gutterBottom>
                  {t('datasets.instructionSettings')}
                </Typography>
                <FormGroup>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={importOptions.useCustomInstruction}
                        onChange={handleImportOptionChange}
                        name="useCustomInstruction"
                      />
                    }
                    label={t('datasets.useCustomInstruction')}
                  />
                  {importOptions.useCustomInstruction && (
                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      value={importOptions.customInstruction}
                      onChange={handleImportOptionChange}
                      name="customInstruction"
                      label={t('datasets.customInstructionContent')}
                      placeholder={t('datasets.customInstructionPlaceholder')}
                      sx={{ mt: 1 }}
                    />
                  )}
                </FormGroup>
              </>
            )}
          </Box>
        )}

        {loading && (
          <Box sx={{ width: '100%', mt: 3 }}>
            <LinearProgress variant="determinate" value={uploadProgress} />
            <Typography variant="caption" display="block" align="center" sx={{ mt: 1 }}>
              {uploadProgress}% {t('datasets.import.uploading')}
            </Typography>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} disabled={loading} variant="outlined" sx={{ borderRadius: 2 }}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleImport}
          disabled={!file || loading}
          variant="contained"
          color="primary"
          sx={{ borderRadius: 2 }}
        >
          {loading ? t('datasets.import.importing') : t('datasets.import.import')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ImportDatasetDialog;
