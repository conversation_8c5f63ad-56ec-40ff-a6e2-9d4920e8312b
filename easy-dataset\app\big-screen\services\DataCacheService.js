/**
 * 数据缓存服务
 * 负责管理大屏数据的缓存、预加载和异步获取
 */
class DataCacheService {
  constructor() {
    this.cache = new Map();
    this.loadingPromises = new Map(); // 防止重复请求
    this.CACHE_DURATION = 10 * 60 * 1000; // 10分钟缓存

    // 东盟十国列表
    this.ASEAN_COUNTRIES = [
      'Brunei', 'Cambodia', 'Indonesia', 'Laos', 'Malaysia',
      'Myanmar', 'Philippines', 'Singapore', 'Thailand', 'Vietnam'
    ];

    // 国家名称到国家代码的映射
    this.countryNameToCode = {
      'Brunei': 'BN',
      'Cambodia': 'KH',
      'Indonesia': 'ID',
      'Laos': 'LA',
      'Malaysia': 'MY',
      'Myanmar': 'MM',
      'Philippines': 'PH',
      'Singapore': 'SG',
      'Thailand': 'TH',
      'Vietnam': 'VN'
    };
  }

  /**
   * 生成缓存键
   */
  getCacheKey(type, country = null) {
    return country ? `${type}_${country}` : `${type}_global`;
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheItem) {
    if (!cacheItem) return false;
    return Date.now() - cacheItem.timestamp < this.CACHE_DURATION;
  }

  /**
   * 设置缓存
   */
  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    const cacheItem = this.cache.get(key);
    if (this.isCacheValid(cacheItem)) {
      return cacheItem.data;
    }
    return null;
  }

  /**
   * 获取项目数据
   */
  async fetchProjects() {
    const cacheKey = this.getCacheKey('projects');
    const cached = this.getCache(cacheKey);
    if (cached) {
      console.log('使用缓存的项目数据');
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchProjectsFromAPI();
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  async _fetchProjectsFromAPI() {
    const response = await fetch('/api/projects');
    if (!response.ok) {
      throw new Error(`获取项目数据失败: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取全局统计数据
   */
  async fetchGlobalStatistics() {
    const cacheKey = this.getCacheKey('globalStats');
    const cached = this.getCache(cacheKey);
    if (cached) {
      console.log('使用缓存的全局统计数据');
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchGlobalStatisticsFromAPI();
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  async _fetchGlobalStatisticsFromAPI() {
    const response = await fetch('/api/statistics/global');
    if (!response.ok) {
      throw new Error(`获取全局统计数据失败: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取仪表板数据（全局或特定国家）
   */
  async fetchDashboardData(country = null) {
    const cacheKey = this.getCacheKey('dashboard', country);
    const cached = this.getCache(cacheKey);
    if (cached) {
      console.log(`使用缓存的仪表板数据: ${country || '全局'}`);
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchDashboardDataFromAPI(country);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  async _fetchDashboardDataFromAPI(country = null) {
    const url = country
      ? `/api/statistics/dashboard?country=${encodeURIComponent(country)}`
      : '/api/statistics/dashboard';

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取仪表板数据失败: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 获取文件记录统计数据
   */
  async fetchFileRecordsData(projectId = null, country = null) {
    const cacheKey = this.getCacheKey('fileRecords', projectId || country);
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchFileRecordsDataFromAPI(projectId, country);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 获取项目数据集数据
   */
  async fetchProjectDatasets(projectId, confirmed = null) {
    const cacheKey = this.getCacheKey('datasets', `${projectId}_${confirmed || 'all'}`);
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchProjectDatasetsFromAPI(projectId, confirmed);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 获取项目标签树数据
   */
  async fetchProjectTags(projectId) {
    const cacheKey = this.getCacheKey('tags', projectId);
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchProjectTagsFromAPI(projectId);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  /**
   * 获取时间序列数据
   */
  async fetchTimelineData(projectId = null, months = 6) {
    const cacheKey = this.getCacheKey('timeline', `${projectId || 'global'}_${months}`);
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchTimelineDataFromAPI(projectId, months);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  async _fetchFileRecordsDataFromAPI(projectId = null, country = null) {
    let url = '/api/statistics/file-records';
    const params = new URLSearchParams();

    if (projectId) {
      params.append('projectId', projectId);
    } else if (country) {
      const countryCode = this.countryNameToCode[country];
      if (countryCode) {
        params.append('country', countryCode);
      }
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取文件记录数据失败: ${response.status}`);
    }
    return response.json();
  }

  async _fetchProjectDatasetsFromAPI(projectId, confirmed = null) {
    let url = `/api/projects/${projectId}/datasets/export`;
    if (confirmed !== null) {
      url += `?confirmed=${confirmed}`;
    }

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取项目数据集失败: ${response.status}`);
    }
    return response.json();
  }

  async _fetchProjectTagsFromAPI(projectId) {
    const url = `/api/projects/${projectId}/tags`;
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取项目标签树失败: ${response.status}`);
    }
    const data = await response.json();
    return data.tags || [];
  }

  async _fetchTimelineDataFromAPI(projectId = null, months = 6) {
    let url = `/api/statistics/timeline?months=${months}`;
    if (projectId) {
      url += `&projectId=${projectId}`;
    }

    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`获取时间序列数据失败: ${response.status}`);
    }
    return response.json();
  }

  /**
   * 批量获取所有项目的数据集和标签数据
   */
  async fetchAllProjectsData(confirmed = null) {
    const cacheKey = this.getCacheKey('allProjectsData', confirmed || 'all');
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey);
    }

    const promise = this._fetchAllProjectsDataFromAPI(confirmed);
    this.loadingPromises.set(cacheKey, promise);

    try {
      const data = await promise;
      this.setCache(cacheKey, data);
      return data;
    } finally {
      this.loadingPromises.delete(cacheKey);
    }
  }

  async _fetchAllProjectsDataFromAPI(confirmed = null) {
    // 获取所有项目
    const projects = await this.fetchProjects();

    let allDatasets = [];
    let allTags = [];

    // 并行获取所有项目的数据
    const projectPromises = projects.map(async (project) => {
      try {
        const [datasets, tags] = await Promise.all([
          this.fetchProjectDatasets(project.id, confirmed),
          this.fetchProjectTags(project.id)
        ]);

        // 为数据集添加项目ID信息
        const datasetsWithProjectId = datasets.map(dataset => ({
          ...dataset,
          projectId: dataset.projectId || project.id
        }));

        return { datasets: datasetsWithProjectId, tags };
      } catch (error) {
        console.warn(`获取项目 ${project.id} 数据失败:`, error);
        return { datasets: [], tags: [] };
      }
    });

    const results = await Promise.all(projectPromises);

    // 合并所有数据
    results.forEach(({ datasets, tags }) => {
      allDatasets = allDatasets.concat(datasets);
      allTags = allTags.concat(tags);
    });

    return { allDatasets, allTags };
  }

  /**
   * 预加载所有必要数据
   * 1. 先加载全局数据
   * 2. 异步加载各国数据
   * 3. 异步加载项目数据集和标签数据
   */
  async preloadAllData() {
    try {
      // 1. 并行加载基础全局数据
      const [projects, globalStats, globalDashboard, globalTimeline] = await Promise.all([
        this.fetchProjects(),
        this.fetchGlobalStatistics(),
        this.fetchDashboardData(),
        this.fetchTimelineData() // 预加载全局时间序列数据
      ]);

      // 2. 构建国家项目映射
      const countryProjectMap = this.buildCountryProjectMap(projects);

      // 3. 异步预加载各国数据和项目数据（不阻塞页面显示）
      this.preloadCountriesDataAsync(countryProjectMap);
      this.preloadProjectsDataAsync(projects);

      return {
        projects,
        globalStats,
        globalDashboard,
        globalTimeline,
        countryProjectMap
      };
    } catch (error) {
      console.error('预加载数据失败:', error);
      throw error;
    }
  }

  /**
   * 构建国家项目映射
   */
  buildCountryProjectMap(projects) {
    const countryProjectMap = {};
    projects.forEach(project => {
      if (project.country) {
        const englishName = Object.keys(this.countryNameToCode).find(
          name => this.countryNameToCode[name] === project.country
        );
        if (englishName) {
          countryProjectMap[englishName] = project.id;
        }
      }
    });
    return countryProjectMap;
  }

  /**
   * 异步预加载各国数据
   */
  async preloadCountriesDataAsync(countryProjectMap) {
    // 为每个国家异步加载数据
    const loadPromises = this.ASEAN_COUNTRIES.map(async (country) => {
      try {
        const projectId = countryProjectMap[country];

        // 并行加载该国家的各种数据
        await Promise.all([
          this.fetchDashboardData(country),
          this.fetchFileRecordsData(projectId, country),
          // 如果有项目ID，也预加载该项目的时间序列数据
          projectId ? this.fetchTimelineData(projectId) : Promise.resolve()
        ]);
      } catch (error) {
        console.warn(`${country} 数据预加载失败:`, error);
      }
    });

    // 等待所有国家数据加载完成
    await Promise.allSettled(loadPromises);
  }

  /**
   * 异步预加载项目数据集和标签数据
   */
  async preloadProjectsDataAsync(projects) {
    // 预加载所有项目的数据集（已确认和全部）
    const loadPromises = [
      this.fetchAllProjectsData(true),  // 已确认的数据集
      this.fetchAllProjectsData(),      // 所有数据集
    ];

    // 为每个项目预加载数据集和标签
    projects.forEach(project => {
      loadPromises.push(
        this.fetchProjectDatasets(project.id, true),  // 已确认数据集
        this.fetchProjectDatasets(project.id),        // 所有数据集
        this.fetchProjectTags(project.id)             // 标签树
      );
    });

    // 等待所有项目数据加载完成
    await Promise.allSettled(loadPromises);
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    const status = {
      totalItems: this.cache.size,
      loadingItems: this.loadingPromises.size,
      items: {}
    };

    for (const [key, value] of this.cache.entries()) {
      status.items[key] = {
        timestamp: value.timestamp,
        age: Date.now() - value.timestamp,
        valid: this.isCacheValid(value)
      };
    }

    return status;
  }


}

// 创建单例实例
const dataCacheService = new DataCacheService();

export default dataCacheService;
