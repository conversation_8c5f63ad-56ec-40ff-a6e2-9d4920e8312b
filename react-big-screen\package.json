{"private": true, "scripts": {"start": "set PORT=9000 && roadhog server", "build": "roadhog build", "lint": "eslint --ext .js src test", "precommit": "npm run lint"}, "dependencies": {"@jiaminghi/data-view-react": "^1.2.4", "dva": "^2.4.1", "echarts": "^4.9.0", "node-sass": "^4.14.1", "react": "^16.2.0", "react-dom": "^16.2.0", "sass-loader": "8.0.2", "styled-components": "^5.2.0"}, "devDependencies": {"babel-plugin-dva-hmr": "^0.3.2", "eslint": "^4.14.0", "eslint-config-umi": "^0.1.1", "eslint-plugin-flowtype": "^2.34.1", "eslint-plugin-import": "^2.6.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.1.0", "husky": "^0.12.0", "redbox-react": "^1.4.3", "roadhog": "^2.5.0-beta.4"}}