[{"version": "1.2.5", "sql": "ALTER TABLE Projects ADD COLUMN test VARCHAR(255) DEFAULT '';"}, {"version": "1.3.3", "sql": "CREATE TABLE IF NOT EXISTS Task (\n  id VARCHAR(255) NOT NULL,\n  projectId VARCHAR(255) NOT NULL,\n  taskType VARCHAR(255) NOT NULL,\n  status INT NOT NULL,\n  startTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  endTime TIMESTAMP NULL,\n  completedCount INT DEFAULT 0,\n  totalCount INT DEFAULT 0,\n  modelInfo TEXT NOT NULL,\n  language VARCHAR(20) DEFAULT 'zh-CN',\n  detail TEXT DEFAULT '',\n  note TEXT DEFAULT '',\n  createAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n  updateAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n  PRIMARY KEY (id),\n  FOREIGN KEY (projectId) REFERENCES Projects(id) ON DELETE CASCADE\n) ENGINE=InnoDB;\n\nCREATE INDEX idx_task_projectId ON Task(projectId);"}, {"version": "1.3.6", "sql": "CREATE TABLE IF NOT EXISTS GaPairs (\n id VARCHAR(255) NOT NULL,\n projectId VARCHAR(255) NOT NULL,\n fileId VARCHAR(255) NOT NULL,\n pairNumber INT NOT NULL,\n genreTitle VARCHAR(255) NOT NULL,\n genreDesc TEXT NOT NULL,\n audienceTitle VARCHAR(255) NOT NULL,\n audienceDesc TEXT NOT NULL,\n isActive TINYINT(1) DEFAULT 1 NOT NULL,\n createAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n updateAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n <PERSON>IMAR<PERSON> KEY (id),\n <PERSON>OR<PERSON><PERSON><PERSON> KEY (projectId) REFERENCES Projects(id) ON DELETE CASCADE,\n FOREIG<PERSON> KEY (fileId) REFERENCES UploadFiles(id) ON DELETE CASCADE,\n UNIQUE (fileId, pairNumber)\n) ENGINE=InnoDB;\n\nCREATE INDEX idx_gapairs_projectId ON GaPairs(projectId);\nCREATE INDEX idx_gapairs_fileId ON GaPairs(fileId);"}, {"version": "1.3.6", "sql": "ALTER TABLE Questions ADD COLUMN gaPairId VARCHAR(255) NULL;"}, {"version": "1.3.6", "sql": "ALTER TABLE Questions ADD CONSTRAINT fk_questions_gapairid FOREIGN KEY (gaPairId) REFERENCES GaPairs(id) ON DELETE SET NULL;\n\nCREATE INDEX idx_questions_gaPairId ON Questions(gaPairId);"}]