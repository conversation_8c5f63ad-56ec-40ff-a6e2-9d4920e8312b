@echo off
REM Docker构建性能分析脚本 (Windows版本)
REM 分析构建过程中的时间消耗和优化建议

echo 🔍 Docker构建性能分析工具
echo ================================

REM 检查Docker版本和BuildKit支持
echo 📋 环境检查:
docker --version
echo BuildKit状态: %DOCKER_BUILDKIT%

REM 分析主要目录大小
echo.
echo 📂 主要目录大小:
if exist "node_modules" (
    for /f "tokens=3" %%a in ('dir "node_modules" /-c ^| find "个文件"') do echo   %%a - node_modules/
)
if exist ".next" (
    for /f "tokens=3" %%a in ('dir ".next" /-c ^| find "个文件"') do echo   %%a - .next/
)
if exist "public" (
    for /f "tokens=3" %%a in ('dir "public" /-c ^| find "个文件"') do echo   %%a - public/
)
if exist "prisma" (
    for /f "tokens=3" %%a in ('dir "prisma" /-c ^| find "个文件"') do echo   %%a - prisma/
)

REM 检查.dockerignore文件
echo.
echo 🚫 .dockerignore 检查:
if exist ".dockerignore" (
    echo ✅ .dockerignore 文件存在
    for /f %%a in ('type ".dockerignore" ^| find /c /v ""') do echo    包含 %%a 条忽略规则
) else (
    echo ❌ .dockerignore 文件不存在 - 这会显著增加构建时间！
)

REM 分析依赖
echo.
echo 📦 依赖分析:
if exist "package.json" (
    echo 检查 package.json 文件...
    findstr /c:"dependencies" package.json >nul && echo ✅ 包含生产依赖
    findstr /c:"devDependencies" package.json >nul && echo ✅ 包含开发依赖
)

REM 性能建议
echo.
echo 💡 优化建议:
echo ================================

if not exist ".dockerignore" (
    echo 🔥 高优先级: 创建 .dockerignore 文件
    echo    - 排除 node_modules, .next, .git 等目录
    echo    - 预计可减少构建时间 60-80%%
)

echo 🔧 中等优先级: 使用多阶段构建优化
echo    - 分离依赖安装和应用构建
echo    - 使用专门的生产依赖阶段

echo ⚡ 低优先级: 启用构建缓存
echo    - 使用 DOCKER_BUILDKIT=1
echo    - 配置远程缓存 (如果使用CI/CD)

REM 估算优化效果
echo.
echo 📈 预期优化效果:
echo ================================
if not exist ".dockerignore" (
    echo 添加 .dockerignore: 减少 60-80%% 构建时间
)
echo 多阶段构建优化: 减少 20-40%% 构建时间
echo 启用 BuildKit: 减少 10-20%% 构建时间
echo 使用构建缓存: 减少 50-90%% 重复构建时间

echo.
echo 🎯 建议的构建命令:
echo set DOCKER_BUILDKIT=1
echo docker build -f Dockerfile.optimized -t easy-dataset:optimized .

echo.
echo 或者使用优化脚本:
echo docker-build-optimized.bat easy-dataset latest

echo.
echo ✅ 分析完成！
pause
