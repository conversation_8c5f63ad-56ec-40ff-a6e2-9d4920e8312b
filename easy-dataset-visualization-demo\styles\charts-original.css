/**
 * 🔑 完全按照原项目的样式布局
 * 保持现有功能，仅改变布局和样式
 */

/* 🔑 原项目的全局样式重置 */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  box-sizing: border-box;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 🔑 原项目的文本溢出处理 */
.textOverHandle {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 🔑 原项目的默认图表样式 */
.default-chart {
  width: 100%;
  height: 100%;
}

/* 🔑 图表容器基础样式 - 按照原项目设计 */
.chart-container {
  height: 400px;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 15px;
  background-color: rgba(19, 25, 47, 0.6);
  border: 1px solid #343f4b;
  transition: all 0.3s ease;
}

.chart-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* 🔑 图表行布局 */
.chart-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

/* 🔑 图表列布局 */
.chart-col {
  flex: 1;
  min-width: 300px;
  padding: 0 15px;
  margin-bottom: 30px;
}

/* 🔑 仪表板部分样式 */
.dashboard-section {
  background-color: rgba(19, 25, 47, 0.6);
  border-radius: 12px;
  border: 1px solid #343f4b;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  padding: 25px;
  margin-bottom: 40px;
}

/* 🔑 部分标题样式 */
.section-title {
  margin-top: 0;
  padding-bottom: 15px;
  border-bottom: 1px solid #343f4b;
  color: #bcdcff;
  font-size: 1.8rem;
  font-weight: 600;
}

/* 🔑 图表标题样式 */
.chart-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: #bcdcff;
  margin-bottom: 15px;
}

/* 🔑 图表工具提示样式 */
.chart-tooltip {
  background-color: rgba(50, 50, 50, 0.9);
  border-radius: 4px;
  padding: 10px;
  color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 🔑 图表图例样式 */
.chart-legend {
  padding: 10px;
  font-size: 12px;
  color: #bcdcff;
}

/* 🔑 加载状态样式 */
.chart-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(19, 25, 47, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.chart-loading-spinner {
  border: 4px solid rgba(0, 162, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid #00a2ff;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 🔑 图表控制按钮样式 */
.chart-control-button {
  background-color: rgba(19, 25, 47, 0.8);
  border: 1px solid #343f4b;
  border-radius: 4px;
  padding: 8px 16px;
  margin: 5px;
  cursor: pointer;
  font-size: 12px;
  color: #bcdcff;
  transition: all 0.2s ease;
}

.chart-control-button:hover {
  background-color: rgba(0, 162, 255, 0.1);
  border-color: #00a2ff;
}

/* 🔑 原项目的主题色变量 */
:root {
  --primary-color: #00a2ff;
  --secondary-color: #00ff9f;
  --accent-color: #ff6b35;
  --warning-color: #ffb347;
  --error-color: #ff4757;
  --success-color: #2ed573;
  --info-color: #00a2ff;
  --background-color: #0c1426;
  --text-color: #ffffff;
  --text-secondary-color: #bcdcff;
}

/* 🔑 恢复原项目的页面滚动行为 */
body,
html {
  overflow-x: hidden !important;
  /* 🔑 防止水平滚动 */
  overflow-y: auto !important;
  /* 🔑 允许垂直滚动 */
  height: auto !important;
  min-height: 100vh;
}

/* 🔑 原项目的主页面样式 */
.index-page-style {
  position: relative;
  overflow: visible;
  /* 🔑 允许内容溢出，触发页面滚动 */
  margin: 0px;
  padding: 10px 0 50px 0;
  /* 🔑 添加底部padding，确保内容可见 */
  background: url('https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/pageBg.png') center center no-repeat;
  background-size: cover;
  min-height: calc(100vh + 200px);
  /* 🔑 确保内容高度超过视口，触发滚动 */
}

/* 🔑 恢复正常的仪表板内容滚动 */
.dashboard-content {
  /* 移除强制滚动设置，让页面自然滚动 */
  overflow: visible;
  max-height: none;
  min-height: auto;
  border: none;
  position: static;
  z-index: auto;
}

/* 🔑 页面级别的滚动条样式，保持科技感 */
body::-webkit-scrollbar {
  width: 8px;
}

body::-webkit-scrollbar-track {
  background: rgba(19, 25, 47, 0.6);
  border-radius: 4px;
}

body::-webkit-scrollbar-thumb {
  background: rgba(0, 162, 255, 0.8);
  border-radius: 4px;
  border: 1px solid rgba(0, 162, 255, 0.3);
}

body::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 162, 255, 1);
  border-color: rgba(0, 162, 255, 0.5);
}

/* 🔑 Firefox 页面滚动条样式 */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 162, 255, 0.8) rgba(19, 25, 47, 0.6);
}

/* 🔑 确保地图区域的固定高度 */
#asean-map {
  min-height: 500px;
  max-height: 500px;
}

/* 🔑 原项目的响应式设计 */
@media (max-width: 1200px) {
  .chart-container {
    height: 350px;
  }

  .section-title {
    font-size: 1.6rem;
  }

  #asean-map {
    min-height: 400px;
    max-height: 400px;
  }

  .dashboard-content {
    max-height: calc(100vh - 550px);
  }
}

@media (max-width: 992px) {
  .chart-container {
    height: 300px;
  }

  .dashboard-section {
    padding: 20px;
  }

  .section-title {
    font-size: 1.5rem;
  }

  #asean-map {
    min-height: 350px;
    max-height: 350px;
  }

  .dashboard-content {
    max-height: calc(100vh - 500px);
  }
}

@media (max-width: 768px) {
  .chart-col {
    flex: 100%;
  }

  .chart-container {
    height: 350px;
  }

  .dashboard-section {
    padding: 15px;
  }

  .section-title {
    font-size: 1.4rem;
  }

  #asean-map {
    min-height: 300px;
    max-height: 300px;
  }

  .dashboard-content {
    max-height: calc(100vh - 450px);
  }
}

@media (max-width: 576px) {
  .chart-container {
    height: 300px;
    padding: 10px;
  }

  .dashboard-section {
    padding: 10px;
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 1.3rem;
    padding-bottom: 10px;
  }

  #asean-map {
    min-height: 250px;
    max-height: 250px;
  }

  .dashboard-content {
    max-height: calc(100vh - 400px);
  }
}