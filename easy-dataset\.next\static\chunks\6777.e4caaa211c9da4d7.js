(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6777],{76777:function(t,e,a){"use strict";a.r(e),a.d(e,{default:function(){return ty}});var n=a(57437),i=a(2265);a(40450);var r=a(45008),o=a(99379);function s(){let t=(0,r._)(["\n  position: relative;\n  overflow-x: hidden; /* 只隐藏横向滚动条 */\n  overflow-y: auto;   /* 允许竖向滚动条 */\n  margin: 0;\n  padding: 10px 0 0 0;\n  background: url('https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/pageBg.png') center center no-repeat;\n  background-size: cover;\n  width: 100%;\n  max-width: 100vw; /* 强制最大宽度不超出视口 */\n  box-sizing: border-box;\n"]);return s=function(){return t},t}function l(){let t=(0,r._)(["\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: nowrap;\n  width: 100%;\n  box-sizing: border-box; /* 确保容器尺寸计算正确 */\n  overflow-x: hidden; /* 防止flex内容溢出产生横向滚动 */\n\n  .center-page {\n    width: 11.5rem;\n    flex-shrink: 0; /* 防止被压缩 */\n    box-sizing: border-box; /* 确保尺寸计算正确 */\n    overflow-x: hidden; /* 防止中间区域产生横向滚动 */\n  }\n"]);return l=function(){return t},t}let c=o.ZP.div(s()),h=o.ZP.div(l());function d(t,e,a){let n,i,r,o,s;let l=function(){let c=+new Date-o;c<e&&c>0?n=setTimeout(l,e-c):(n=null,a||(s=t.apply(r,i),n||(r=i=null)))};return function(){for(var i=arguments.length,c=Array(i),h=0;h<i;h++)c[h]=arguments[h];r=this,o=+new Date;let d=a&&!n;return n||(n=setTimeout(l,e)),d&&(s=t.apply(r,c),r=c=null),s}}function m(t,e){if(!t)return"";{let a=new Date(t),n={"M+":a.getMonth()+1,"d+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};for(let t in/(y+)/.test(e)&&(e=e.replace(RegExp.$1,(a.getFullYear()+"").substr(4-RegExp.$1.length))),n)RegExp("("+t+")").test(e)&&(e=e.replace(RegExp.$1,1===RegExp.$1.length?n[t]:("00"+n[t]).substr((""+n[t]).length)));return e}}var u=a(1620);let p="#bcdcff";function f(){let t=(0,r._)(["\n  .top_box {\n    display: flex;\n    justify-content: center;\n\n    .top_decoration10 {\n      position: relative;\n      width: 33.3%;\n      height: 0.0625rem;\n    }\n\n    .top_decoration10_reverse {\n      transform: rotateY(180deg);\n    }\n\n    .title-box {\n      display: flex;\n      justify-content: center;\n\n      .top_decoration8 {\n        width: 2.5rem;\n        height: 0.625rem;\n      }\n\n      .title {\n        position: relative;\n        width: 6.5rem;\n        text-align: center;\n        background-size: cover;\n        background-repeat: no-repeat;\n\n        .title-content {\n          position: absolute;\n          bottom: 10px;\n          left: 50%;\n          transform: translate(-50%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.15rem;\n        }\n\n        .title-logo {\n          width: 0.45rem;\n          height: 0.45rem;\n          object-fit: contain;\n        }\n\n        .title-text {\n          font-size: 0.4rem;\n          font-weight: 600;\n          letter-spacing: -0.02rem;\n          /* 使用与Navbar相同的深色模式渐变文字样式 */\n          background: linear-gradient(90deg, #2a5caa 0%, #8b5cf6 100%);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n          text-fill-color: transparent;\n          color: #fff;\n        }\n\n        .top_decoration6 {\n          width: 3.125rem;\n          height: 0.1rem;\n        }\n\n        .title-bototm {\n          position: absolute;\n          bottom: -0.375rem;\n          left: 50%;\n          transform: translate(-50%);\n        }\n      } // end title\n    } // end title-box\n  } // end top_box\n"]);return f=function(){return t},t}function g(){let t=(0,r._)(["\n  position: absolute;\n  right: 0.375rem;\n  top: 0.5rem;\n  text-align: right;\n  color: #fff;\n  h3{\n    font-size: 0.225rem;\n    color: ",";\n  }\n"]);return g=function(){return t},t}let b=o.ZP.div(f()),y=o.ZP.div(g(),p);class x extends i.PureComponent{componentDidMount(){this.setTimingFn()}setTimingFn(){this.timing=setInterval(()=>{let t=m(new Date,"yyyy-MM-dd"),e=m(new Date,"HH: mm: ss"),a=this.state.weekday[new Date().getDay()];this.setState({timeStr:"".concat(t," | ").concat(e," ").concat(a)})},1e3)}render(){return(0,n.jsx)(i.Fragment,{children:(0,n.jsx)(b,{children:(0,n.jsxs)("div",{className:"top_box",children:[(0,n.jsx)(u.Dm,{className:"top_decoration10"}),(0,n.jsxs)("div",{className:"title-box",children:[(0,n.jsx)(u.ix,{className:"top_decoration8",color:["#568aea","#000000"]}),(0,n.jsxs)("div",{className:"title",children:[(0,n.jsxs)("div",{className:"title-content",children:[(0,n.jsx)("img",{src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/log.png",alt:"Niu Dataset Logo",className:"title-logo"}),(0,n.jsx)("span",{className:"title-text",children:"Niu DataSet"})]}),(0,n.jsx)(u.Kb,{className:"title-bototm top_decoration6",reverse:!0,color:["#50e3c2","#67a1e5"]})]}),(0,n.jsx)(u.ix,{reverse:!0,className:"top_decoration8",color:["#568aea","#000000"]})]}),(0,n.jsx)(u.Dm,{className:"top_decoration10 top_decoration10_reverse"}),(0,n.jsx)(y,{children:(0,n.jsx)("h3",{children:this.state.timeStr})})]})})})}constructor(t){super(t),this.state={timeStr:"",weekday:["星期天","星期一","星期二","星期三","星期四","星期五","星期六"]}}}function w(){let t=(0,r._)(["\n  width: 6.25rem;\n  height: auto;\n  padding: 0.2rem 0.2rem 0;\n  box-sizing: border-box; /* padding包含在width内 */\n  flex-shrink: 0; /* 防止被压缩 */\n  overflow-x: hidden; /* 防止内容溢出 */\n"]);return w=function(){return t},t}function C(){let t=(0,r._)(["\n  position: relative;\n  height: 4.375rem;\n  width: 100%;\n  .left-top-borderBox12 {\n    width: inherit;\n    height: inherit;\n    padding: 0.1875rem;\n    .left-top {\n      width: 100%;\n      height: 100%;\n      border-radius: 10px;\n      background-color: rgba(19, 25, 47, 0.6);\n      .title-dis {\n        margin-top: 0.1875rem;\n        display: flex;\n        justify-content: space-around;\n        align-items: center;\n        font-size: 0.2rem;\n        color: #c0c9d2;\n        &-keyword {\n          padding-left: 0.125rem;\n          color: #47dae8;\n        }\n      }\n    }\n  }\n"]);return C=function(){return t},t}function S(){let t=(0,r._)(["\n  position: relative;\n  margin-top: 0.25rem;\n  height: 7.75rem;\n  width: 100%;\n  .left-bottom-borderBox13 {\n    width: inherit;\n    height: inherit;\n    padding: 0.25rem 0.1875rem;\n    .left-bottom {\n      width: 100%;\n      height: 100%;\n      border-radius: 10px;\n      background-color: rgba(19, 25, 47, 0.6);\n    }\n  }\n"]);return S=function(){return t},t}let v=o.ZP.div(w()),A=o.ZP.div(C()),D=o.ZP.div(S());function j(){let t=(0,r._)(["\n  padding: 0.125rem 0.125rem;\n  color: ",";\n  font-size: 0.2rem;\n  font-weight: bold;\n  .iconfont {\n    font-size: 0.175rem;\n    margin-right: 0.125rem;\n    color: #89e5ff;\n    font-weight: 400;\n  }\n"]);return j=function(){return t},t}let P=o.ZP.h3(j(),p);var I=a(76526);a(81317);class E extends i.PureComponent{async componentDidMount(){try{await this.initChart(this.el),this.setOption(this.props.option),this.addEventListeners(),window.addEventListener("resize",d(this.resize,100)),setTimeout(()=>{this.addCustomZoomListener()},100)}catch(t){console.error("Chart component mount error:",t)}}componentDidUpdate(){this.chart&&this.setOption(this.props.option)}componentWillUnmount(){this.dispose()}render(){let{width:t,height:e}=this.state;return(0,n.jsx)("div",{className:"default-chart",ref:t=>this.el=t,style:{width:t,height:e}})}constructor(t){super(t),this.initChart=t=>{let e=this.props.renderer||"canvas";return new Promise((a,n)=>{setTimeout(()=>{try{if(!t){n(Error("Chart container element is not available"));return}this.chart=I.S1(t,null,{renderer:e,width:"auto",height:"auto"}),this.chart?a():n(Error("Failed to initialize ECharts instance"))}catch(t){console.error("ECharts initialization error:",t),n(t)}},0)})},this.setOption=t=>{if(this.chart&&t)try{let e=this.props.notMerge,a=this.props.lazyUpdate;this.chart.setOption(t,e,a)}catch(t){console.error("ECharts setOption error:",t)}},this.dispose=()=>{this.chart&&(this.chart.dispose(),this.chart=null)},this.resize=()=>{this.chart&&this.chart.resize()},this.getInstance=()=>this.chart,this.addEventListeners=()=>{this.chart&&this.chart.on("click",t=>{this.props.onChartClick&&this.props.onChartClick(t)})},this.addCustomZoomListener=()=>{this.chart&&this.el&&this.el.addEventListener("wheel",t=>{let e;t.preventDefault();let a=this.chart.getOption();if(!a||!a.geo||!a.geo[0])return;let n=a.geo[0],i=n.zoom||6.5;e=t.deltaY<0?Math.min(1.2*i,15):Math.max(.8*i,5),this.chart.setOption({geo:{zoom:e,center:n.center||[118,9]}},!1)},{passive:!1})},this.state={width:"100%",height:"100%"},this.chart=null}}let k=t=>({title:{show:!1},legend:{show:!0,top:"5%",textStyle:{color:"#c0c9d2"}},tooltip:{trigger:"axis",axisPointer:{lineStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(0, 255, 233,0)"},{offset:.5,color:"rgba(255, 255, 255,1)"},{offset:1,color:"rgba(0, 255, 233,0)"}],global:!1}}}},grid:{top:"15%",left:"10%",right:"5%",bottom:"10%"},xAxis:{type:"category",axisLine:{show:!0},splitArea:{color:"#f00",lineStyle:{color:"#f00"}},axisLabel:{color:"#BCDCF0"},splitLine:{show:!1},boundaryGap:!1,data:t.timeList},yAxis:{type:"value",min:0,splitLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.1)"}},axisLine:{show:!0},axisLabel:{show:!0,margin:10,textStyle:{color:"#d1e6eb"}},axisTick:{show:!1}},series:[{name:"入库数据",type:"line",smooth:!0,lineStyle:{normal:{color:"#00b3f4",shadowColor:"rgba(0, 0, 0, .3)",shadowBlur:0,shadowOffsetY:5,shadowOffsetX:5}},label:{show:!1,position:"top",textStyle:{color:"#00b3f4"}},symbolSize:0,itemStyle:{color:"#00b3f4"},areaStyle:{normal:{color:new I.Q.o(0,0,0,1,[{offset:0,color:"rgba(0,179,244,0.3)"},{offset:1,color:"rgba(0,179,244,0)"}],!1),shadowColor:"rgba(0,179,244, 0.9)",shadowBlur:20}},data:t.outData},{name:"出库数据",type:"line",smooth:!0,lineStyle:{normal:{color:"#00ca95",shadowColor:"rgba(0, 0, 0, .3)",shadowBlur:0,shadowOffsetY:5,shadowOffsetX:5}},label:{show:!1,position:"top",textStyle:{color:"#00ca95"}},symbolSize:0,itemStyle:{color:"#00ca95"},areaStyle:{normal:{color:new I.Q.o(0,0,0,1,[{offset:0,color:"rgba(0,202,149,0.3)"},{offset:1,color:"rgba(0,202,149,0)"}],!1),shadowColor:"rgba(0,202,149, 0.9)",shadowBlur:20}},data:t.inData}]}),B=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{header:t.header,data:t.data}};class L extends i.PureComponent{render(){let{renderer:t}=this.state,{trafficSitua:e}=this.props;return(0,n.jsx)("div",{style:{width:"5.375rem",height:"3rem"},children:e&&e.timeList&&e.inData&&e.outData?(0,n.jsx)(E,{renderer:t,option:k(e)}):(0,n.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",color:"#BCDCFF",fontSize:"14px"},children:"暂无数据"})})}constructor(t){super(t),this.state={renderer:"canvas"}}}class F extends i.PureComponent{render(){let{userSitua:t}=this.props,e=t&&t.data&&t.data.length>0,a={...this.state.config,...B(t),hoverPause:!1};return(0,n.jsx)("div",{children:e?(0,n.jsx)(u.a9,{config:a,style:{width:"5.475rem",height:"6.8rem"}}):(0,n.jsxs)("div",{style:{width:"5.475rem",height:"6.8rem",overflow:"hidden"},children:[(0,n.jsxs)("div",{style:{display:"flex",backgroundColor:"#443dc5",color:"#fff",fontSize:"12px",fontWeight:"bold",height:"35px",lineHeight:"30px"},children:[(0,n.jsx)("div",{style:{width:"50px",textAlign:"center",borderRight:"1px solid #333"},children:"序号"}),(0,n.jsx)("div",{style:{width:"120px",textAlign:"center",borderRight:"1px solid #333",paddingLeft:"8px"},children:"问题"}),(0,n.jsx)("div",{style:{width:"180px",textAlign:"center",borderRight:"1px solid #333",paddingLeft:"8px"},children:"答案"}),(0,n.jsx)("div",{style:{width:"100px",textAlign:"center",paddingLeft:"8px"},children:"时间"})]}),(0,n.jsx)("div",{style:{height:"calc(100% - 35px)",display:"flex",alignItems:"center",justifyContent:"center",color:"#BCDCFF",fontSize:"14px"},children:"暂无数据"})]})})}constructor(t){super(t),this.state={config:{headerBGC:"#443dc5",oddRowBGC:"#09184F",evenRowBGC:"#070C34",index:!0,indexHeader:"序号",columnWidth:[50,120,180,80],align:["center","left","left","center"],rowNum:10,waitTime:2e3,cellStyle:{fontSize:"12px",color:"#fff",textAlign:"left",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},hoverPause:!1}}}}let N=(t,e,a)=>{let n=["layout-stable"];return t?n.push("data-loading"):e&&n.push("data-loaded"),a&&n.push("data-error"),n.join(" ")};class z extends i.PureComponent{async fetchRealData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!this.props.dataCacheService)return console.warn("数据缓存服务不可用"),this.setState({realData:this.getEmptyData(),loading:!1}),null;try{let a;this.setState({loading:!0,error:null});let n=await this.props.dataCacheService.fetchFileRecordsData(t,e);if(t)try{let n=await this.props.dataCacheService.fetchProjectDatasets(t,!0);if(e&&0===n.length)a=this.getEmptyUserSitua();else{let t=n.sort((t,e)=>new Date(e.createAt)-new Date(t.createAt));a={header:["问题","答案","时间"],data:t.slice(0,20).map(t=>[t.question||"",t.answer||"",t.createAt?new Date(t.createAt).toLocaleDateString("zh-CN"):""])}}}catch(e){console.warn("获取项目 ".concat(t," 数据集失败:"),e),a=this.getEmptyUserSitua()}else a=e?this.getEmptyUserSitua():await this.fetchAllProjectsData();let i={accessFrequency:n.avgDailyProcessing||0,peakFlow:n.peakFlow||0,trafficSitua:{timeList:n.timeList||this.generateMonthlyTimeList(),inData:n.inData||Array(6).fill(0),outData:n.outData||Array(6).fill(0)},userSitua:a||this.getEmptyUserSitua()};return this.setState({realData:i,loading:!1}),i}catch(t){return console.error("获取真实数据失败:",t),this.setState({error:t.message,loading:!1}),null}}getCountryCode(t){return({Brunei:"BN",Cambodia:"KH",Indonesia:"ID",Laos:"LA",Malaysia:"MY",Myanmar:"MM",Philippines:"PH",Singapore:"SG",Thailand:"TH",Vietnam:"VN"})[t]}async fetchAllProjectsData(){try{if(!this.props.dataCacheService)return this.getEmptyUserSitua();let{allDatasets:t}=await this.props.dataCacheService.fetchAllProjectsData(!0);if(0===t.length)return this.getEmptyUserSitua();return t.sort((t,e)=>new Date(e.createAt)-new Date(t.createAt)),{header:["问题","答案","时间"],data:t.slice(0,20).map(t=>[t.question||"",t.answer||"",t.createAt?new Date(t.createAt).toLocaleDateString("zh-CN"):""])}}catch(t){return console.warn("获取所有项目数据失败:",t),this.getEmptyUserSitua()}}getEmptyUserSitua(){return{header:["问题","答案","时间"],data:[]}}getEmptyData(){return{accessFrequency:0,peakFlow:0,trafficSitua:{timeList:this.generateMonthlyTimeList(),inData:Array(6).fill(0),outData:Array(6).fill(0)},userSitua:this.getEmptyUserSitua()}}generateMonthlyTimeList(){let t=new Date,e=t.getFullYear(),a=t.getMonth(),n=new Date(e,a+1,0).getDate(),i=[];for(let t=0;t<6;t++){let e=Math.min(Math.round(n/5*t)+1,n),r=String(a+1).padStart(2,"0"),o=String(e).padStart(2,"0");i.push("".concat(r,"/").concat(o))}return i}componentDidMount(){let{selectedCountry:t,countryProjectMap:e}=this.props;if(t&&e){let a=e[t];a?this.fetchRealData(a,t):this.fetchRealData(null,t)}else this.fetchRealData()}componentDidUpdate(t){let{selectedCountry:e,countryProjectMap:a}=this.props;if(e!==t.selectedCountry){if(e&&a){let t=a[e];t?this.fetchRealData(t,e):this.fetchRealData(null,e)}else this.fetchRealData()}}render(){let{loading:t,error:e}=this.state,{userSitua:a,trafficSitua:i,accessFrequency:r,peakFlow:o}=this.getDataByCountry(),s=N(t,!t&&!e,e);return(0,n.jsxs)(v,{className:s,children:[(0,n.jsx)(A,{children:(0,n.jsx)(u.XK,{className:"left-top-borderBox12",children:(0,n.jsxs)("div",{className:"left-top",children:[(0,n.jsxs)(P,{children:[(0,n.jsx)("i",{className:"iconfont",children:""}),(0,n.jsx)("span",{children:"本月数据态势"})]}),(0,n.jsxs)("div",{className:"title-dis",children:[(0,n.jsxs)("span",{children:["平均处理数据(天):",(0,n.jsxs)("span",{className:"title-dis-keyword stable-text",children:[r,"M"]})]}),(0,n.jsxs)("span",{children:["数据峰值:",(0,n.jsxs)("span",{className:"title-dis-keyword stable-text",children:[o,"M"]})]})]}),(0,n.jsx)(L,{trafficSitua:i})]})})}),(0,n.jsx)(D,{children:(0,n.jsx)(u._6,{className:"left-bottom-borderBox13",children:(0,n.jsxs)("div",{className:"left-bottom",children:[(0,n.jsxs)(P,{children:[(0,n.jsx)("i",{className:"iconfont",children:""}),(0,n.jsx)("span",{children:"最新项目数据"})]}),(0,n.jsx)(F,{userSitua:a})]})})})]})}constructor(t){super(t),this.getDataByCountry=()=>{let{realData:t,loading:e,error:a}=this.state;return e?{accessFrequency:"--",peakFlow:"--",trafficSitua:{timeList:this.generateMonthlyTimeList(),outData:[0,0,0,0,0,0],inData:[0,0,0,0,0,0]},userSitua:{header:["问题","答案","时间"],data:Array.from({length:10},(t,e)=>["加载中...","数据加载中，请稍候...","--:--"])}}:a?{accessFrequency:0,peakFlow:0,trafficSitua:{timeList:this.generateMonthlyTimeList(),outData:[0,0,0,0,0,0],inData:[0,0,0,0,0,0]},userSitua:{header:["问题","答案","时间"],data:[["数据加载失败",a,""]]}}:t||this.getEmptyData()},this.state={realData:null,loading:!1,error:null}}}function M(){let t=(0,r._)(["\n  margin-top: 0.25rem;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%; /* 使用父容器分配的11.5rem宽度 */\n  box-sizing: border-box; /* 确保所有尺寸计算正确 */\n  overflow-x: hidden; /* 防止中间区域产生横向滚动 */\n"]);return M=function(){return t},t}function T(){let t=(0,r._)(["\n    display: flex;\n    margin-bottom: 0.25rem;\n    margin-top: 0.875rem;\n    width: 100%;\n    height: 3.25rem; /* 精确高度 */\n\n    .detail-list {\n        display: flex;\n        flex-wrap: wrap;\n        align-items: center;\n        align-content: space-between;\n        justify-content: space-around;\n        width: 100%;\n\n        &-item {\n            display: flex;\n            align-items: center;\n            position: relative;\n            height: 1.5625rem;\n            padding: 0 0.1rem;\n            width: 32%;\n            box-sizing: border-box; /* 确保padding包含在width内 */\n            border-radius: 5px;\n            border: 1px solid #343f4b;\n            background-color: rgba(19, 25, 47, 0.8);\n\n            img {\n                width: 1.25rem;\n                height: 1.25rem;\n            }\n\n            .detail-item-text {\n                margin-left: 0.1rem;\n              \n                h3 {\n                    color: #bcdcff;\n                    font-size: 16px;\n                    margin-bottom: 0.25rem;\n                    margin-top: 0;\n                    line-height: 1.2;\n                }\n\n                span {\n                    font-weight: 500;\n                    font-size: 0.25rem;\n                    background: linear-gradient(to bottom, #fff, #4db6e5);\n                    color: transparent;\n                    -webkit-background-clip: text;\n                    background-clip: text;\n                    display: inline;\n                }\n\n                .unit {\n                    font-size: 0.2rem;\n                    margin-left: 0.125rem;\n                    display: inline;\n                }\n            }\n        }\n    }\n"]);return T=function(){return t},t}let R=o.ZP.div(M()),O=o.ZP.div(T()),q={type:"FeatureCollection",features:[{type:"Feature",properties:{name:"中国"},geometry:{type:"Polygon",coordinates:[[[73.66,53.56],[134.77,53.56],[134.77,18.16],[73.66,18.16],[73.66,53.56]]]}}]},U=!1,J=async()=>{if(U)return!0;try{console.log("正在加载完整的亚洲地图数据...");let t=await fetch("https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson");if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));let e=await t.json();if(!e||!e.features||!Array.isArray(e.features))throw Error("Invalid GeoJSON format");let a=["China","Japan","South Korea","North Korea","Mongolia","India","Pakistan","Bangladesh","Sri Lanka","Nepal","Bhutan","Myanmar","Thailand","Laos","Vietnam","Cambodia","Malaysia","Singapore","Indonesia","Philippines","Brunei","East Timor","Afghanistan","Iran","Iraq","Syria","Lebanon","Jordan","Israel","Palestine","Saudi Arabia","Yemen","Oman","United Arab Emirates","Qatar","Bahrain","Kuwait","Turkey","Cyprus","Georgia","Armenia","Azerbaijan","Kazakhstan","Kyrgyzstan","Tajikistan","Turkmenistan","Uzbekistan","Russia"],n={type:"FeatureCollection",features:e.features.filter(t=>a.includes(t.properties.NAME)||a.includes(t.properties.name)||a.includes(t.properties.NAME_EN))};return I.je("asia",n),U=!0,console.log("亚洲地图数据加载成功，包含",n.features.length,"个国家"),!0}catch(t){return console.warn("加载完整地图数据失败，使用备用数据:",t.message),I.je("asia",q),U=!0,!1}},V=["Brunei","Cambodia","Indonesia","Laos","Malaysia","Myanmar","Philippines","Singapore","Thailand","Vietnam"],W=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:V,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;arguments.length>3&&void 0!==arguments[3]&&arguments[3];let n=[];return["China","Japan","South Korea","North Korea","Mongolia","India","Pakistan","Bangladesh","Sri Lanka","Nepal","Bhutan","Myanmar","Thailand","Laos","Vietnam","Cambodia","Malaysia","Singapore","Indonesia","Philippines","Brunei","East Timor","Afghanistan","Iran","Iraq","Syria","Lebanon","Jordan","Israel","Palestine","Saudi Arabia","Yemen","Oman","United Arab Emirates","Qatar","Bahrain","Kuwait","Turkey","Cyprus","Georgia","Armenia","Azerbaijan","Kazakhstan","Kyrgyzstan","Tajikistan","Turkmenistan","Uzbekistan","Russia"].forEach(i=>{let r=t.includes(i),o=e[i]||{},s=["China","Japan","South Korea","India"].includes(i),l=a===i;if(r){let t=V.includes(i);n.push({name:i,itemStyle:{normal:{areaColor:l?"rgba(102,105,240,.8)":o.color||"rgba(145, 147, 240, 0.1)",borderColor:l?"#FFD700":o.borderColor||"rgba(255,209,163,.6)",borderWidth:l?2:o.borderWidth||1,shadowBlur:l?10:0,shadowColor:l?"rgba(255,215,0,.8)":"transparent"},emphasis:{areaColor:o.emphasisColor||"rgba(102,105,240,.7)",borderColor:o.emphasisBorderColor||"#FFD1A3",borderWidth:2}},label:{show:t,color:"#fff",fontSize:11,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.8)",textBorderWidth:1,textShadowColor:"rgba(0,0,0,0.8)",textShadowBlur:3,textShadowOffsetX:1,textShadowOffsetY:1,formatter:function(t){return({Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印尼",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"})[t.name]||t.name},emphasis:{show:t,color:"#FFD700",fontSize:13,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.9)",textBorderWidth:2,textShadowColor:"rgba(0,0,0,0.9)",textShadowBlur:5}}})}else s?n.push({name:i,silent:!0,itemStyle:{normal:{areaColor:"transparent",borderColor:"transparent",borderWidth:0},emphasis:{areaColor:"transparent",borderColor:"transparent"}}}):n.push({name:i,silent:!0,itemStyle:{normal:{areaColor:"rgba(15,15,15,.01)",borderColor:"rgba(35,35,35, .05)",borderWidth:.3},emphasis:{areaColor:"rgba(15,15,15,.01)",borderColor:"rgba(35,35,35, .05)"}}})}),n},K=async function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};await J();let n=(null==t?void 0:t.highlightCountries)||V,i=(null==t?void 0:t.countryData)||{},r=(null==t?void 0:t.showAseanOnly)!==!1;return null==t||t.showLabels,{title:{show:!1,text:e?({Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印度尼西亚",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"})[e]||e:"",subtext:(null==t?void 0:t.subtitle)||"",left:"center",top:10,textStyle:{color:"#fff",fontSize:16},subtextStyle:{color:"#999",fontSize:12}},tooltip:{show:!0,trigger:"item",formatter:function(t){let e={文莱:"Brunei",柬埔寨:"Cambodia",印度尼西亚:"Indonesia",老挝:"Laos",马来西亚:"Malaysia",缅甸:"Myanmar",菲律宾:"Philippines",新加坡:"Singapore",泰国:"Thailand",越南:"Vietnam"}[t.name]||t.name,n={Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印度尼西亚",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"}[e]||t.name,i=a[e]||{};return'\n        <div style="padding: 8px;">\n          <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #FFD700;">'.concat(n,'</div>\n          <div style="margin-bottom: 4px;">\uD83D\uDCCA 数据集数量: <span style="color: #4FC3F7;">').concat(i.datasets||0,'</span></div>\n          <div style="margin-bottom: 4px;">❓ 问题数量: <span style="color: #81C784;">').concat((i.questions||0).toLocaleString(),'</span></div>\n          <div style="margin-bottom: 4px;">\uD83D\uDE80 项目数量: <span style="color: #FFB74D;">').concat(i.projects||0,"</span></div>\n          ").concat(i.lastUpdated?'<div style="margin-top: 6px; font-size: 12px; color: #999;">更新时间: '.concat(new Date(i.lastUpdated).toLocaleString(),"</div>"):"","\n        </div>\n      ")},backgroundColor:"rgba(0,0,0,0.9)",borderColor:"#FFD1A3",borderWidth:1,textStyle:{color:"#fff",fontSize:14},extraCssText:"border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);"},legend:{show:!1},geo:{map:"asia",label:{show:!0,color:"#fff",fontSize:11,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.8)",textBorderWidth:1,textShadowColor:"rgba(0,0,0,0.8)",textShadowBlur:3,textShadowOffsetX:1,textShadowOffsetY:1,formatter:function(t){return["Brunei","Cambodia","Indonesia","Laos","Malaysia","Myanmar","Philippines","Singapore","Thailand","Vietnam"].includes(t.name)?({Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印尼",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"})[t.name]||t.name:""},emphasis:{show:!0,color:"#FFD700",fontSize:13,fontWeight:"bold",textBorderColor:"rgba(0,0,0,0.9)",textBorderWidth:2,textShadowColor:"rgba(0,0,0,0.9)",textShadowBlur:5,formatter:function(t){return["Brunei","Cambodia","Indonesia","Laos","Malaysia","Myanmar","Philippines","Singapore","Thailand","Vietnam"].includes(t.name)?({Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印尼",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"})[t.name]||t.name:""}}},regions:W(r?V:n,i,e),center:[118,9],zoom:8.5,aspectScale:.75,roam:!1,scaleLimit:{min:5,max:15},layoutCenter:["50%","50%"],layoutSize:"80%",itemStyle:{normal:{borderColor:"rgba(255,209,163, .5)",areaColor:"rgba(73,86,166,.2)",borderWidth:.5,shadowBlur:5,shadowColor:"rgba(107,91,237,.7)"},emphasis:{borderColor:"#FFD1A3",areaColor:"rgba(102,105,240,.4)",borderWidth:1,shadowBlur:5,shadowColor:"rgba(135,138,255,.5)"}}},series:[{type:"map",map:"asia",geoIndex:0,data:[{name:"Brunei",value:1},{name:"Cambodia",value:1},{name:"Indonesia",value:1},{name:"Laos",value:1},{name:"Malaysia",value:1},{name:"Myanmar",value:1},{name:"Philippines",value:1},{name:"Singapore",value:1},{name:"Thailand",value:1},{name:"Vietnam",value:1}],showLegendSymbol:!1}]}};class G extends i.PureComponent{async componentDidMount(){await this.loadMapOptions()}async componentDidUpdate(t){(t.mapData!==this.props.mapData||t.selectedCountry!==this.props.selectedCountry)&&await this.loadMapOptions()}async loadMapOptions(){let{mapData:t,selectedCountry:e,globalStatistics:a}=this.props;if(!t){this.setState({mapOption:null,loading:!1});return}try{this.setState({loading:!0});let n=await K(t,e,a);this.setState({mapOption:n,loading:!1})}catch(t){console.error("加载地图配置失败:",t),this.setState({mapOption:null,loading:!1})}}render(){let{renderer:t,mapOption:e,loading:a}=this.state,{mapData:i}=this.props,r={width:"10.625rem",height:"8.125rem",position:"relative",backgroundColor:"transparent"},o={...r,display:"flex",alignItems:"center",justifyContent:"center",color:"#fff",fontSize:"0.2rem",textAlign:"center"};return i?a?(0,n.jsx)("div",{style:o,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{marginBottom:"0.1rem"},children:"\uD83C\uDF0D"}),(0,n.jsx)("div",{children:"正在加载地图数据..."})]})}):e?(0,n.jsx)("div",{style:r,children:(0,n.jsx)(E,{renderer:t,option:e,onChartClick:this.handleChartClick})}):(0,n.jsx)("div",{style:o,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{marginBottom:"0.1rem"},children:"⚠️"}),(0,n.jsx)("div",{children:"地图数据加载失败"})]})}):(0,n.jsx)("div",{style:o,children:(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{style:{marginBottom:"0.1rem"},children:"\uD83D\uDCCD"}),(0,n.jsx)("div",{children:"暂无地图数据"})]})})}constructor(t){super(t),this.handleChartClick=t=>{this.props.onCountryClick&&t.name&&this.props.onCountryClick(t.name)},this.state={renderer:"canvas",mapOption:null,loading:!0}}}class Y extends i.PureComponent{componentDidMount(){this.props.globalDashboard&&this.setState({globalRealData:this.props.globalDashboard})}componentDidUpdate(t){t.globalDashboard!==this.props.globalDashboard&&this.props.globalDashboard&&this.setState({globalRealData:this.props.globalDashboard})}getEmptyDetailsList(){return[{title:"文献生成问题",number:0,unit:"个"},{title:"问题生成数据集",number:0,unit:"个"},{title:"问题总数",number:0,unit:"个"},{title:"数据集总数",number:0,unit:"个"},{title:"导入文件总数",number:0,unit:"个"},{title:"导出文件总数",number:0,unit:"个"}]}render(){let t;let{selectedCountry:e,currentDataType:a,countrySpecificData:i,globalRealData:r,loading:o}=this.state,{globalStatistics:s}=this.props,l=this.generateAseanMapData();return t="country"===a&&e?i[e]||this.getEmptyDetailsList():r?this.formatGlobalData(r):this.getEmptyDetailsList(),(0,n.jsxs)(R,{children:[(0,n.jsx)(G,{mapData:l,selectedCountry:e,onCountryClick:this.handleCountryClick,globalStatistics:s}),(0,n.jsx)(O,{children:(0,n.jsx)("div",{className:"detail-list",children:o?Array.from({length:6},(t,e)=>(0,n.jsxs)("div",{className:"detail-list-item",children:[(0,n.jsx)("img",{src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/center-details-data".concat(e+1,".png"),alt:"加载中",style:{opacity:.5,width:"1.25rem",height:"1.25rem"}}),(0,n.jsxs)("div",{className:"detail-item-text",children:[(0,n.jsx)("h3",{children:"..."}),(0,n.jsx)("span",{children:"--"}),(0,n.jsx)("span",{className:"unit",children:"--"})]})]},"loading-".concat(e))):t&&t.length>0?t.map((t,e)=>(0,n.jsxs)("div",{className:"detail-list-item",children:[(0,n.jsx)("img",{src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/center-details-data".concat(e+1,".png"),alt:t.title,style:{width:"1.25rem",height:"1.25rem"}}),(0,n.jsxs)("div",{className:"detail-item-text",children:[(0,n.jsx)("h3",{children:t.title}),(0,n.jsx)("span",{children:t.number}),(0,n.jsx)("span",{className:"unit",children:t.unit})]})]},e)):Array.from({length:6},(t,e)=>(0,n.jsxs)("div",{className:"detail-list-item empty-item",children:[(0,n.jsx)("img",{src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/center-details-data".concat(e+1,".png"),alt:"暂无数据",style:{opacity:.3,width:"1.25rem",height:"1.25rem"}}),(0,n.jsxs)("div",{className:"detail-item-text",children:[(0,n.jsx)("h3",{children:"暂无数据"}),(0,n.jsx)("span",{children:"--"}),(0,n.jsx)("span",{className:"unit",children:"--"})]})]},"empty-".concat(e)))})})]})}constructor(t){super(t),this.loadGlobalData=async()=>{if(!this.props.dataCacheService){console.warn("数据缓存服务不可用");return}try{this.setState({loading:!0});let t=await this.props.dataCacheService.fetchDashboardData();this.setState({globalRealData:t})}catch(t){console.error("加载全局统计数据出错:",t)}finally{this.setState({loading:!1})}},this.generateAseanMapData=()=>({moveLines:[],title:"东盟十国地图",subtitle:"",showAseanOnly:!0,countryData:{Brunei:{color:"rgba(73,86,166,.4)",borderColor:"rgba(255,209,163, .8)",emphasisColor:"rgba(102,105,240,.6)"},Cambodia:{color:"rgba(73,86,166,.4)",borderColor:"rgba(255,209,163, .8)",emphasisColor:"rgba(102,105,240,.6)"},Indonesia:{color:"rgba(73,86,166,.5)",borderColor:"rgba(255,209,163, .9)",emphasisColor:"rgba(102,105,240,.7)"},Laos:{color:"rgba(73,86,166,.4)",borderColor:"rgba(255,209,163, .8)",emphasisColor:"rgba(102,105,240,.6)"},Malaysia:{color:"rgba(73,86,166,.5)",borderColor:"rgba(255,209,163, .9)",emphasisColor:"rgba(102,105,240,.7)"},Myanmar:{color:"rgba(73,86,166,.4)",borderColor:"rgba(255,209,163, .8)",emphasisColor:"rgba(102,105,240,.6)"},Philippines:{color:"rgba(73,86,166,.5)",borderColor:"rgba(255,209,163, .9)",emphasisColor:"rgba(102,105,240,.7)"},Singapore:{color:"rgba(73,86,166,.6)",borderColor:"rgba(255,209,163, 1)",emphasisColor:"rgba(102,105,240,.8)"},Thailand:{color:"rgba(73,86,166,.5)",borderColor:"rgba(255,209,163, .9)",emphasisColor:"rgba(102,105,240,.7)"},Vietnam:{color:"rgba(73,86,166,.5)",borderColor:"rgba(255,209,163, .9)",emphasisColor:"rgba(102,105,240,.7)"}}}),this.handleCountryClick=t=>{let{selectedCountry:e}=this.state,{onCountrySelect:a}=this.props;e===t?(this.setState({selectedCountry:null,currentDataType:"asean"}),a&&a(null)):(this.setState({selectedCountry:t,currentDataType:"country"}),this.loadCountryData(t),a&&a(t))},this.loadCountryData=d(async t=>{if(!this.props.dataCacheService){console.warn("数据缓存服务不可用");return}try{this.setState({loading:!0});let e=await this.props.dataCacheService.fetchDashboardData(t),a=this.formatCountryData(e);this.setState({countrySpecificData:{...this.state.countrySpecificData,[t]:a},loading:!1})}catch(a){console.error("加载国家统计数据出错:",a);let e=this.generateCountrySpecificData();this.setState({countrySpecificData:{...this.state.countrySpecificData,[t]:e},loading:!1})}},300),this.formatCountryData=t=>[{title:"文献生成问题",number:t.documentToQuestionCount,unit:"个"},{title:"问题生成数据集",number:t.questionToDatasetCount,unit:"个"},{title:"问题总数",number:t.questionCount,unit:"个"},{title:"数据集总数",number:t.datasetCount,unit:"个"},{title:"导入文件总数",number:t.importFileCount,unit:"个"},{title:"导出文件总数",number:t.exportFileCount,unit:"个"}],this.formatGlobalData=t=>[{title:"文献生成问题",number:t.documentToQuestionCount,unit:"个"},{title:"问题生成数据集",number:t.questionToDatasetCount,unit:"个"},{title:"问题总数",number:t.questionCount,unit:"个"},{title:"数据集总数",number:t.datasetCount,unit:"个"},{title:"导入文件总数",number:t.importFileCount,unit:"个"},{title:"导出文件总数",number:t.exportFileCount,unit:"个"}],this.generateCountrySpecificData=()=>this.getEmptyDetailsList(),this.getCountryDisplayName=t=>({Brunei:"文莱",Cambodia:"柬埔寨",Indonesia:"印度尼西亚",Laos:"老挝",Malaysia:"马来西亚",Myanmar:"缅甸",Philippines:"菲律宾",Singapore:"新加坡",Thailand:"泰国",Vietnam:"越南"})[t]||t,this.state={selectedCountry:null,currentDataType:"asean",countrySpecificData:{},globalRealData:null,loading:!1}}}let Q=t=>({radar:{center:["50%","50%"],radius:"70%",name:{formatter:function(t){return["{a|"+t+"}"].join("\n")},textStyle:{rich:{a:{color:"#BCDCFF",fontSize:14,fontWeight:600,fontFamily:"Source Han Sans CN"}}}},nameGap:5,indicator:t.indicator,splitLine:{show:!1},axisLine:{show:!1},splitArea:{areaStyle:{color:["rgba(84,136,255, 0.05)","rgba(84,136,255, 0.1)","rgba(84,136,255, 0.2)","rgba(84,136,255, 0.3)","rgba(84,136,255, 0.4)","rgba(84,136,255, 0.5)"].reverse(),shadowColor:"rgba(0, 0, 0, .5)",shadowBlur:5,shadowOffsetX:10,shadowOffsetY:10}}},series:[{name:"用户浏览类别",type:"radar",data:[t.data],label:{show:!1,formatter:function(t){return t.value+"万"},color:"#9ae8ac",distance:10,align:"right"},symbol:"none",symbolSize:[6,6],lineStyle:{color:"rgba(160,159,246, 0.6)",width:2},areaStyle:{color:"rgba(114,113,233,.4)",opacity:.8,shadowColor:"rgba(115,149,255,1)",shadowBlur:10}}]}),X=t=>{let e;let a=t.value||t.number;if("string"==typeof a&&a.includes("/")){let[t,n]=a.split("/").map(Number);e=n>0?Math.round(t/n*100):0}else e=a;return{title:{text:"".concat(e,"%"),left:"45%",top:"40%",textAlign:"center",textStyle:{fontSize:"16",fontWeight:"500",color:"#909dff",textAlign:"center"}},series:[{type:"pie",startAngle:0,radius:["80%","70%"],center:["50%","50%"],data:[{value:e,name:t.title,itemStyle:{normal:{color:new I.Q.o(0,0,0,1,[{offset:0,color:"#5a8bfa"},{offset:1,color:"#831bdb"}]),shadowColor:"rgba(175,143,230,.5)",shadowBlur:10}},label:{show:!1},labelLine:{normal:{smooth:!0,lineStyle:{width:0}}},hoverAnimation:!1},{label:{show:!1},labelLine:{normal:{smooth:!0,lineStyle:{width:0}}},value:100-e,hoverAnimation:!0,itemStyle:{color:"rgba(79,76,192, 0.3)"}}]}]}},H=t=>({color:["#73A0FA","#73DEB3","#32C5E9","#67E0E3"],tooltip:{trigger:"axis",axisPointer:{type:"cross",crossStyle:{color:"#999"},lineStyle:{type:"dashed"}}},grid:{left:"15",right:"15",bottom:"10",top:"30",containLabel:!0},legend:{data:["问题","数据集","已归类","已确认"],show:!0,textStyle:{color:"#BCDCFF"}},xAxis:[{type:"category",data:t.xData,axisLabel:{color:"#BCDCF0",textStyle:{fontSize:12}},splitLine:{show:!1},axisTick:{show:!0},axisLine:{show:!1},boundaryGap:!0},{type:"category",axisLabel:{color:"#BCDCF0",textStyle:{fontSize:12}},splitLine:{show:!1},axisTick:{show:!0},axisLine:{show:!1},boundaryGap:!0}],yAxis:[{type:"value",name:"",nameTextStyle:{color:"#BCDCFF"},axisLabel:{color:"#BCDCF0",textStyle:{fontSize:12}},splitLine:{show:!0,lineStyle:{color:"#252938"}},axisTick:{show:!0},axisLine:{show:!0}},{type:"value",min:0,max:100,interval:20,name:"",splitLine:{show:!1},axisLine:{lineStyle:{color:"#94b5ca"}}}],series:[{name:"问题",type:"line",data:t.data1,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2}},{name:"数据集",type:"line",data:t.data2,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2}},{name:"已归类",type:"line",data:t.data3,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2}},{name:"已确认",type:"line",data:t.data4,smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:2}}]});class Z extends i.PureComponent{render(){let{renderer:t}=this.state,{browseCategories:e}=this.props;return(0,n.jsx)("div",{style:{width:"5.375rem",height:"2.5rem"},children:e&&e.data&&e.indicator?(0,n.jsx)(E,{renderer:t,option:Q(e)}):(0,n.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",color:"#BCDCFF",fontSize:"14px"},children:"暂无数据"})})}constructor(t){super(t),this.state={renderer:"canvas"}}}class _ extends i.PureComponent{render(){let{userIdentityCategory:t}=this.props;if(console.log("UserIdentityCategory data:",t),!(t&&t.length>0&&t.some(t=>t.value>0)))return(0,n.jsx)("div",{style:{width:"5.85rem",height:"2.625rem",display:"flex",alignItems:"center",justifyContent:"center",color:"#BCDCFF",fontSize:"14px"},children:"暂无数据"});let e={unit:"（条）",showValue:!0,data:t};return console.log("CapsuleChart config:",e),(0,n.jsx)("div",{children:(0,n.jsx)(u.md,{config:e,style:{width:"5.85rem",height:"2.625rem"}})})}constructor(t){super(t),this.state={config:{unit:"（条）",showValue:!1,data:[]}}}}class $ extends i.PureComponent{render(){var t,e,a;let{renderer:i}=this.state,{offlinePortalData:r}=this.props,o=r&&r.xData&&r.data1&&(r.data1.some(t=>t>0)||(null===(t=r.data2)||void 0===t?void 0:t.some(t=>t>0))||(null===(e=r.data3)||void 0===e?void 0:e.some(t=>t>0))||(null===(a=r.data4)||void 0===a?void 0:a.some(t=>t>0)));return(0,n.jsx)("div",{style:{width:"5.375rem",height:"2.5rem"},children:o?(0,n.jsx)(E,{renderer:i,option:H(r)}):(0,n.jsx)("div",{style:{width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",color:"#BCDCFF",fontSize:"14px"},children:"暂无数据"})})}constructor(t){super(t),this.state={renderer:"canvas"}}}class tt extends i.PureComponent{render(){let{renderer:t}=this.state,{FeedbackData:e}=this.props;return(0,n.jsx)("div",{style:{width:"1.25rem",height:"1.25rem"},children:(0,n.jsx)(E,{renderer:t,option:X(e)})})}constructor(t){super(t),this.state={renderer:"canvas",option:""}}}function te(){let t=(0,r._)(["\n  width: 6.25rem;\n  height: auto;\n  padding: 0 0.2rem;\n  box-sizing: border-box; /* padding包含在width内 */\n  flex-shrink: 0; /* 防止被压缩 */\n  overflow-x: hidden; /* 防止内容溢出 */\n"]);return te=function(){return t},t}function ta(){let t=(0,r._)(["\n  position: relative;\n  height: 3rem;\n  width: 100%;\n  margin-bottom: 0.25rem;\n  .right-top {\n    &-content {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-top: 0.1875rem;\n    }\n    .earth-gif {\n      width: 2.75rem;\n      height: auto;\n      border-radius: 50%;\n      overflow: hidden;\n    }\n  }\n"]);return ta=function(){return t},t}function tn(){let t=(0,r._)(["\n  position: relative;\n  height: 3.125rem;\n  width: 100%;\n  margin-bottom: 0.25rem;\n"]);return tn=function(){return t},t}function ti(){let t=(0,r._)(["\n  position: relative;\n  height: 6rem;\n  width: 100%;\n\n  .right-bottom-borderBox13 {\n    width: inherit;\n    height: inherit;\n    padding: 0.25rem 0.1875rem 0.1875rem;\n\n    .right-bottom {\n      width: 100%;\n      height: 100%;\n      border-radius: 10px;\n      background-color: rgba(19, 25, 47, 0.6);\n\n      .feedback-box {\n        margin-top: 0.1rem;\n        display: flex;\n        align-items: center;\n        justify-content: space-around;\n\n        &-item {\n          display: flex;\n          align-items: center;\n          flex-direction: column;\n          height: 1.75rem;\n\n          .dis-text {\n            font-weight: bold;\n            margin-top: 0.0625rem;\n            color: #b2cfee;\n            font-size: 0.2rem;\n            background: linear-gradient(to bottom, #fff, #6176F4);\n            color: transparent;\n            -webkit-background-clip: text;\n            background-clip: text;\n          }\n        }\n      }\n\n      .offline-portal-box {\n        margin-top: 0.125rem;\n      }\n    }\n  }\n"]);return ti=function(){return t},t}let tr=o.ZP.div(te()),to=o.ZP.div(ta()),ts=o.ZP.div(tn()),tl=o.ZP.div(ti());class tc extends i.PureComponent{getTagDisplayName(t){return t?"object"==typeof t?t.displayName||t.label:t:null}findTopLevelTag(t,e){if(!t||!t.parentId)return t;let a=e.get(t.parentId);return this.findTopLevelTag(a,e)}calculateDomainStats(t,e){let a={},n=new Map,i=new Map;return e.forEach(t=>{n.set(t.label,t),i.set(t.id,t)}),t.forEach(t=>{if(!t.questionLabel||"待分类"===t.questionLabel||"未分类"===t.questionLabel)return;let e=n.get(t.questionLabel),r=null;if(e&&(r=this.findTopLevelTag(e,i)),r){let t=this.getTagDisplayName(r);t&&(a[t]=(a[t]||0)+1)}else console.warn("未找到标签: ".concat(t.questionLabel))}),a}calculateGlobalDomainStats(t,e){let a={},n=new Map;e.forEach(t=>{n.has(t.projectId)||n.set(t.projectId,[]),n.get(t.projectId).push(t)});let i=new Map;return t.forEach(t=>{i.has(t.projectId)||i.set(t.projectId,[]),i.get(t.projectId).push(t)}),n.forEach((t,e)=>{let n=i.get(e)||[];Object.entries(this.calculateDomainStats(n,t)).forEach(t=>{let[e,n]=t;a[e]=(a[e]||0)+n})}),a}async fetchRealData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;try{if(this.state.loading||this.setState({loading:!0,error:null}),t){let e,a,n;try{if(this.props.dataCacheService)[e,a]=await Promise.all([this.props.dataCacheService.fetchProjectDatasets(t),this.props.dataCacheService.fetchProjectTags(t)]);else{let[n,i]=await Promise.all([fetch("/api/projects/".concat(t,"/datasets/export")),fetch("/api/projects/".concat(t,"/distill/tags/all"))]);e=n.ok?await n.json():[],a=i.ok?await i.json():[]}}catch(n){console.warn("获取项目 ".concat(t," 数据失败:"),n),e=[],a=[]}if(!e||0===e.length)return this.setState({realData:null,loading:!1}),null;let i=await fetch("/api/projects/".concat(t,"/statistics")),r=0;i.ok&&(r=(await i.json()).questionCount||0);let o=e.length,s=e.filter(t=>1===t.confirmed||!0===t.confirmed).length,l=this.calculateDomainStats(e,a),c=Object.keys(l).length,h=e.filter(t=>t.questionLabel&&"待分类"!==t.questionLabel&&"未分类"!==t.questionLabel).length,d=Object.entries(l).sort((t,e)=>{let[,a]=t,[,n]=e;return n-a}).slice(0,6),m=Object.values(l).length>0?Math.max(...Object.values(l)):100,u={data:d.map(t=>{let[e,a]=t;return a||0}),indicator:d.map(t=>{let[e,a]=t;return{name:e||"未知领域",max:m}})};for(;u.data.length<6;)u.data.push(0),u.indicator.push({name:"",max:m});let p=d.map(t=>{let[e,a]=t;return{name:e,value:a}}),f=r>0?Math.round(s/o*r):0;try{if(this.props.dataCacheService)n=await this.props.dataCacheService.fetchTimelineData(t,6);else{let e=await fetch("/api/statistics/timeline?projectId=".concat(t,"&months=6"));n=e.ok?await e.json():null}}catch(t){console.warn("获取时间序列数据失败:",t),n=null}n||(n={xData:["2月","3月","4月","5月","6月","7月"],data1:[0,0,0,0,0,0],data2:[0,0,0,0,0,0],data3:[0,0,0,0,0,0],data4:[0,0,0,0,0,0]});let g={totalDatasets:o,confirmedDatasets:s,domainCount:c,browseCategories:u,userIdentityCategory:p,offline:{feedback:[{title:"总数据集",value:"".concat(o,"/").concat(r)},{title:"已确认数量",value:"".concat(s,"/").concat(f)},{title:"已归类数量",value:"".concat(h,"/").concat(o)}],offlinePortalData:{data1:n.data1,data2:n.data2,data3:n.data3,data4:n.data4,xData:n.xData}}};return this.setState({realData:g,loading:!1}),g}{let t,e,a;try{if(this.props.dataCacheService){let a=await this.props.dataCacheService.fetchAllProjectsData();t=a.allDatasets,e=a.allTags}else{let a=await fetch("/api/projects").then(t=>t.json());if(0===a.length)throw Error("没有可用的项目数据");for(let n of(t=[],e=[],a))try{let[a,i]=await Promise.all([fetch("/api/projects/".concat(n.id,"/datasets/export")).then(t=>t.ok?t.json():[]),fetch("/api/projects/".concat(n.id,"/distill/tags/all")).then(t=>t.ok?t.json():[])]),r=a.map(t=>({...t,projectId:t.projectId||n.id}));t=t.concat(r),e=e.concat(i)}catch(t){console.warn("获取项目 ".concat(n.id," 数据失败:"),t)}}}catch(a){console.error("获取所有项目数据失败:",a),t=[],e=[]}let n=0;if(this.props.dataCacheService)try{n=(await this.props.dataCacheService.fetchDashboardData()).questionCount||0}catch(e){console.warn("从缓存获取仪表板数据失败，使用直接API调用:",e);let t=await fetch("/api/statistics/dashboard");t.ok&&(n=(await t.json()).questionCount||0)}else{let t=await fetch("/api/statistics/dashboard");t.ok&&(n=(await t.json()).questionCount||0)}let i=t.length,r=t.filter(t=>1===t.confirmed||!0===t.confirmed).length,o=this.calculateGlobalDomainStats(t,e),s=Object.keys(o).length,l=t.filter(t=>t.questionLabel&&"待分类"!==t.questionLabel&&"未分类"!==t.questionLabel).length,c=Object.entries(o).sort((t,e)=>{let[,a]=t,[,n]=e;return n-a}).slice(0,6),h=Object.values(o).length>0?Math.max(...Object.values(o)):100,d={data:c.map(t=>{let[e,a]=t;return a||0}),indicator:c.map(t=>{let[e,a]=t;return{name:e||"未知领域",max:h}})};for(;d.data.length<6;)d.data.push(0),d.indicator.push({name:"",max:h});let m=c.map(t=>{let[e,a]=t;return{name:e||"未知领域",value:a||0}}),u=n>0?Math.round(r/i*n):0;try{if(this.props.dataCacheService)a=await this.props.dataCacheService.fetchTimelineData(null,6);else{let t=await fetch("/api/statistics/timeline?months=6");a=t.ok?await t.json():null}}catch(t){console.warn("获取全局时间序列数据失败:",t),a=null}a||(a={xData:["2月","3月","4月","5月","6月","7月"],data1:[0,0,0,0,0,0],data2:[0,0,0,0,0,0],data3:[0,0,0,0,0,0],data4:[0,0,0,0,0,0]});let p={totalDatasets:i,confirmedDatasets:r,domainCount:s,browseCategories:d,userIdentityCategory:m,offline:{feedback:[{title:"总数据集",value:"".concat(i,"/").concat(n)},{title:"已确认数量",value:"".concat(r,"/").concat(u)},{title:"已归类数量",value:"".concat(l,"/").concat(i)}],offlinePortalData:{data1:a.data1,data2:a.data2,data3:a.data3,data4:a.data4,xData:a.xData}}};return this.setState({realData:p,loading:!1}),p}}catch(t){return console.error("获取真实数据失败:",t),this.setState({error:t.message,loading:!1}),null}}componentDidMount(){let{selectedCountry:t,countryProjectMap:e}=this.props;if(t&&e){let a=e[t];a?this.fetchRealData(a):this.setState({realData:null,loading:!1,error:null})}else this.fetchRealData()}componentDidUpdate(t){let{selectedCountry:e,countryProjectMap:a}=this.props;if(e!==t.selectedCountry){if(this.setState({loading:!0,error:null,realData:null}),e&&a){let t=a[e];t?this.fetchRealData(t):this.setState({realData:null,loading:!1,error:null})}else this.fetchRealData()}}getEmptyData(){return{browseCategories:{data:[0,0,0,0,0,0],indicator:[{name:"暂无数据",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100}]},userIdentityCategory:[{name:"暂无数据",value:0}],offline:{feedback:[{title:"总数据集",value:"0/0"},{title:"已确认数量",value:"0/0"},{title:"已归类数量",value:"0/0"}],offlinePortalData:{data1:[0,0,0,0,0,0],data2:[0,0,0,0,0,0],data3:[0,0,0,0,0,0],data4:[0,0,0,0,0,0],xData:["2月","3月","4月","5月","6月","7月"]}}}}render(){let{selectedCountry:t}=this.props,{loading:e,error:a}=this.state,{offline:i,browseCategories:r,userIdentityCategory:o}=this.getDataByCountry(t),s=N(e,!e&&!a,a);return(0,n.jsxs)(tr,{className:s,children:[(0,n.jsx)(to,{children:(0,n.jsxs)("div",{className:"right-top",children:[(0,n.jsxs)(P,{children:[(0,n.jsx)("i",{className:"iconfont",children:""}),(0,n.jsx)("span",{children:"数据集领域分布"})]}),(0,n.jsxs)("div",{className:"right-top-content",children:[(0,n.jsx)(Z,{browseCategories:r}),(0,n.jsx)("img",{alt:"地球",className:"earth-gif",src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/earth-rotate.gif"})]})]})}),(0,n.jsxs)(ts,{children:[(0,n.jsx)(P,{children:(0,n.jsx)("span",{})}),(0,n.jsx)(_,{userIdentityCategory:o})]}),(0,n.jsx)(tl,{children:(0,n.jsx)(u._6,{className:"right-bottom-borderBox13",children:(0,n.jsxs)("div",{className:"right-bottom",children:[(0,n.jsxs)(P,{children:[(0,n.jsx)("i",{className:"iconfont",children:""}),(0,n.jsx)("span",{children:"数据统计概览"})]}),(0,n.jsx)("div",{className:"feedback-box",children:i?i.feedback.map((t,e)=>(0,n.jsxs)("div",{className:"feedback-box-item",children:[(0,n.jsx)(tt,{FeedbackData:t}),(0,n.jsx)("span",{className:"dis-text stable-text",children:t.title})]},e)):""}),(0,n.jsx)("div",{className:"offline-portal-box",children:i?(0,n.jsx)($,{offlinePortalData:i.offlinePortalData}):""})]})})})]})}constructor(t){super(t),this.getDataByCountry=t=>{let{realData:e,loading:a,error:n}=this.state;return a?{browseCategories:{data:[0,0,0,0,0,0],indicator:[{name:"数据加载中",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100}]},userIdentityCategory:[{name:"加载中...",value:0},{name:"",value:0},{name:"",value:0},{name:"",value:0}],offline:{feedback:[{title:"总数据集",value:"--"},{title:"已确认数量",value:"--"},{title:"已归类数量",value:"--"}],offlinePortalData:{data1:[0,0,0,0,0,0],data2:[0,0,0,0,0,0],data3:[0,0,0,0,0,0],data4:[0,0,0,0,0,0],xData:["2月","3月","4月","5月","6月","7月"]}}}:!t&&e?{...e,userIdentityCategory:e.userIdentityCategory||[]}:n?{browseCategories:{data:[0,0,0,0,0,0],indicator:[{name:"加载失败",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100},{name:"",max:100}]},userIdentityCategory:[{name:"加载失败",value:0}],offline:{feedback:[{title:"总数据集",value:"数据加载失败"},{title:"已确认数量",value:"数据加载失败"},{title:"已归类数量",value:"数据加载失败"}],offlinePortalData:{data1:[0,0,0,0,0,0],data2:[0,0,0,0,0,0],data3:[0,0,0,0,0,0],data4:[0,0,0,0,0,0],xData:["2月","3月","4月","5月","6月","7月"]}}}:t&&e||this.getEmptyData()},this.state={realData:null,loading:!0,error:null}}}class th{getCacheKey(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e?"".concat(t,"_").concat(e):"".concat(t,"_global")}isCacheValid(t){return!!t&&Date.now()-t.timestamp<this.CACHE_DURATION}setCache(t,e){this.cache.set(t,{data:e,timestamp:Date.now()})}getCache(t){let e=this.cache.get(t);return this.isCacheValid(e)?e.data:null}async fetchProjects(){let t=this.getCacheKey("projects"),e=this.getCache(t);if(e)return console.log("使用缓存的项目数据"),e;if(this.loadingPromises.has(t))return this.loadingPromises.get(t);let a=this._fetchProjectsFromAPI();this.loadingPromises.set(t,a);try{let e=await a;return this.setCache(t,e),e}finally{this.loadingPromises.delete(t)}}async _fetchProjectsFromAPI(){let t=await fetch("/api/projects");if(!t.ok)throw Error("获取项目数据失败: ".concat(t.status));return t.json()}async fetchGlobalStatistics(){let t=this.getCacheKey("globalStats"),e=this.getCache(t);if(e)return console.log("使用缓存的全局统计数据"),e;if(this.loadingPromises.has(t))return this.loadingPromises.get(t);let a=this._fetchGlobalStatisticsFromAPI();this.loadingPromises.set(t,a);try{let e=await a;return this.setCache(t,e),e}finally{this.loadingPromises.delete(t)}}async _fetchGlobalStatisticsFromAPI(){let t=await fetch("/api/statistics/global");if(!t.ok)throw Error("获取全局统计数据失败: ".concat(t.status));return t.json()}async fetchDashboardData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=this.getCacheKey("dashboard",t),a=this.getCache(e);if(a)return console.log("使用缓存的仪表板数据: ".concat(t||"全局")),a;if(this.loadingPromises.has(e))return this.loadingPromises.get(e);let n=this._fetchDashboardDataFromAPI(t);this.loadingPromises.set(e,n);try{let t=await n;return this.setCache(e,t),t}finally{this.loadingPromises.delete(e)}}async _fetchDashboardDataFromAPI(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=t?"/api/statistics/dashboard?country=".concat(encodeURIComponent(t)):"/api/statistics/dashboard",a=await fetch(e);if(!a.ok)throw Error("获取仪表板数据失败: ".concat(a.status));return a.json()}async fetchFileRecordsData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=this.getCacheKey("fileRecords",t||e),n=this.getCache(a);if(n)return n;if(this.loadingPromises.has(a))return this.loadingPromises.get(a);let i=this._fetchFileRecordsDataFromAPI(t,e);this.loadingPromises.set(a,i);try{let t=await i;return this.setCache(a,t),t}finally{this.loadingPromises.delete(a)}}async fetchProjectDatasets(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=this.getCacheKey("datasets","".concat(t,"_").concat(e||"all")),n=this.getCache(a);if(n)return n;if(this.loadingPromises.has(a))return this.loadingPromises.get(a);let i=this._fetchProjectDatasetsFromAPI(t,e);this.loadingPromises.set(a,i);try{let t=await i;return this.setCache(a,t),t}finally{this.loadingPromises.delete(a)}}async fetchProjectTags(t){let e=this.getCacheKey("tags",t),a=this.getCache(e);if(a)return a;if(this.loadingPromises.has(e))return this.loadingPromises.get(e);let n=this._fetchProjectTagsFromAPI(t);this.loadingPromises.set(e,n);try{let t=await n;return this.setCache(e,t),t}finally{this.loadingPromises.delete(e)}}async fetchTimelineData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=this.getCacheKey("timeline","".concat(t||"global","_").concat(e)),n=this.getCache(a);if(n)return n;if(this.loadingPromises.has(a))return this.loadingPromises.get(a);let i=this._fetchTimelineDataFromAPI(t,e);this.loadingPromises.set(a,i);try{let t=await i;return this.setCache(a,t),t}finally{this.loadingPromises.delete(a)}}async _fetchFileRecordsDataFromAPI(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a="/api/statistics/file-records",n=new URLSearchParams;if(t)n.append("projectId",t);else if(e){let t=this.countryNameToCode[e];t&&n.append("country",t)}n.toString()&&(a+="?".concat(n.toString()));let i=await fetch(a);if(!i.ok)throw Error("获取文件记录数据失败: ".concat(i.status));return i.json()}async _fetchProjectDatasetsFromAPI(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a="/api/projects/".concat(t,"/datasets/export");null!==e&&(a+="?confirmed=".concat(e));let n=await fetch(a);if(!n.ok)throw Error("获取项目数据集失败: ".concat(n.status));return n.json()}async _fetchProjectTagsFromAPI(t){let e=await fetch("/api/projects/".concat(t,"/tags"));if(!e.ok)throw Error("获取项目标签树失败: ".concat(e.status));return(await e.json()).tags||[]}async _fetchTimelineDataFromAPI(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a="/api/statistics/timeline?months=".concat(e);t&&(a+="&projectId=".concat(t));let n=await fetch(a);if(!n.ok)throw Error("获取时间序列数据失败: ".concat(n.status));return n.json()}async fetchAllProjectsData(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=this.getCacheKey("allProjectsData",t||"all"),a=this.getCache(e);if(a)return a;if(this.loadingPromises.has(e))return this.loadingPromises.get(e);let n=this._fetchAllProjectsDataFromAPI(t);this.loadingPromises.set(e,n);try{let t=await n;return this.setCache(e,t),t}finally{this.loadingPromises.delete(e)}}async _fetchAllProjectsDataFromAPI(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=await this.fetchProjects(),a=[],n=[],i=e.map(async e=>{try{let[a,n]=await Promise.all([this.fetchProjectDatasets(e.id,t),this.fetchProjectTags(e.id)]);return{datasets:a.map(t=>({...t,projectId:t.projectId||e.id})),tags:n}}catch(t){return console.warn("获取项目 ".concat(e.id," 数据失败:"),t),{datasets:[],tags:[]}}});return(await Promise.all(i)).forEach(t=>{let{datasets:e,tags:i}=t;a=a.concat(e),n=n.concat(i)}),{allDatasets:a,allTags:n}}async preloadAllData(){try{let[t,e,a,n]=await Promise.all([this.fetchProjects(),this.fetchGlobalStatistics(),this.fetchDashboardData(),this.fetchTimelineData()]),i=this.buildCountryProjectMap(t);return this.preloadCountriesDataAsync(i),this.preloadProjectsDataAsync(t),{projects:t,globalStats:e,globalDashboard:a,globalTimeline:n,countryProjectMap:i}}catch(t){throw console.error("预加载数据失败:",t),t}}buildCountryProjectMap(t){let e={};return t.forEach(t=>{if(t.country){let a=Object.keys(this.countryNameToCode).find(e=>this.countryNameToCode[e]===t.country);a&&(e[a]=t.id)}}),e}async preloadCountriesDataAsync(t){let e=this.ASEAN_COUNTRIES.map(async e=>{try{let a=t[e];await Promise.all([this.fetchDashboardData(e),this.fetchFileRecordsData(a,e),a?this.fetchTimelineData(a):Promise.resolve()])}catch(t){console.warn("".concat(e," 数据预加载失败:"),t)}});await Promise.allSettled(e)}async preloadProjectsDataAsync(t){let e=[this.fetchAllProjectsData(!0),this.fetchAllProjectsData()];t.forEach(t=>{e.push(this.fetchProjectDatasets(t.id,!0),this.fetchProjectDatasets(t.id),this.fetchProjectTags(t.id))}),await Promise.allSettled(e)}clearCache(){this.cache.clear(),this.loadingPromises.clear()}getCacheStatus(){let t={totalItems:this.cache.size,loadingItems:this.loadingPromises.size,items:{}};for(let[e,a]of this.cache.entries())t.items[e]={timestamp:a.timestamp,age:Date.now()-a.timestamp,valid:this.isCacheValid(a)};return t}constructor(){this.cache=new Map,this.loadingPromises=new Map,this.CACHE_DURATION=6e5,this.ASEAN_COUNTRIES=["Brunei","Cambodia","Indonesia","Laos","Malaysia","Myanmar","Philippines","Singapore","Thailand","Vietnam"],this.countryNameToCode={Brunei:"BN",Cambodia:"KH",Indonesia:"ID",Laos:"LA",Malaysia:"MY",Myanmar:"MM",Philippines:"PH",Singapore:"SG",Thailand:"TH",Vietnam:"VN"}}}let td=new th,tm=()=>(0,n.jsx)("button",{style:{position:"absolute",left:"0.125rem",top:"0.25rem",zIndex:1e3,margin:"8px",backgroundColor:"transparent",border:"none",cursor:"pointer"},onClick:()=>window.location.href="/projects",children:(0,n.jsx)("img",{src:"https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/begin.png",alt:"返回项目页面",style:{height:"40px"}})});class tu extends i.Component{async componentDidMount(){try{this.setState({loading:!0});let{projects:t,globalStats:e,globalDashboard:a,globalTimeline:n,countryProjectMap:i}=await td.preloadAllData();this.setState({projects:t,countryProjectMap:i,globalStatistics:e,globalDashboard:a,globalTimeline:n,loading:!1,dataReady:!0})}catch(t){console.error("获取数据失败:",t),this.setState({loading:!1,dataReady:!0})}}render(){let{selectedCountry:t,countryProjectMap:e,globalStatistics:a,globalDashboard:i,globalTimeline:r,loading:o,dataReady:s}=this.state;return o||!s?(0,n.jsx)(c,{children:(0,n.jsx)("div",{style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",height:"100vh",color:"#fff",fontSize:"18px"},children:(0,n.jsx)("div",{style:{marginBottom:"20px"},children:"正在加载..."})})}):(0,n.jsxs)(c,{children:[(0,n.jsx)(tm,{}),(0,n.jsx)(x,{}),(0,n.jsxs)(h,{children:[(0,n.jsx)(z,{selectedCountry:t,countryProjectMap:e,dataCacheService:td}),(0,n.jsx)(Y,{className:"center-page",onCountrySelect:this.handleCountrySelect,globalStatistics:a,globalDashboard:i,dataCacheService:td}),(0,n.jsx)(tc,{selectedCountry:t,countryProjectMap:e,globalTimeline:r,dataCacheService:td})]})]})}constructor(t){super(t),this.handleCountrySelect=t=>{this.setState({selectedCountry:t})},this.state={selectedCountry:null,projects:[],countryProjectMap:{},globalStatistics:{},globalDashboard:{},globalTimeline:{},loading:!0,dataReady:!1}}}function tp(){let t=(0,r._)(['\n@font-face {font-family: "iconfont";\n  src: url(\'/iconfont.eot?t=1600868300505\'); /* IE9 */\n  src: url(\'/iconfont.eot?t=1600868300505#iefix\') format(\'embedded-opentype\'), /* IE6-IE8 */\n  url(\'data:application/x-font-woff2;charset=utf-8;base64,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\') format(\'woff2\'),\n  url(\'/iconfont.woff?t=1600868300505\') format(\'woff\'),\n  url(\'/iconfont.ttf?t=1600868300505\') format(\'truetype\'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */\n  url(\'/iconfont.svg?t=1600868300505#iconfont\') format(\'svg\'); /* iOS 4.1- */\n}\n\n.iconfont {\n  font-family: "iconfont" !important;\n  font-size: 16px;\n  font-style: normal;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.icon-supervise:before {\n  content: "e777";\n}\n\n.icon-chart-pie-alt:before {\n  content: "e78c";\n}\n\n.icon-chart-area:before {\n  content: "e78f";\n}\n\n.icon-chart-line:before {\n  content: "e790";\n}\n\n.icon-chart-bar:before {\n  content: "e791";\n}\n\n.icon-laptop:before {\n  content: "e797";\n}\n\n.icon-layer-group:before {\n  content: "e7f7";\n}\n\n.icon-lock:before {\n  content: "e7fb";\n}\n\n.icon-align-left:before {\n  content: "e7fd";\n}\n\n.icon-border-bottom:before {\n  content: "e7fe";\n}\n\n.icon-clouddownload:before {\n  content: "e81b";\n}\n\n.icon-cloudupload:before {\n  content: "e81c";\n}\n\n.icon-rank:before {\n  content: "e86a";\n}\n\n.icon-early-warning:before {\n  content: "e86e";\n}\n\n.icon-vector:before {\n  content: "e888";\n}\n\n.icon-monitoring:before {\n  content: "e88e";\n}\n\n.icon-diagnose:before {\n  content: "e88f";\n}\n\n.icon-Directory-tree:before {\n  content: "e892";\n}\n\n.icon-application:before {\n  content: "e89e";\n}\n'],['\r\n@font-face {font-family: "iconfont";\r\n  src: url(\'/iconfont.eot?t=1600868300505\'); /* IE9 */\r\n  src: url(\'/iconfont.eot?t=1600868300505#iefix\') format(\'embedded-opentype\'), /* IE6-IE8 */\r\n  url(\'data:application/x-font-woff2;charset=utf-8;base64,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\') format(\'woff2\'),\r\n  url(\'/iconfont.woff?t=1600868300505\') format(\'woff\'),\r\n  url(\'/iconfont.ttf?t=1600868300505\') format(\'truetype\'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */\r\n  url(\'/iconfont.svg?t=1600868300505#iconfont\') format(\'svg\'); /* iOS 4.1- */\r\n}\r\n\r\n.iconfont {\r\n  font-family: "iconfont" !important;\r\n  font-size: 16px;\r\n  font-style: normal;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n.icon-supervise:before {\r\n  content: "\\e777";\r\n}\r\n\r\n.icon-chart-pie-alt:before {\r\n  content: "\\e78c";\r\n}\r\n\r\n.icon-chart-area:before {\r\n  content: "\\e78f";\r\n}\r\n\r\n.icon-chart-line:before {\r\n  content: "\\e790";\r\n}\r\n\r\n.icon-chart-bar:before {\r\n  content: "\\e791";\r\n}\r\n\r\n.icon-laptop:before {\r\n  content: "\\e797";\r\n}\r\n\r\n.icon-layer-group:before {\r\n  content: "\\e7f7";\r\n}\r\n\r\n.icon-lock:before {\r\n  content: "\\e7fb";\r\n}\r\n\r\n.icon-align-left:before {\r\n  content: "\\e7fd";\r\n}\r\n\r\n.icon-border-bottom:before {\r\n  content: "\\e7fe";\r\n}\r\n\r\n.icon-clouddownload:before {\r\n  content: "\\e81b";\r\n}\r\n\r\n.icon-cloudupload:before {\r\n  content: "\\e81c";\r\n}\r\n\r\n.icon-rank:before {\r\n  content: "\\e86a";\r\n}\r\n\r\n.icon-early-warning:before {\r\n  content: "\\e86e";\r\n}\r\n\r\n.icon-vector:before {\r\n  content: "\\e888";\r\n}\r\n\r\n.icon-monitoring:before {\r\n  content: "\\e88e";\r\n}\r\n\r\n.icon-diagnose:before {\r\n  content: "\\e88f";\r\n}\r\n\r\n.icon-Directory-tree:before {\r\n  content: "\\e892";\r\n}\r\n\r\n.icon-application:before {\r\n  content: "\\e89e";\r\n}\r\n']);return tp=function(){return t},t}let tf=(0,o.vJ)(tp());function tg(){let t=(0,r._)(["\n    html, body, div, span, applet, object, iframe,\n    h1, h2, h3, h4, h5, h6, p, blockquote, pre,\n    a, abbr, acronym, address, big, cite, code,\n    del, dfn, em, img, ins, kbd, q, s, samp,\n    small, strike, strong, sub, sup, tt, var,\n    b, u, i, center,\n    dl, dt, dd, ol, ul, li,\n    fieldset, form, label, legend,\n    table, caption, tbody, tfoot, thead, tr, th, td,\n    article, aside, canvas, details, embed,\n    figure, figcaption, footer, header, hgroup,\n    menu, nav, output, ruby, section, summary,\n    time, mark, audio, video {\n        margin: 0;\n        padding: 0;\n        border: 0;\n        font-size: 100%;\n        font: inherit;\n        vertical-align: baseline;\n        box-sizing: border-box;\n    }\n\n    article, aside, details, figcaption, figure,\n    footer, header, hgroup, menu, nav, section {\n        display: block;\n    }\n\n    body {\n        line-height: 1;\n        overflow-x: hidden !important; /* 禁用横向滚动条 */\n        overflow-y: auto !important;   /* 允许竖向滚动条 */\n        max-width: 100vw !important;   /* 最大宽度 */\n    }\n\n    /* \uD83D\uDD11 强制所有元素不超出视口宽度 */\n    html {\n        overflow-x: hidden !important;\n        max-width: 100vw !important;\n    }\n\n    ol, ul {\n        list-style: none;\n    }\n\n    blockquote, q {\n        quotes: none;\n    }\n\n    blockquote:before, blockquote:after,\n    q:before, q:after {\n        content: '';\n        content: none;\n    }\n\n    table {\n        border-collapse: collapse;\n        border-spacing: 0;\n    }\n    \n    /* \uD83D\uDD11 关键修复：回归原项目的简洁全局样式，移除所有复杂的响应式和适配代码 */\n"]);return tg=function(){return t},t}let tb=(0,o.vJ)(tg());var ty=()=>(0,n.jsxs)("div",{style:{width:"100%",minHeight:"100vh",overflowX:"hidden",boxSizing:"border-box"},children:[(0,n.jsx)(tf,{}),(0,n.jsx)(tb,{}),(0,n.jsx)(tu,{})]})},40450:function(){!function(t,e){var a,n=t.document,i=n.documentElement,r=n.querySelector('meta[name="viewport"]'),o=n.querySelector('meta[name="flexible"]'),s=0,l=0,c=e.flexible||(e.flexible={});if(r){console.warn("将根据已有的meta标签来设置缩放比例");var h=r.getAttribute("content").match(/initial\-scale=([\d\.]+)/);h&&(s=parseInt(1/(l=parseFloat(h[1])),10))}else if(o){var d=o.getAttribute("content");if(d){var m=d.match(/initial\-dpr=([\d\.]+)/),u=d.match(/maximum\-dpr=([\d\.]+)/);m&&(l=parseFloat((1/(s=parseFloat(m[1]))).toFixed(2))),u&&(l=parseFloat((1/(s=parseFloat(u[1]))).toFixed(2)))}}if(!s&&!l){t.navigator.appVersion.match(/android/gi);var p=t.navigator.appVersion.match(/iphone/gi),f=t.devicePixelRatio;l=1/(s=p?f>=3&&(!s||s>=3)?3:f>=2&&(!s||s>=2)?2:1:1)}if(i.setAttribute("data-dpr",s),!r){if((r=n.createElement("meta")).setAttribute("name","viewport"),r.setAttribute("content","initial-scale="+l+", maximum-scale="+l+", minimum-scale="+l+", user-scalable=no"),i.firstElementChild)i.firstElementChild.appendChild(r);else{var g=n.createElement("div");g.appendChild(r),n.write(g.innerHTML)}}function b(){var e=i.getBoundingClientRect().width,a=e/s;a<1366?e=1366*s:a>2560&&(e=2560*s);var n=e/24;n=Math.max(60,Math.min(n,120)),i.style.fontSize=n+"px",c.rem=t.rem=n,i.setAttribute("data-screen-type","external")}t.addEventListener("resize",function(){clearTimeout(a),a=setTimeout(b,300)},!1),t.addEventListener("pageshow",function(t){t.persisted&&(clearTimeout(a),a=setTimeout(b,300))},!1),"complete"===n.readyState?n.body.style.fontSize=12*s+"px":n.addEventListener("DOMContentLoaded",function(t){n.body.style.fontSize=12*s+"px"},!1),b(),c.dpr=t.dpr=s,c.refreshRem=b,c.rem2px=function(t){var e=parseFloat(t)*this.rem;return"string"==typeof t&&t.match(/rem$/)&&(e+="px"),e},c.px2rem=function(t){var e=parseFloat(t)/this.rem;return"string"==typeof t&&t.match(/px$/)&&(e+="rem"),e}}(window,window.lib||(window.lib={}))}}]);