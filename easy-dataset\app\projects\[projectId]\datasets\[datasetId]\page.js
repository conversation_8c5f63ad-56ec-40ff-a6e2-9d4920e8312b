'use client';

import { Container, Box, Paper, Alert, Typography, Snackbar, IconButton, Button } from '@mui/material';
import ChunkViewDialog from '@/components/text-split/ChunkViewDialog';
import DatasetHeader from '@/components/datasets/DatasetHeader';
import DatasetMetadata from '@/components/datasets/DatasetMetadata';
import EditableField from '@/components/datasets/EditableField';
import DomainTagSelector from '@/components/datasets/DomainTagSelector';
import OptimizeDialog from '@/components/datasets/OptimizeDialog';
import useDatasetDetails from '@/app/projects/[projectId]/datasets/[datasetId]/useDatasetDetails';
import { useTranslation } from 'react-i18next';
import EditIcon from '@mui/icons-material/Edit';

/**
 * 数据集详情页面
 */
export default function DatasetDetailsPage({ params }) {
    const { projectId, datasetId } = params;

    const { t } = useTranslation();
    // 使用自定义Hook管理状态和逻辑
    const {
        currentDataset,
        loading,
        editingAnswer,
        editingCot,
        editingQuestion,
        editingLabel,
        editingInstruction,
        answerValue,
        cotValue,
        questionValue,
        labelValue,
        instructionValue,
        snackbar,
        confirming,
        optimizeDialog,
        viewDialogOpen,
        viewChunk,
        datasetsAllCount,
        datasetsConfirmCount,
        answerTokens,
        cotTokens,
        shortcutsEnabled,
        setShortcutsEnabled,
        setSnackbar,
        setAnswerValue,
        setCotValue,
        setQuestionValue,
        setLabelValue,
        setInstructionValue,
        setEditingAnswer,
        setEditingCot,
        setEditingQuestion,
        setEditingLabel,
        setEditingInstruction,
        handleNavigate,
        handleConfirm,
        handleUnconfirm, // 新增取消确认函数
        handleSave,
        handleDelete,
        handleOpenOptimizeDialog,
        handleCloseOptimizeDialog,
        handleOptimize,
        handleViewChunk,
        handleCloseViewDialog
    } = useDatasetDetails(projectId, datasetId);

    // 加载状态
    if (loading) {
        return (
            <Container maxWidth="lg" sx={{ mt: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
                    <Alert severity="info">{t('datasets.loadingDataset')}</Alert>
                </Box>
            </Container>
        );
    }

    // 无数据状态
    if (!currentDataset) {
        return (
            <Container maxWidth="lg" sx={{ mt: 4 }}>
                <Alert severity="error">{t('datasets.datasetNotFound')}</Alert>
            </Container>
        );
    }

    return (
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
            {/* 顶部导航栏 */}
            <DatasetHeader
                projectId={projectId}
                datasetsAllCount={datasetsAllCount}
                datasetsConfirmCount={datasetsConfirmCount}
                confirming={confirming}
                currentDataset={currentDataset}
                shortcutsEnabled={shortcutsEnabled}
                setShortcutsEnabled={setShortcutsEnabled}
                onNavigate={handleNavigate}
                onConfirm={handleConfirm}
                onUnconfirm={handleUnconfirm} // 传递取消确认函数
                onDelete={handleDelete}
            />

            {/* 主要内容 */}
            <Paper sx={{ p: 3 }}>
                {/* 问题字段 - 可编辑 */}
                <EditableField
                    label={t('datasets.question')}
                    value={questionValue}
                    editing={editingQuestion}
                    onEdit={() => setEditingQuestion(true)}
                    onChange={e => setQuestionValue(e.target.value)}
                    onSave={() => handleSave('question', questionValue)}
                    onCancel={() => {
                        setEditingQuestion(false);
                        setQuestionValue(currentDataset.question);
                    }}
                />

                {/* 领域标签字段 - 使用DomainTagSelector组件 */}
                <DomainTagSelector
                    projectId={projectId}
                    value={labelValue}
                    onChange={newValue => setLabelValue(newValue)}
                    editing={editingLabel}
                    editButton={!editingLabel && (
                        <IconButton size="small" onClick={() => setEditingLabel(true)}>
                            <EditIcon fontSize="small" />
                        </IconButton>
                    )}
                />
                {editingLabel && (
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
                        <Button variant="outlined" onClick={() => {
                            setEditingLabel(false);
                            setLabelValue(currentDataset.questionLabel || '');
                        }}>
                            {t('common.cancel')}
                        </Button>
                        <Button variant="contained" onClick={() => {
                            handleSave('questionLabel', labelValue);
                            setEditingLabel(false);
                        }}>
                            {t('common.save')}
                        </Button>
                    </Box>
                )}

                <EditableField
                    label={t('datasets.answer')}
                    value={answerValue}
                    editing={editingAnswer}
                    onEdit={() => setEditingAnswer(true)}
                    onChange={e => setAnswerValue(e.target.value)}
                    onSave={() => handleSave('answer', answerValue)}
                    onCancel={() => {
                        setEditingAnswer(false);
                        setAnswerValue(currentDataset.answer);
                    }}
                    onOptimize={handleOpenOptimizeDialog}
                    tokenCount={answerTokens}
                />

                <EditableField
                    label={t('datasets.instruction')}
                    value={instructionValue}
                    editing={editingInstruction}
                    onEdit={() => setEditingInstruction(true)}
                    onChange={e => setInstructionValue(e.target.value)}
                    onSave={() => handleSave('instruction', instructionValue)}
                    onCancel={() => {
                        setEditingInstruction(false);
                        setInstructionValue(currentDataset.instruction || '');
                    }}
                />

                <EditableField
                    label={t('datasets.cot')}
                    value={cotValue}
                    editing={editingCot}
                    onEdit={() => setEditingCot(true)}
                    onChange={e => setCotValue(e.target.value)}
                    onSave={() => handleSave('cot', cotValue)}
                    onCancel={() => {
                        setEditingCot(false);
                        setCotValue(currentDataset.cot || '');
                    }}
                    tokenCount={cotTokens}
                />

                <DatasetMetadata currentDataset={currentDataset} onViewChunk={handleViewChunk} />
            </Paper>

            {/* 消息提示 */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={2000}
                onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
            >
                <Alert
                    onClose={() => setSnackbar(prev => ({ ...prev, open: false }))}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>

            {/* AI优化对话框 */}
            <OptimizeDialog
                open={optimizeDialog.open}
                onClose={handleCloseOptimizeDialog}
                onConfirm={handleOptimize}
                loading={optimizeDialog.loading}
            />

            {/* 文本块详情对话框 */}
            <ChunkViewDialog open={viewDialogOpen} chunk={viewChunk} onClose={handleCloseViewDialog} />
        </Container>
    );
}
