/**
 * OSS实现
 * 移除所有不必要的抽象层和复杂逻辑
 */

import OSS from 'ali-oss';
import { createHash } from 'crypto';
import { createUploadFileInfo, getUploadFileInfoById, delUploadFileInfoById } from '@/lib/db/upload-files';
import { createFileRecord } from '@/lib/db/file-records';
import { generateFileRecordData } from '@/lib/util/file-record-helper';
import { FILE_RECORD } from '@/constant';

// OSS客户端单例
let ossClient = null;

/**
 * 获取OSS客户端
 */
function getOSSClient() {
  if (!ossClient) {
    ossClient = new OSS({
      region: process.env.OSS_REGION,
      accessKeyId: process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
      bucket: process.env.OSS_BUCKET_DOCUMENTS,
      endpoint: process.env.OSS_ENDPOINT,
      secure: true,
      timeout: 60000
    });
  }
  return ossClient;
}

/**
 * 生成OSS文件路径
 */
function generateOSSPath(projectId, fileName) {
  const env = process.env.OSS_ENVIRONMENT || 'dev';
  return `${env}/projects/${projectId}/files/${fileName}`;
}

/**
 * 计算文件MD5
 */
function calculateMD5(buffer) {
  return createHash('md5').update(buffer).digest('hex');
}

/**
 * 获取文件MIME类型
 */
function getMimeType(fileName) {
  const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  const mimeTypes = {
    '.md': 'text/markdown',
    '.pdf': 'application/pdf'
  };
  return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * 上传文献文件 - 极简版
 */
export async function uploadDocument(projectId, fileName, fileBuffer) {
  try {
    // 1. 基本验证
    if (!fileName || !fileBuffer || fileBuffer.length === 0) {
      throw new Error('Invalid file');
    }

    // 2. 文件类型验证 - 只支持Markdown和PDF
    const ext = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    if (ext !== '.md' && ext !== '.pdf') {
      throw new Error(`Unsupported file type: ${ext}. Only Markdown (.md) and PDF (.pdf) files are supported`);
    }

    // 3. 生成路径和计算信息
    const ossPath = generateOSSPath(projectId, fileName);
    const md5 = calculateMD5(fileBuffer);
    const fileExt = fileName.substring(fileName.lastIndexOf('.'));

    // 4. 直接上传到OSS
    const client = getOSSClient();
    const result = await client.put(ossPath, fileBuffer, {
      headers: {
        'Content-Type': getMimeType(fileName),
        'Cache-Control': 'private, no-cache'
      },
      meta: {
        projectId: projectId,
        uploadTime: new Date().toISOString()
      }
    });

    // 5. 保存到数据库
    const fileInfo = await createUploadFileInfo({
      projectId,
      fileName,
      size: fileBuffer.length,
      md5,
      fileExt,
      path: ossPath
    });

    // 5. 创建文件记录
    await createFileRecord(generateFileRecordData({
      projectId,
      fileName,
      originalName: fileName,
      fileBuffer,
      operationType: FILE_RECORD.OPERATION_TYPE.UPLOAD,
      filePath: ossPath,
      description: `上传文献文件: ${fileName}`
    }));

    return {
      success: true,
      fileId: fileInfo.id,
      fileName,
      filePath: ossPath,
      size: fileBuffer.length,
      md5,
      url: result.url
    };

  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
}

/**
 * 下载文献文件 - 极简版
 */
export async function downloadDocument(fileId) {
  try {
    // 1. 获取文件信息
    const fileInfo = await getUploadFileInfoById(fileId);
    if (!fileInfo) {
      throw new Error('File not found');
    }

    // 2. 从OSS下载
    const client = getOSSClient();
    const result = await client.get(fileInfo.path);

    // 3. 根据文件类型返回适当的内容格式
    const isTextFile = fileInfo.fileName.endsWith('.md') || fileInfo.fileName.endsWith('.txt');

    return {
      fileId,
      fileName: fileInfo.fileName,
      content: isTextFile ? result.content.toString('utf-8') : result.content,
      size: result.content.length
    };

  } catch (error) {
    console.error('Download failed:', error);
    throw error;
  }
}

/**
 * 获取文件签名URL - 极简版
 */
export async function getDocumentUrl(fileId, expires = 3600) {
  try {
    const fileInfo = await getUploadFileInfoById(fileId);
    if (!fileInfo) {
      throw new Error('File not found');
    }

    const client = getOSSClient();
    const url = client.signatureUrl(fileInfo.path, {
      expires,
      method: 'GET'
    });

    return {
      fileId,
      fileName: fileInfo.fileName,
      url,
      expires
    };

  } catch (error) {
    console.error('Get URL failed:', error);
    throw error;
  }
}

/**
 * 删除文献文件 - 极简版
 */
export async function deleteDocument(projectId, fileId) {
  try {
    // 1. 获取文件信息
    const fileInfo = await getUploadFileInfoById(fileId);
    if (!fileInfo) {
      throw new Error('File not found');
    }

    // 2. 从OSS删除
    const client = getOSSClient();
    await client.delete(fileInfo.path);

    // 3. 从数据库删除
    const result = await delUploadFileInfoById(fileId);

    return {
      success: true,
      fileName: fileInfo.fileName,
      ...result
    };

  } catch (error) {
    console.error('Delete failed:', error);
    throw error;
  }
}

/**
 * 检查文件是否存在 - 极简版
 */
export async function checkDocumentExists(fileId) {
  try {
    const fileInfo = await getUploadFileInfoById(fileId);
    if (!fileInfo) {
      return { exists: false, reason: 'Database record not found' };
    }

    const client = getOSSClient();
    try {
      await client.head(fileInfo.path);
      return { exists: true, fileId, fileName: fileInfo.fileName };
    } catch (error) {
      return { exists: false, reason: 'File not found in OSS' };
    }

  } catch (error) {
    return { exists: false, reason: error.message };
  }
}
