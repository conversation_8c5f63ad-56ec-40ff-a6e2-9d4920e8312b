<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>东盟十国数据可视化</title>
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="styles/charts.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background-color, #f5f5f5);
        }
        
        .container {
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .main-content {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .map-container {
            flex: 1;
            min-width: 300px;
        }
        
        .data-panel {
            flex: 1;
            min-width: 300px;
            display: none; /* 初始隐藏，点击国家后显示 */
            opacity: 0;
            transform: translateX(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        .data-panel.active {
            opacity: 1;
            transform: translateX(0);
        }
        
        #asean-map {
            width: 100%;
            height: 500px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }
        
        .country-info {
            padding: 15px;
            margin-bottom: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        
        .country-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--primary-color, #1976d2);
        }
        
        .chart-wrapper {
            margin-bottom: 20px;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        .chart-wrapper:nth-child(2) {
            transition-delay: 0.1s;
        }
        
        .chart-wrapper:nth-child(3) {
            transition-delay: 0.2s;
        }
        
        .chart-wrapper:nth-child(4) {
            transition-delay: 0.3s;
        }
        
        .data-panel.active .chart-wrapper {
            opacity: 1;
            transform: translateY(0);
        }
        
        .controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        select, button {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
            background-color: #fff;
        }
        
        button {
            cursor: pointer;
            background-color: var(--primary-color, #1976d2);
            color: white;
            border: none;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #1565c0;
        }
        
        .comparison-mode {
            display: none; /* 初始隐藏 */
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        .comparison-mode.active {
            opacity: 1;
            transform: translateY(0);
        }
        
        .country-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .comparison-tip {
            background-color: rgba(33, 150, 243, 0.1);
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 0 4px 4px 0;
            font-size: 14px;
            color: #333;
        }
        
        .comparison-charts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            #asean-map {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>东盟十国数据可视化</h1>
            <p>点击地图上的国家查看详细数据</p>
        </div>
        
        <div class="controls">
            <div class="filter-group">
                <label for="year-filter">年份：</label>
                <select id="year-filter">
                    <option value="2023">2023</option>
                    <option value="2022">2022</option>
                    <option value="2021">2021</option>
                    <option value="2020">2020</option>
                    <option value="2019">2019</option>
                </select>
                
                <label for="data-type">数据类型：</label>
                <select id="data-type">
                    <option value="economic">经济指标</option>
                    <option value="population">人口统计</option>
                    <option value="social">社会发展</option>
                </select>
            </div>
            
            <div class="action-group">
                <button id="compare-btn">开启比较模式</button>
                <button id="export-img-btn">导出图片</button>
                <button id="export-csv-btn">导出CSV</button>
            </div>
        </div>
        
        <div class="main-content">
            <div class="map-container">
                <div id="asean-map"></div>
            </div>
            
            <div class="data-panel" id="data-panel">
                <div class="country-info">
                    <div class="country-name" id="country-name">国家名称</div>
                    <div class="country-basic-info" id="country-basic-info">基本信息将显示在这里</div>
                </div>
                
                <div class="chart-wrapper">
                    <div class="chart-title">GDP增长趋势</div>
                    <div class="chart-container" id="gdp-chart"></div>
                </div>
                
                <div class="chart-wrapper">
                    <div class="chart-title">人口统计</div>
                    <div class="chart-container" id="population-chart"></div>
                </div>
                
                <div class="chart-wrapper">
                    <div class="chart-title">贸易数据</div>
                    <div class="chart-container" id="trade-chart"></div>
                </div>
            </div>
            
            <div class="comparison-mode" id="comparison-panel">
                <div class="comparison-tip">
                    <p><strong>比较模式使用说明：</strong></p>
                    <p>1. 您可以通过下拉菜单选择两个国家进行比较</p>
                    <p>2. 也可以直接点击地图上的国家进行选择</p>
                    <p>3. 点击"比较"按钮或选择两个国家后自动生成对比图表</p>
                </div>
                
                <div class="country-selector">
                    <select id="country-1">
                        <option value="">选择第一个国家</option>
                        <option value="Indonesia">印度尼西亚</option>
                        <option value="Malaysia">马来西亚</option>
                        <option value="Philippines">菲律宾</option>
                        <option value="Singapore">新加坡</option>
                        <option value="Thailand">泰国</option>
                        <option value="Brunei">文莱</option>
                        <option value="Vietnam">越南</option>
                        <option value="Laos">老挝</option>
                        <option value="Myanmar">缅甸</option>
                        <option value="Cambodia">柬埔寨</option>
                    </select>
                    
                    <select id="country-2">
                        <option value="">选择第二个国家</option>
                        <option value="Indonesia">印度尼西亚</option>
                        <option value="Malaysia">马来西亚</option>
                        <option value="Philippines">菲律宾</option>
                        <option value="Singapore">新加坡</option>
                        <option value="Thailand">泰国</option>
                        <option value="Brunei">文莱</option>
                        <option value="Vietnam">越南</option>
                        <option value="Laos">老挝</option>
                        <option value="Myanmar">缅甸</option>
                        <option value="Cambodia">柬埔寨</option>
                    </select>
                    
                    <button id="compare-countries-btn">比较</button>
                </div>
                
                <div class="comparison-charts">
                    <div class="chart-wrapper">
                        <div class="chart-title">GDP对比</div>
                        <div class="chart-container" id="gdp-comparison-chart"></div>
                    </div>
                    
                    <div class="chart-wrapper">
                        <div class="chart-title">人口对比</div>
                        <div class="chart-container" id="population-comparison-chart"></div>
                    </div>
                    
                    <div class="chart-wrapper">
                        <div class="chart-title">贸易数据对比</div>
                        <div class="chart-container" id="trade-comparison-chart"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@4.9.0/dist/echarts.min.js"></script>
    <!-- 引入世界地图数据 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@4.9.0/map/js/world.js"></script>
    
    <!-- 引入自定义脚本 -->
    <script src="js/chartResponsive.js"></script>
    <script src="js/aseanMapVisualization.js"></script>
    
    <!-- 模拟数据 -->
    <script>
        // 这里可以放置模拟数据，后续可以替换为真实数据
        const aseanData = {
            // 各国数据将在这里定义
        };
        
        // 添加调试代码
        console.log('页面加载完成，检查ECharts是否正确初始化');
        console.log('ECharts版本:', echarts.version);
        console.log('世界地图已注册:', echarts.getMap('world') ? '是' : '否');
        
        // 检查地图容器
        console.log('地图容器存在:', document.getElementById('asean-map') ? '是' : '否');
    </script>
</body>
</html>