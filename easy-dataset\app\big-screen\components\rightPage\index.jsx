import React, { PureComponent } from 'react';
import { BorderBox13 } from '@jiaminghi/data-view-react';
import BrowseCategories from './charts/BrowseCategories';
import UserIdentityCategory from './charts/UserIdentityCategory';
import OfflinePortal from './charts/OfflinePortal';
import Feedback from './charts/Feedback';
import { ModuleTitle } from '../../style/globalStyledSet';
import {
  RightPage,
  RightTopBox,
  RightCenterBox,
  RightBottomBox,
} from './style';
import { getStableClassNames } from '../../hooks/useStableLayout';

class index extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      realData: null,
      loading: true, // 初始状态设置为加载中
      error: null
    };
  }

  // 获取标签的显示名称
  getTagDisplayName(tag) {
    if (!tag) return null;

    // 如果是标签对象，优先使用displayName，否则使用label
    if (typeof tag === 'object') {
      return tag.displayName || tag.label;
    }

    // 如果是字符串，直接返回
    return tag;
  }

  // 递归查找顶级父标签
  findTopLevelTag(tag, idToTagMap) {
    if (!tag || !tag.parentId) {
      // 如果没有父标签，则这就是顶级标签
      return tag;
    }

    // 递归查找父标签
    const parentTag = idToTagMap.get(tag.parentId);
    return this.findTopLevelTag(parentTag, idToTagMap);
  }

  // 计算单个项目的领域分布统计（只统计父标签）
  calculateDomainStats(datasets, tags) {
    const domainStats = {};

    // 构建标签树映射
    const tagMap = new Map(); // 标签名 -> 标签对象
    const idToTagMap = new Map(); // ID -> 标签对象

    tags.forEach(tag => {
      tagMap.set(tag.label, tag);
      idToTagMap.set(tag.id, tag);
    });

    // 统计数据集
    datasets.forEach(dataset => {
      if (!dataset.questionLabel ||
        dataset.questionLabel === '待分类' ||
        dataset.questionLabel === '未分类') {
        return;
      }

      // 查找对应的标签
      const tag = tagMap.get(dataset.questionLabel);
      let topLevelTag = null;

      if (tag) {
        // 递归查找顶级父标签
        topLevelTag = this.findTopLevelTag(tag, idToTagMap);
      }

      if (topLevelTag) {
        const displayName = this.getTagDisplayName(topLevelTag);
        if (displayName) {
          domainStats[displayName] = (domainStats[displayName] || 0) + 1;
        }
      } else {
        // 如果找不到对应的标签，跳过统计（不统计未分类的数据）
        console.warn(`未找到标签: ${dataset.questionLabel}`);
      }
    });

    return domainStats;
  }

  // 计算全局领域分布统计（按标签名称汇总）
  calculateGlobalDomainStats(allDatasets, allTags) {
    const globalDomainStats = {};

    // 按项目分组处理标签
    const projectTagsMap = new Map();
    allTags.forEach(tag => {
      if (!projectTagsMap.has(tag.projectId)) {
        projectTagsMap.set(tag.projectId, []);
      }
      projectTagsMap.get(tag.projectId).push(tag);
    });

    // 按项目分组处理数据集
    const projectDatasetsMap = new Map();
    allDatasets.forEach(dataset => {
      if (!projectDatasetsMap.has(dataset.projectId)) {
        projectDatasetsMap.set(dataset.projectId, []);
      }
      projectDatasetsMap.get(dataset.projectId).push(dataset);
    });

    // 为每个项目计算领域统计，然后按名称汇总
    projectTagsMap.forEach((tags, projectId) => {
      const datasets = projectDatasetsMap.get(projectId) || [];
      const projectDomainStats = this.calculateDomainStats(datasets, tags);

      // 将项目的统计结果汇总到全局统计中
      Object.entries(projectDomainStats).forEach(([domain, count]) => {
        globalDomainStats[domain] = (globalDomainStats[domain] || 0) + count;
      });
    });

    return globalDomainStats;
  }

  // 获取真实数据
  async fetchRealData(projectId = null) {
    try {
      // 只在初始加载时设置loading状态，切换国家时由componentDidUpdate设置
      if (!this.state.loading) {
        this.setState({ loading: true, error: null });
      }

      if (projectId) {
        // 有选中国家时，获取特定项目的数据（使用缓存）
        let datasets, tags;

        try {
          if (this.props.dataCacheService) {
            [datasets, tags] = await Promise.all([
              this.props.dataCacheService.fetchProjectDatasets(projectId),
              this.props.dataCacheService.fetchProjectTags(projectId)
            ]);
          } else {
            // 降级到直接API调用
            const [datasetsResponse, tagsResponse] = await Promise.all([
              fetch(`/api/projects/${projectId}/datasets/export`),
              fetch(`/api/projects/${projectId}/distill/tags/all`)
            ]);

            datasets = datasetsResponse.ok ? await datasetsResponse.json() : [];
            tags = tagsResponse.ok ? await tagsResponse.json() : [];
          }
        } catch (error) {
          console.warn(`获取项目 ${projectId} 数据失败:`, error);
          datasets = [];
          tags = [];
        }

        // 如果该国家没有数据，设置为空状态
        if (!datasets || datasets.length === 0) {
          this.setState({
            realData: null,
            loading: false
          });
          return null;
        }

        // 获取项目的真实问题总数
        const statisticsResponse = await fetch(`/api/projects/${projectId}/statistics`);
        let totalQuestions = 0;
        if (statisticsResponse.ok) {
          const statistics = await statisticsResponse.json();
          totalQuestions = statistics.questionCount || 0;
        }

        // 统计各种数据
        const totalDatasets = datasets.length;
        const confirmedDatasets = datasets.filter(item => item.confirmed === 1 || item.confirmed === true).length;

        // 计算领域分布统计
        const domainStats = this.calculateDomainStats(datasets, tags);
        const domainCount = Object.keys(domainStats).length;

        // 计算实际已归类的数据集数量
        const categorizedDatasets = datasets.filter(dataset =>
          dataset.questionLabel &&
          dataset.questionLabel !== '待分类' &&
          dataset.questionLabel !== '未分类'
        ).length;

        // 转换为图表数据格式
        const domainEntries = Object.entries(domainStats)
          .sort(([, a], [, b]) => b - a) // 按数量降序排序
          .slice(0, 6); // 取前6个
        const maxValue = Object.values(domainStats).length > 0 ? Math.max(...Object.values(domainStats)) : 100;
        const browseCategories = {
          data: domainEntries.map(([_, count]) => count || 0),
          indicator: domainEntries.map(([domain, _]) => ({ name: domain || '未知领域', max: maxValue }))
        };

        // 确保至少有6个数据项
        while (browseCategories.data.length < 6) {
          browseCategories.data.push(0);
          browseCategories.indicator.push({ name: '', max: maxValue });
        }

        const userIdentityCategory = domainEntries.map(([domain, count]) => ({ name: domain, value: count }));

        // 计算已确认问题数量（基于已确认数据集的比例估算）
        const confirmedQuestions = totalQuestions > 0 ? Math.round((confirmedDatasets / totalDatasets) * totalQuestions) : 0;

        // 获取真实的时间序列数据（使用缓存）
        let timelineData;
        try {
          if (this.props.dataCacheService) {
            timelineData = await this.props.dataCacheService.fetchTimelineData(projectId, 6);
          } else {
            const timelineResponse = await fetch(`/api/statistics/timeline?projectId=${projectId}&months=6`);
            timelineData = timelineResponse.ok ? await timelineResponse.json() : null;
          }
        } catch (error) {
          console.warn('获取时间序列数据失败:', error);
          timelineData = null;
        }

        // 如果获取失败，使用默认数据
        if (!timelineData) {
          timelineData = {
            xData: ['2月', '3月', '4月', '5月', '6月', '7月'],
            data1: [0, 0, 0, 0, 0, 0],
            data2: [0, 0, 0, 0, 0, 0],
            data3: [0, 0, 0, 0, 0, 0],
            data4: [0, 0, 0, 0, 0, 0]
          };
        }

        const realData = {
          totalDatasets,
          confirmedDatasets,
          domainCount,
          browseCategories,
          userIdentityCategory,
          offline: {
            feedback: [
              { title: '总数据集', value: `${totalDatasets}/${totalQuestions}` },
              { title: '已确认数量', value: `${confirmedDatasets}/${confirmedQuestions}` },
              { title: '已归类数量', value: `${categorizedDatasets}/${totalDatasets}` },
            ],
            offlinePortalData: {
              data1: timelineData.data1, // 问题总数趋势（真实数据）
              data2: timelineData.data2, // 数据集趋势（真实数据）
              data3: timelineData.data3, // 已归类趋势（真实数据）
              data4: timelineData.data4, // 已确认趋势（真实数据）
              xData: timelineData.xData, // 月份标签（真实数据）
            },
          },
        };

        this.setState({ realData, loading: false });
        return realData;
      } else {
        // 没有选中国家时，获取所有项目的数据（使用缓存）
        let allDatasets, allTags;

        try {
          if (this.props.dataCacheService) {
            // 使用缓存服务获取所有项目数据
            const result = await this.props.dataCacheService.fetchAllProjectsData();
            allDatasets = result.allDatasets;
            allTags = result.allTags;
          } else {
            // 降级到直接API调用
            const projects = await fetch('/api/projects').then(r => r.json());

            if (projects.length === 0) {
              throw new Error('没有可用的项目数据');
            }

            allDatasets = [];
            allTags = [];
            for (const project of projects) {
              try {
                const [projectDatasets, projectTags] = await Promise.all([
                  fetch(`/api/projects/${project.id}/datasets/export`).then(r => r.ok ? r.json() : []),
                  fetch(`/api/projects/${project.id}/distill/tags/all`).then(r => r.ok ? r.json() : [])
                ]);

                const datasetsWithProjectId = projectDatasets.map(dataset => ({
                  ...dataset,
                  projectId: dataset.projectId || project.id
                }));
                allDatasets = allDatasets.concat(datasetsWithProjectId);
                allTags = allTags.concat(projectTags);
              } catch (error) {
                console.warn(`获取项目 ${project.id} 数据失败:`, error);
              }
            }
          }
        } catch (error) {
          console.error('获取所有项目数据失败:', error);
          allDatasets = [];
          allTags = [];
        }

        // 获取全局真实问题总数（优先使用缓存）
        let totalQuestions = 0;
        if (this.props.dataCacheService) {
          try {
            const dashboardData = await this.props.dataCacheService.fetchDashboardData();
            totalQuestions = dashboardData.questionCount || 0;
          } catch (error) {
            console.warn('从缓存获取仪表板数据失败，使用直接API调用:', error);
            const dashboardResponse = await fetch('/api/statistics/dashboard');
            if (dashboardResponse.ok) {
              const dashboardData = await dashboardResponse.json();
              totalQuestions = dashboardData.questionCount || 0;
            }
          }
        } else {
          const dashboardResponse = await fetch('/api/statistics/dashboard');
          if (dashboardResponse.ok) {
            const dashboardData = await dashboardResponse.json();
            totalQuestions = dashboardData.questionCount || 0;
          }
        }

        // 统计全局数据
        const totalDatasets = allDatasets.length;
        const confirmedDatasets = allDatasets.filter(item => item.confirmed === 1 || item.confirmed === true).length;

        // 计算全局领域分布统计（按标签名称汇总）
        const domainStats = this.calculateGlobalDomainStats(allDatasets, allTags);
        const domainCount = Object.keys(domainStats).length;

        // 计算全局实际已归类的数据集数量
        const categorizedDatasets = allDatasets.filter(dataset =>
          dataset.questionLabel &&
          dataset.questionLabel !== '待分类' &&
          dataset.questionLabel !== '未分类'
        ).length;


        // 转换为图表数据格式
        const domainEntries = Object.entries(domainStats)
          .sort(([, a], [, b]) => b - a) // 按数量降序排序
          .slice(0, 6); // 取前6个
        const maxValue = Object.values(domainStats).length > 0 ? Math.max(...Object.values(domainStats)) : 100;
        const browseCategories = {
          data: domainEntries.map(([_, count]) => count || 0),
          indicator: domainEntries.map(([domain, _]) => ({ name: domain || '未知领域', max: maxValue }))
        };

        // 确保至少有6个数据项
        while (browseCategories.data.length < 6) {
          browseCategories.data.push(0);
          browseCategories.indicator.push({ name: '', max: maxValue });
        }

        const userIdentityCategory = domainEntries.map(([domain, count]) => ({ name: domain || '未知领域', value: count || 0 }));

        // 计算全局已确认问题数量（基于已确认数据集的比例估算）
        const confirmedQuestions = totalQuestions > 0 ? Math.round((confirmedDatasets / totalDatasets) * totalQuestions) : 0;

        // 获取全局真实的时间序列数据（使用缓存）
        let timelineData;
        try {
          if (this.props.dataCacheService) {
            timelineData = await this.props.dataCacheService.fetchTimelineData(null, 6);
          } else {
            const timelineResponse = await fetch(`/api/statistics/timeline?months=6`);
            timelineData = timelineResponse.ok ? await timelineResponse.json() : null;
          }
        } catch (error) {
          console.warn('获取全局时间序列数据失败:', error);
          timelineData = null;
        }

        // 如果获取失败，使用默认数据
        if (!timelineData) {
          timelineData = {
            xData: ['2月', '3月', '4月', '5月', '6月', '7月'],
            data1: [0, 0, 0, 0, 0, 0],
            data2: [0, 0, 0, 0, 0, 0],
            data3: [0, 0, 0, 0, 0, 0],
            data4: [0, 0, 0, 0, 0, 0]
          };
        }

        const realData = {
          totalDatasets,
          confirmedDatasets,
          domainCount,
          browseCategories,
          userIdentityCategory,
          offline: {
            feedback: [
              { title: '总数据集', value: `${totalDatasets}/${totalQuestions}` },
              { title: '已确认数量', value: `${confirmedDatasets}/${confirmedQuestions}` },
              { title: '已归类数量', value: `${categorizedDatasets}/${totalDatasets}` },
            ],
            offlinePortalData: {
              data1: timelineData.data1, // 问题总数趋势（真实数据）
              data2: timelineData.data2, // 数据集趋势（真实数据）
              data3: timelineData.data3, // 已归类趋势（真实数据）
              data4: timelineData.data4, // 已确认趋势（真实数据）
              xData: timelineData.xData, // 月份标签（真实数据）
            },
          },
        };

        this.setState({ realData, loading: false });
        return realData;
      }
    } catch (error) {
      console.error('获取真实数据失败:', error);
      this.setState({ error: error.message, loading: false });
      return null;
    }
  }

  // 组件挂载时获取数据
  componentDidMount() {
    const { selectedCountry, countryProjectMap } = this.props;
    if (selectedCountry && countryProjectMap) {
      const projectId = countryProjectMap[selectedCountry];
      if (projectId) {
        this.fetchRealData(projectId);
      } else {
        // 选中国家但没有对应项目ID，设置为无数据状态
        this.setState({
          realData: null,
          loading: false,
          error: null
        });
      }
    } else {
      // 没有选中国家时，获取全局数据
      this.fetchRealData();
    }
  }

  // 当选中国家变化时获取数据
  componentDidUpdate(prevProps) {
    const { selectedCountry, countryProjectMap } = this.props;
    if (selectedCountry !== prevProps.selectedCountry) {
      // 先设置加载状态，清除之前的数据
      this.setState({
        loading: true,
        error: null,
        realData: null
      });

      if (selectedCountry && countryProjectMap) {
        const projectId = countryProjectMap[selectedCountry];
        if (projectId) {
          this.fetchRealData(projectId);
        } else {
          // 选中国家但没有对应项目ID，设置为无数据状态
          this.setState({
            realData: null,
            loading: false,
            error: null
          });
        }
      } else {
        // 没有选中国家时，获取全局数据
        this.fetchRealData();
      }
    }
  }

  // 获取空数据状态
  getEmptyData() {
    return {
      browseCategories: {
        data: [0, 0, 0, 0, 0, 0],
        indicator: [
          { name: '暂无数据', max: 100 },
          { name: '', max: 100 },
          { name: '', max: 100 },
          { name: '', max: 100 },
          { name: '', max: 100 },
          { name: '', max: 100 },
        ],
      },
      userIdentityCategory: [
        { name: '暂无数据', value: 0 },
      ],
      offline: {
        feedback: [
          { title: '总数据集', value: '0/0' },
          { title: '已确认数量', value: '0/0' },
          { title: '已归类数量', value: '0/0' },
        ],
        offlinePortalData: {
          data1: [0, 0, 0, 0, 0, 0],
          data2: [0, 0, 0, 0, 0, 0],
          data3: [0, 0, 0, 0, 0, 0],
          data4: [0, 0, 0, 0, 0, 0],
          xData: ['2月', '3月', '4月', '5月', '6月', '7月'],
        },
      },
    };
  }

  // 根据选中国家获取数据
  getDataByCountry = (selectedCountry) => {

    const { realData, loading, error } = this.state;

    // 如果正在加载，显示加载状态（无论是否选中国家）
    if (loading) {
      return {
        browseCategories: {
          data: [0, 0, 0, 0, 0, 0],
          indicator: [
            { name: '数据加载中', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
          ],
        },
        userIdentityCategory: [
          { name: '加载中...', value: 0 },
          { name: '', value: 0 },
          { name: '', value: 0 },
          { name: '', value: 0 },
        ],
        offline: {
          feedback: [
            { title: '总数据集', value: '--' },
            { title: '已确认数量', value: '--' },
            { title: '已归类数量', value: '--' },
          ],
          offlinePortalData: {
            data1: [0, 0, 0, 0, 0, 0],
            data2: [0, 0, 0, 0, 0, 0],
            data3: [0, 0, 0, 0, 0, 0],
            data4: [0, 0, 0, 0, 0, 0],
            xData: ['2月', '3月', '4月', '5月', '6月', '7月'],
          },
        },
      };
    }

    // 如果没有选中国家，但有真实数据，则显示真实数据
    if (!selectedCountry && realData) {
      // 确保userIdentityCategory数据格式正确
      return {
        ...realData,
        userIdentityCategory: realData.userIdentityCategory || []
      };
    }

    // 如果有错误，显示错误信息（无论是否选中国家）
    if (error) {
      return {
        browseCategories: {
          data: [0, 0, 0, 0, 0, 0],
          indicator: [
            { name: '加载失败', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
            { name: '', max: 100 },
          ],
        },
        userIdentityCategory: [
          { name: '加载失败', value: 0 },
        ],
        offline: {
          feedback: [
            { title: '总数据集', value: '数据加载失败' },
            { title: '已确认数量', value: '数据加载失败' },
            { title: '已归类数量', value: '数据加载失败' },
          ],
          offlinePortalData: {
            data1: [0, 0, 0, 0, 0, 0],
            data2: [0, 0, 0, 0, 0, 0],
            data3: [0, 0, 0, 0, 0, 0],
            data4: [0, 0, 0, 0, 0, 0],
            xData: ['2月', '3月', '4月', '5月', '6月', '7月'],
          },
        },
      };
    }

    // 如果没有选中国家且没有真实数据，返回空数据状态
    if (!selectedCountry) {
      return this.getEmptyData();
    }

    // 如果有真实数据，使用真实数据
    if (realData) {
      return realData;
    }

    // 如果选中了国家但没有真实数据，显示无数据状态
    if (selectedCountry) {
      return this.getEmptyData();
    }

    // 默认返回空数据
    return this.getEmptyData();
  }

  render() {
    const { selectedCountry } = this.props;
    const { loading, error } = this.state;
    const currentData = this.getDataByCountry(selectedCountry);
    const { offline, browseCategories, userIdentityCategory } = currentData;

    // 生成稳定性CSS类名
    const stableClasses = getStableClassNames(loading, !loading && !error, error);

    return (
      <RightPage className={stableClasses}>
        <RightTopBox>
          <div className='right-top'>
            <ModuleTitle>
              <i className='iconfont'>&#xe7f7;</i>
              <span>数据集领域分布</span>
            </ModuleTitle>
            <div className='right-top-content'>
              <BrowseCategories
                browseCategories={browseCategories}></BrowseCategories>
              <img
                alt='地球'
                className='earth-gif'
                src='https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/earth-rotate.gif'
              />
            </div>
          </div>
        </RightTopBox>

        <RightCenterBox>
          <ModuleTitle>
            {/* <i className='iconfont'>&#xe7fd;</i> */}
            <span></span>
          </ModuleTitle>
          <UserIdentityCategory
            userIdentityCategory={userIdentityCategory}></UserIdentityCategory>
        </RightCenterBox>

        <RightBottomBox>
          <BorderBox13 className='right-bottom-borderBox13'>
            <div className='right-bottom'>
              <ModuleTitle>
                <i className='iconfont'>&#xe790;</i>
                <span>数据统计概览</span>
              </ModuleTitle>

              {/* 反馈 */}
              <div className='feedback-box'>
                {offline
                  ? offline.feedback.map((item, index) => {
                    return (
                      <div className='feedback-box-item' key={index}>
                        <Feedback FeedbackData={item}></Feedback>
                        <span className='dis-text stable-text'>{item.title}</span>
                      </div>
                    );
                  })
                  : ''}
              </div>
              {/* 门店 */}
              <div className='offline-portal-box'>
                {offline ? (
                  <OfflinePortal
                    offlinePortalData={offline.offlinePortalData}
                  />
                ) : (
                  ''
                )}
              </div>
            </div>
          </BorderBox13>
        </RightBottomBox>
      </RightPage>
    );
  }
}

export default index;
