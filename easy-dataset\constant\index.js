/**
 * 全局常量
 */

export const FILE = {
  MAX_FILE_SIZE: 50 * 1024 * 1024 // 50MB in bytes
};

export const TASK = {
  STATUS: {
    PROCESSING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELED: 3
  },
  TYPE: {
    FILE_PROCESSING: 'file-processing',
    QUESTION_GENERATION: 'question-generation',
    ANSWER_GENERATION: 'answer-generation',
    DATA_DISTILLATION: 'data-distillation',
    DATASET_IMPORT: 'dataset-import'
  }
};

/**
 * 文件记录相关常量
 */
export const FILE_RECORD = {
  // 操作类型
  OPERATION_TYPE: {
    IMPORT: 'import',      // 导入数据集
    EXPORT: 'export',      // 导出数据集
    UPLOAD: 'upload'       // 上传文献
  },
  // 文件格式
  FORMAT: {
    EXCEL: 'excel',
    JSON: 'json',
    JSONL: 'jsonl',
    CSV: 'csv',
    PDF: 'pdf',
    DOCX: 'docx',
    MARKDOWN: 'markdown',
    TXT: 'txt'
  },
  // 文件状态
  STATUS: {
    PROCESSING: 0,  // 处理中
    SUCCESS: 1,     // 成功
    FAILED: 2,      // 失败
    DELETED: 3      // 已删除
  },
  // MIME类型映射
  MIME_TYPES: {
    'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'json': 'application/json',
    'jsonl': 'application/jsonl',
    'csv': 'text/csv',
    'pdf': 'application/pdf',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'markdown': 'text/markdown',
    'txt': 'text/plain'
  }
};

/**
 * 默认领域标签体系
 */
export const DEFAULT_DOMAIN_TAGS = [
  {
    label: '社交互动',
    child: [
      { label: '日常社交' },
      { label: '情感交流' },
      { label: '人际关系' },
      { label: '社会交往' },
      { label: '社交礼仪' }
    ]
  },
  {
    label: '日常生活',
    child: [
      { label: '居家事务' },
      { label: '消费购物' },
      { label: '饮食事务' },
      { label: '服饰穿戴' },
      { label: '交通出行' },
      { label: '时间管理' }
    ]
  },
  {
    label: '学习发展',
    child: [
      { label: '正式教育' },
      { label: '高等教育' },
      { label: '职业培训' },
      { label: '自主学习' },
      { label: '知识获取' }
    ]
  },
  {
    label: '职业工作',
    child: [
      { label: '求职就业' },
      { label: '职场协作' },
      { label: '商务活动' },
      { label: '职业技能' },
      { label: '工作管理' },
      { label: '职业规划' }
    ]
  },
  {
    label: '休闲娱乐',
    child: [
      { label: '文化体验' },
      { label: '旅行探索' },
      { label: '运动健康' },
      { label: '游戏娱乐' },
      { label: '艺术爱好' },
      { label: '社交娱乐' }
    ]
  },
  {
    label: '健康医疗',
    child: [
      { label: '日常保健' },
      { label: '疾病预防' },
      { label: '医疗服务' },
      { label: '康复护理' },
      { label: '心理健康' }
    ]
  },
  {
    label: '家庭事务',
    child: [
      { label: '婚姻关系' },
      { label: '育儿教养' },
      { label: '养老赡养' },
      { label: '家庭财务' },
      { label: '家庭规划' }
    ]
  },
  {
    label: '金融财务',
    child: [
      { label: '日常理财' },
      { label: '投资管理' },
      { label: '借贷信贷' },
      { label: '保险规划' },
      { label: '税务处理' }
    ]
  },
  {
    label: '法律事务',
    child: [
      { label: '民事纠纷' },
      { label: '权益保障' },
      { label: '法律文书' },
      { label: '法律咨询' },
      { label: '公共事务' }
    ]
  },
  {
    label: '科技应用',
    child: [
      { label: '数码设备' },
      { label: '网络应用' },
      { label: '软件工具' },
      { label: '信息安全' },
      { label: '新兴技术' }
    ]
  },
  {
    label: '特殊需求',
    child: [
      { label: '跨文化沟通' },
      { label: '无障碍支持' },
      { label: '应急处理' },
      { label: '特殊场景' }
    ]
  },
  {
    label: '其他',
    child: []
  }
];
