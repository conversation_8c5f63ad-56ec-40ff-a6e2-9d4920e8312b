-- 安全版本：分步执行去除标签序号的SQL脚本
-- 执行前请务必备份数据库！

-- =====================================================
-- 步骤1：创建备份表（可选，用于回滚）
-- =====================================================

-- 备份 Tags 表
CREATE TABLE Tags_backup AS SELECT * FROM Tags;

-- 备份 Questions 表
CREATE TABLE Questions_backup AS SELECT * FROM Questions;

-- 备份 Datasets 表  
CREATE TABLE Datasets_backup AS SELECT * FROM Datasets;

-- =====================================================
-- 步骤2：预览将要更新的数据（执行前先查看）
-- =====================================================

-- 预览 Tags 表中将要更新的记录
SELECT 
    id,
    label as original_label,
    CASE 
        WHEN label REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN label REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
        ELSE label
    END as new_label,
    projectId
FROM Tags 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
ORDER BY projectId, label;

-- 预览 Questions 表中将要更新的记录（限制显示数量）
SELECT 
    id,
    label as original_label,
    CASE 
        WHEN label REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
        WHEN label REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN label REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
        ELSE label
    END as new_label,
    projectId
FROM Questions 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'
ORDER BY projectId, label
LIMIT 50;

-- 预览 Datasets 表中将要更新的记录（限制显示数量）
SELECT 
    id,
    questionLabel as original_label,
    CASE 
        WHEN questionLabel REGEXP '^[一二三四五六七八九十]+、' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[一二三四五六七八九十]+、\\s*', ''))
        WHEN questionLabel REGEXP '^[0-9]+、' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+、\\s*', ''))
        WHEN questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\.[0-9]+\\s+', ''))
        WHEN questionLabel REGEXP '^[0-9]+\\s+' THEN 
            TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\s+', ''))
        ELSE questionLabel
    END as new_label,
    projectId
FROM Datasets 
WHERE 
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+'
ORDER BY projectId, questionLabel
LIMIT 50;

-- =====================================================
-- 步骤3：统计将要更新的记录数量
-- =====================================================

SELECT 
    'Tags' as table_name,
    COUNT(*) as records_to_update
FROM Tags 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 
    'Questions' as table_name,
    COUNT(*) as records_to_update
FROM Questions 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 
    'Datasets' as table_name,
    COUNT(*) as records_to_update
FROM Datasets 
WHERE 
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+';

-- =====================================================
-- 步骤4：执行更新（确认预览结果无误后再执行）
-- =====================================================

-- 开始事务
START TRANSACTION;

-- 更新 Tags 表
UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE label REGEXP '^[一二三四五六七八九十]+、';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE label REGEXP '^[0-9]+、';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Tags 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\s+';

-- 更新 Questions 表
UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE label REGEXP '^[一二三四五六七八九十]+、';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+、\\s*', ''))
WHERE label REGEXP '^[0-9]+、';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Questions 
SET label = TRIM(REGEXP_REPLACE(label, '^[0-9]+\\s+', ''))
WHERE label REGEXP '^[0-9]+\\s+';

-- 更新 Datasets 表
UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[一二三四五六七八九十]+、\\s*', ''))
WHERE questionLabel REGEXP '^[一二三四五六七八九十]+、';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+、\\s*', ''))
WHERE questionLabel REGEXP '^[0-9]+、';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\.[0-9]+\\s+', ''))
WHERE questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+';

UPDATE Datasets 
SET questionLabel = TRIM(REGEXP_REPLACE(questionLabel, '^[0-9]+\\s+', ''))
WHERE questionLabel REGEXP '^[0-9]+\\s+';

-- 提交事务（如果一切正常）
COMMIT;

-- 如果出现问题，可以执行 ROLLBACK; 来回滚

-- =====================================================
-- 步骤5：验证更新结果
-- =====================================================

-- 检查是否还有遗漏的序号格式
SELECT 'Tags - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Tags 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 'Questions - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Questions 
WHERE 
    label REGEXP '^[一二三四五六七八九十]+、'
    OR label REGEXP '^[0-9]+、'
    OR label REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR label REGEXP '^[0-9]+\\s+'

UNION ALL

SELECT 'Datasets - Remaining numbered labels' as check_type, COUNT(*) as count
FROM Datasets 
WHERE 
    questionLabel REGEXP '^[一二三四五六七八九十]+、'
    OR questionLabel REGEXP '^[0-9]+、'
    OR questionLabel REGEXP '^[0-9]+\\.[0-9]+\\s+'
    OR questionLabel REGEXP '^[0-9]+\\s+';

-- =====================================================
-- 步骤6：清理备份表（确认一切正常后执行）
-- =====================================================

-- DROP TABLE Tags_backup;
-- DROP TABLE Questions_backup;
-- DROP TABLE Datasets_backup;
