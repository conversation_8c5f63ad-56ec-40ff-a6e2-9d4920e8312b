(()=>{var e={};e.id=4871,e.ids=[4871],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92048:e=>{"use strict";e.exports=require("fs")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},6005:e=>{"use strict";e.exports=require("node:crypto")},82323:(e,t,a)=>{"use strict";a.r(t),a.d(t,{originalPathname:()=>g,patchFetch:()=>j,requestAsyncStorage:()=>h,routeModule:()=>p,serverHooks:()=>y,staticGenerationAsyncStorage:()=>w});var r={};a.r(r),a.d(r,{GET:()=>f,POST:()=>b,dynamic:()=>u});var n=a(49303),o=a(88716),i=a(60670),l=a(6142),c=a(30383),s=a(3800),d=a(49428);let u="force-dynamic";async function b(e){try{let t=await e.json();if(!t.name)return Response.json({error:"项目名称不能为空"},{status:400});if(await (0,l.isExistByName)(t.name))return Response.json({error:"项目名称已存在"},{status:400});let a=await (0,l.createProject)({name:t.name,description:t.description,country:t.country});try{await (0,s.batchSaveTags)(a.id,d.I0),console.log(`项目 ${a.id} 默认领域标签初始化成功`)}catch(e){console.error(`项目 ${a.id} 默认领域标签初始化失败:`,e)}if(t.reuseConfigFrom){let e=(await (0,c.JH)(t.reuseConfigFrom)).map(e=>(delete e.id,{...e,projectId:a.id}));await (0,c.xB)(e)}return Response.json(a,{status:201})}catch(e){return console.error("创建项目出错:",String(e)),Response.json({error:String(e)},{status:500})}}async function f(){try{let e=await (0,l.getProjects)();return Response.json(e)}catch(e){return console.error("获取项目列表出错:",String(e)),Response.json({error:String(e)},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:y}=p,g="/api/projects/route";function j(){return(0,i.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:w})}},49428:(e,t,a)=>{"use strict";a.d(t,{Cs:()=>r,I0:()=>o,dI:()=>n});let r={STATUS:{PROCESSING:0,COMPLETED:1,FAILED:2,CANCELED:3},TYPE:{FILE_PROCESSING:"file-processing",QUESTION_GENERATION:"question-generation",ANSWER_GENERATION:"answer-generation",DATA_DISTILLATION:"data-distillation",DATASET_IMPORT:"dataset-import"}},n={OPERATION_TYPE:{IMPORT:"import",EXPORT:"export",UPLOAD:"upload"},FORMAT:{EXCEL:"excel",JSON:"json",JSONL:"jsonl",CSV:"csv",PDF:"pdf",DOCX:"docx",MARKDOWN:"markdown",TXT:"txt"},STATUS:{PROCESSING:0,SUCCESS:1,FAILED:2,DELETED:3},MIME_TYPES:{excel:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",json:"application/json",jsonl:"application/jsonl",csv:"text/csv",pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",markdown:"text/markdown",txt:"text/plain"}},o=[{label:"社交互动",child:[{label:"日常社交"},{label:"情感交流"},{label:"人际关系"},{label:"社会交往"},{label:"社交礼仪"}]},{label:"日常生活",child:[{label:"居家事务"},{label:"消费购物"},{label:"饮食事务"},{label:"服饰穿戴"},{label:"交通出行"},{label:"时间管理"}]},{label:"学习发展",child:[{label:"正式教育"},{label:"高等教育"},{label:"职业培训"},{label:"自主学习"},{label:"知识获取"}]},{label:"职业工作",child:[{label:"求职就业"},{label:"职场协作"},{label:"商务活动"},{label:"职业技能"},{label:"工作管理"},{label:"职业规划"}]},{label:"休闲娱乐",child:[{label:"文化体验"},{label:"旅行探索"},{label:"运动健康"},{label:"游戏娱乐"},{label:"艺术爱好"},{label:"社交娱乐"}]},{label:"健康医疗",child:[{label:"日常保健"},{label:"疾病预防"},{label:"医疗服务"},{label:"康复护理"},{label:"心理健康"}]},{label:"家庭事务",child:[{label:"婚姻关系"},{label:"育儿教养"},{label:"养老赡养"},{label:"家庭财务"},{label:"家庭规划"}]},{label:"金融财务",child:[{label:"日常理财"},{label:"投资管理"},{label:"借贷信贷"},{label:"保险规划"},{label:"税务处理"}]},{label:"法律事务",child:[{label:"民事纠纷"},{label:"权益保障"},{label:"法律文书"},{label:"法律咨询"},{label:"公共事务"}]},{label:"科技应用",child:[{label:"数码设备"},{label:"网络应用"},{label:"软件工具"},{label:"信息安全"},{label:"新兴技术"}]},{label:"特殊需求",child:[{label:"跨文化沟通"},{label:"无障碍支持"},{label:"应急处理"},{label:"特殊场景"}]},{label:"其他",child:[]}]},67565:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ensureDbExists:()=>f,ensureDir:()=>w,getProjectRoot:()=>b,readJsonFile:()=>p,writeJsonFile:()=>h});var r=a(24330);a(60166);var n=a(92048),o=a.n(n),i=a(55315),l=a.n(i),c=a(19801),s=a.n(c),d=a(40618);let u="";async function b(){return u||(u=function(){if(process.resourcesPath){let e=String(o().readFileSync(l().join(process.resourcesPath,"root-path.txt")));if(e)return e}if(!process.versions||!process.versions.electron)return l().join(process.cwd(),"local-db");try{let{app:e}=a(66616);return l().join(e.getPath("userData"),"local-db")}catch(e){return console.error("Failed to get user data directory:",String(e),l().join(s().homedir(),".easy-dataset-db")),l().join(s().homedir(),".easy-dataset-db")}}()),u}async function f(){try{await o().promises.access(u)}catch(e){await o().promises.mkdir(u,{recursive:!0})}}async function p(e){try{await o().promises.access(e);let t=await o().promises.readFile(e,"utf8");return JSON.parse(t)}catch(e){return null}}async function h(e,t){let a=`${e}_${Date.now()}.tmp`;try{let r=JSON.stringify(t,null,2);await o().promises.writeFile(a,r,"utf8");try{let t=await o().promises.readFile(a,"utf8");JSON.parse(t),await o().promises.rename(a,e)}catch(e){throw await o().promises.unlink(a).catch(()=>{}),Error(`写入的JSON文件内容无效: ${e.message}`)}return t}catch(t){throw console.error(`写入JSON文件 ${e} 失败:`,t),t}finally{await o().promises.unlink(a).catch(()=>{})}}async function w(e){try{await o().promises.access(e)}catch(t){await o().promises.mkdir(e,{recursive:!0})}}(0,d.h)([b,f,p,h,w]),(0,r.j)("62c7fabcd2e3ee7caf7f462f293b892cc0b14386",b),(0,r.j)("ded1554cc91f298bb2ed3c9cb64c452d8ad4653b",f),(0,r.j)("88f51ddb916fc05f7c693302a1f7cc717fe6b538",p),(0,r.j)("7f894c53dded7fe7d9466fff5abe1a69b2db2354",h),(0,r.j)("332c9473f7cbbb0bc5ae43d3adcf4fc93f5d23bd",w)},4342:(e,t,a)=>{"use strict";a.d(t,{db:()=>n});var r=a(53524);let n=globalThis.prisma||new r.PrismaClient({log:["error"]})},30383:(e,t,a)=>{"use strict";a.d(t,{JH:()=>i,RM:()=>d,aG:()=>s,bu:()=>c,xB:()=>l});var r=a(24330);a(60166);var n=a(4342),o=a(42549);async function i(e){try{return await n.db.modelConfig.findMany({where:{projectId:e}})}catch(e){throw console.error("Failed to get modelConfig by projectId in database"),e}}async function l(e){try{return await n.db.modelConfig.createMany({data:e})}catch(e){throw console.error("Failed to create init modelConfig list in database"),e}}async function c(e){try{return await n.db.modelConfig.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get modelConfig by id in database"),e}}async function s(e){try{return await n.db.modelConfig.delete({where:{id:e}})}catch(e){throw console.error("Failed to delete modelConfig by id in database"),e}}async function d(e){try{return e.id||(e.id=(0,o.x0)(12)),await n.db.modelConfig.upsert({create:e,update:e,where:{id:e.id}})}catch(e){throw console.error("Failed to create modelConfig in database"),e}}(0,a(40618).h)([i,l,c,s,d]),(0,r.j)("5636be5c355e6e25cbd500af345c3685a2465d14",i),(0,r.j)("d561805e679cd84fa8a8fdccd34e62f30947517a",l),(0,r.j)("d11a2c8347d3e80cdce01ffccd9285bd18889165",c),(0,r.j)("7dc497640f9c6e242f0cb81326631dca91213137",s),(0,r.j)("8221e2dcd0cbdd116fbd8cbe4b51b14f573db705",d)},6142:(e,t,a)=>{"use strict";a.r(t),a.d(t,{createProject:()=>b,deleteProject:()=>y,getProject:()=>h,getProjects:()=>p,getTaskConfig:()=>g,isExistByName:()=>f,updateProject:()=>w});var r=a(24330);a(60166);var n=a(92048),o=a.n(n),i=a(55315),l=a.n(i),c=a(67565);let s={textSplitMinLength:1500,textSplitMaxLength:2e3,questionGenerationLength:240,questionMaskRemovingProbability:60,huggingfaceToken:"",concurrencyLimit:5,visionConcurrencyLimit:5};var d=a(4342),u=a(42549);async function b(e){try{let t=(0,u.x0)(12),a=await (0,c.getProjectRoot)(),r=l().join(a,t);return await o().promises.mkdir(r,{recursive:!0}),await o().promises.mkdir(l().join(r,"files"),{recursive:!0}),await d.db.projects.create({data:{id:t,name:e.name,description:e.description,country:e.country}})}catch(e){throw console.error("Failed to create project in database"),e}}async function f(e){try{return await d.db.projects.count({where:{name:e}})>0}catch(e){throw console.error("Failed to get project by name in database"),e}}async function p(){try{return await d.db.projects.findMany({include:{_count:{select:{Datasets:!0,Questions:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get projects in database"),e}}async function h(e){try{return await d.db.projects.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get project by id in database"),e}}async function w(e,t){try{return delete t.projectId,await d.db.projects.update({where:{id:e},data:{...t}})}catch(e){throw console.error("Failed to update project in database"),e}}async function y(e){try{let t=await (0,c.getProjectRoot)(),a=l().join(t,e);return await d.db.projects.delete({where:{id:e}}),o().existsSync(a)&&await o().promises.rm(a,{recursive:!0}),!0}catch(e){return!1}}async function g(e){let t=await (0,c.getProjectRoot)(),a=l().join(t,e),r=l().join(a,"task-config.json");return await (0,c.readJsonFile)(r)||s}(0,a(40618).h)([b,f,p,h,w,y,g]),(0,r.j)("735bd40bd5e797892e593b8fddc2605b8d39c8fd",b),(0,r.j)("bd403a39d7d466f99a4386bb75622a1fd55c733f",f),(0,r.j)("2680d512486ca803a14da96551e1a84226778673",p),(0,r.j)("f9f198a77b5228a5545ccb8d1ddbac6437c363d5",h),(0,r.j)("a20b40853cd9266fb771e0109a9df77229473516",w),(0,r.j)("ed0d857f38cd63a93b7768748cc0f7b89e8627b9",y),(0,r.j)("8e0c652d3cc60dd97f2d2b56a785bf2d4b1ab3a5",g)},3800:(e,t,a)=>{"use strict";a.r(t),a.d(t,{batchSaveTags:()=>h,createTag:()=>s,deleteTag:()=>u,getTags:()=>i,updateTag:()=>d});var r=a(24330);a(60166);var n=a(4342),o=a(50121);async function i(e){try{let t=await l(e);return(0,o.h)(t)}catch(e){return[]}}async function l(e,t=null){let a=await n.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of a){let a=await c(t.id);t.questionCount=await n.db.questions.count({where:{label:{in:a},projectId:e}}),t.child=await l(e,t.id)}return a}async function c(e){let t=[],a=[e];for(;a.length>0;){let e=a.shift(),r=await n.db.tags.findUnique({where:{id:e}});if(r){t.push(r.label);let o=await n.db.tags.findMany({where:{parentId:e},select:{id:!0}});a.push(...o.map(e=>e.id))}}return t}async function s(e,t,a){try{let r=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return console.log(`标签已存在: ${t}，返回现有标签`),r;let o={projectId:e,label:t};return a&&(o.parentId=a),await n.db.tags.create({data:o})}catch(r){if("P2002"===r.code&&r.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let r=await n.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return r}throw console.error("Error insert tags db:",r),r}}async function d(e,t){try{let a=await n.db.tags.findUnique({where:{id:t}});if(!a)throw Error(`标签不存在: ${t}`);let r=a.label,o=a.projectId,i=await n.db.tags.update({where:{id:t},data:{label:e}});return r!==e&&(console.log(`标签名称从 "${r}" 更新为 "${e}"，开始同步更新相关数据`),await n.db.questions.updateMany({where:{label:r,projectId:o},data:{label:e}}),console.log(`已更新问题表中的标签: ${r} -> ${e}`),await n.db.datasets.updateMany({where:{questionLabel:r,projectId:o},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${r} -> ${e}`)),i}catch(e){throw console.error("Error update tags db:",e),e}}async function u(e){try{console.log(`开始删除标签: ${e}`);let t=await n.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let a=await b(e,t.projectId);for(let e of(console.log(`找到 ${a.length} 个子标签需要删除`),a.reverse()))await p(e.label,e.projectId),await f(e.label,e.projectId),await n.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await p(t.label,t.projectId),await f(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await n.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function b(e,t){let a=[];async function r(e){let o=await n.db.tags.findMany({where:{parentId:e,projectId:t}});if(o.length>0)for(let e of(a.push(...o),o))await r(e.id)}return await r(e),a}async function f(e,t){try{await n.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function p(e,t){try{await n.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function h(e,t){try{await n.db.tags.deleteMany({where:{projectId:e}}),await w(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function w(e,t,a=null){for(let r of t){let t=await n.db.tags.create({data:{projectId:e,label:r.label,parentId:a}});r.child&&r.child.length>0&&await w(e,r.child,t.id)}}(0,a(40618).h)([i,s,d,u,h]),(0,r.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",i),(0,r.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",s),(0,r.j)("745145798d6802bd295e673536ce529c25576c3c",d),(0,r.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",u),(0,r.j)("1e89c922534cbdd6a59e5783551aafd799be4734",h)},50121:(e,t,a)=>{"use strict";function r(e,t){return e&&Array.isArray(t)?function t(a){for(let r of a){if(r.label===e)return r;if(r.child&&r.child.length>0){let e=t(r.child);if(e)return e}}return null}(t):null}a.d(t,{E:()=>r,h:()=>function e(t,a=0,r=0){return Array.isArray(t)?t.map((t,n)=>{let o;if(0===a){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let a=Math.floor(e/10),r=e%10;return 1===a?0===r?"十":"十"+t[r]:t[a]+"十"+(0===r?"":t[r])}(n+1);o=`${e}、${t.label}`}else o=`${r+1}.${n+1} ${t.label}`;let i={...t,displayLabel:o,displayName:t.label};return t.child&&t.child.length>0&&(i.child=e(t.child,a+1,n)),t.children&&t.children.length>0&&(i.children=e(t.children,a+1,n)),i}):[]}})},66616:(e,t,a)=>{let r=a(92048),n=a(55315),o=n.join(__dirname,"path.txt");e.exports=function(){let e;if(r.existsSync(o)&&(e=r.readFileSync(o,"utf-8")),process.env.ELECTRON_OVERRIDE_DIST_PATH)return n.join(process.env.ELECTRON_OVERRIDE_DIST_PATH,e||"electron");if(e)return n.join(__dirname,"dist",e);throw Error("Electron failed to install correctly, please delete node_modules/electron and try installing again")}()},49303:(e,t,a)=>{"use strict";e.exports=a(30517)},42549:(e,t,a)=>{"use strict";let r,n;a.d(t,{x0:()=>i});var o=a(6005);function i(e=21){var t;t=e|=0,!r||r.length<t?(r=Buffer.allocUnsafe(128*t),o.webcrypto.getRandomValues(r),n=0):n+t>r.length&&(o.webcrypto.getRandomValues(r),n=0),n+=t;let a="";for(let t=n-e;t<n;t++)a+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&r[t]];return a}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,8341],()=>a(82323));module.exports=r})();