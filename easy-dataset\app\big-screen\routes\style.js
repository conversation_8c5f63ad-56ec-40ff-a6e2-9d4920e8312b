import styled from 'styled-components';

export const IndexPageStyle = styled.div`
  position: relative;
  overflow-x: hidden; /* 只隐藏横向滚动条 */
  overflow-y: auto;   /* 允许竖向滚动条 */
  margin: 0;
  padding: 10px 0 0 0;
  background: url('https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/pageBg.png') center center no-repeat;
  background-size: cover;
  width: 100%;
  max-width: 100vw; /* 强制最大宽度不超出视口 */
  box-sizing: border-box;
`;
export const IndexPageContent = styled.div`
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
  box-sizing: border-box; /* 确保容器尺寸计算正确 */
  overflow-x: hidden; /* 防止flex内容溢出产生横向滚动 */

  .center-page {
    width: 11.5rem;
    flex-shrink: 0; /* 防止被压缩 */
    box-sizing: border-box; /* 确保尺寸计算正确 */
    overflow-x: hidden; /* 防止中间区域产生横向滚动 */
  }
`;
