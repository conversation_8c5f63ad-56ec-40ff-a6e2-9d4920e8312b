(()=>{var e={};e.id=9768,e.ids=[9768],e.modules={53524:e=>{"use strict";e.exports=require("@prisma/client")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},92048:e=>{"use strict";e.exports=require("fs")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},6005:e=>{"use strict";e.exports=require("node:crypto")},2070:(e,t,a)=>{"use strict";a.r(t),a.d(t,{originalPathname:()=>w,patchFetch:()=>m,requestAsyncStorage:()=>f,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>h});var r={};a.r(r),a.d(r,{POST:()=>u,PUT:()=>b});var l=a(49303),s=a(88716),n=a(60670),o=a(87070),i=a(6142),c=a(3800),d=a(49428);async function u(e){try{let e=await (0,i.getProjects)();if(!e||0===e.length)return o.NextResponse.json({message:"没有找到任何项目"},{status:200});let t=[],a=0,r=0,l=0;for(let s of e)try{let e=await (0,c.getTags)(s.id);if(e&&e.length>0){t.push({projectId:s.id,projectName:s.name,status:"skipped",message:"项目已存在标签，跳过初始化",tagCount:e.length}),r++;continue}await (0,c.batchSaveTags)(s.id,d.I0);let l=await (0,c.getTags)(s.id);t.push({projectId:s.id,projectName:s.name,status:"success",message:"默认领域标签初始化成功",tagCount:l.length}),a++,console.log(`项目 ${s.id} (${s.name}) 默认领域标签初始化成功`)}catch(e){t.push({projectId:s.id,projectName:s.name,status:"error",message:e.message||"初始化失败",error:e.message}),l++,console.error(`项目 ${s.id} (${s.name}) 默认领域标签初始化失败:`,e)}return o.NextResponse.json({success:!0,message:"批量初始化完成",summary:{totalProjects:e.length,successCount:a,skipCount:r,errorCount:l},results:t},{status:200})}catch(e){return console.error("批量初始化默认领域标签失败:",e),o.NextResponse.json({error:e.message||"批量初始化默认领域标签失败"},{status:500})}}async function b(e){try{let e=await (0,i.getProjects)();if(!e||0===e.length)return o.NextResponse.json({message:"没有找到任何项目"},{status:200});let t=[],a=0,r=0;for(let l of e)try{await (0,c.batchSaveTags)(l.id,d.I0);let e=await (0,c.getTags)(l.id);t.push({projectId:l.id,projectName:l.name,status:"success",message:"默认领域标签强制重新初始化成功",tagCount:e.length}),a++,console.log(`项目 ${l.id} (${l.name}) 默认领域标签强制重新初始化成功`)}catch(e){t.push({projectId:l.id,projectName:l.name,status:"error",message:e.message||"强制重新初始化失败",error:e.message}),r++,console.error(`项目 ${l.id} (${l.name}) 默认领域标签强制重新初始化失败:`,e)}return o.NextResponse.json({success:!0,message:"批量强制重新初始化完成",summary:{totalProjects:e.length,successCount:a,errorCount:r},results:t},{status:200})}catch(e){return console.error("批量强制重新初始化默认领域标签失败:",e),o.NextResponse.json({error:e.message||"批量强制重新初始化默认领域标签失败"},{status:500})}}let p=new l.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/projects/batch-init-tags/route",pathname:"/api/projects/batch-init-tags",filename:"route",bundlePath:"app/api/projects/batch-init-tags/route"},resolvedPagePath:"D:\\office\\niuma-dataset\\easy-dataset\\app\\api\\projects\\batch-init-tags\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:h,serverHooks:g}=p,w="/api/projects/batch-init-tags/route";function m(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:h})}},49428:(e,t,a)=>{"use strict";a.d(t,{Cs:()=>r,I0:()=>s,dI:()=>l});let r={STATUS:{PROCESSING:0,COMPLETED:1,FAILED:2,CANCELED:3},TYPE:{FILE_PROCESSING:"file-processing",QUESTION_GENERATION:"question-generation",ANSWER_GENERATION:"answer-generation",DATA_DISTILLATION:"data-distillation",DATASET_IMPORT:"dataset-import"}},l={OPERATION_TYPE:{IMPORT:"import",EXPORT:"export",UPLOAD:"upload"},FORMAT:{EXCEL:"excel",JSON:"json",JSONL:"jsonl",CSV:"csv",PDF:"pdf",DOCX:"docx",MARKDOWN:"markdown",TXT:"txt"},STATUS:{PROCESSING:0,SUCCESS:1,FAILED:2,DELETED:3},MIME_TYPES:{excel:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",json:"application/json",jsonl:"application/jsonl",csv:"text/csv",pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",markdown:"text/markdown",txt:"text/plain"}},s=[{label:"社交互动",child:[{label:"日常社交"},{label:"情感交流"},{label:"人际关系"},{label:"社会交往"},{label:"社交礼仪"}]},{label:"日常生活",child:[{label:"居家事务"},{label:"消费购物"},{label:"饮食事务"},{label:"服饰穿戴"},{label:"交通出行"},{label:"时间管理"}]},{label:"学习发展",child:[{label:"正式教育"},{label:"高等教育"},{label:"职业培训"},{label:"自主学习"},{label:"知识获取"}]},{label:"职业工作",child:[{label:"求职就业"},{label:"职场协作"},{label:"商务活动"},{label:"职业技能"},{label:"工作管理"},{label:"职业规划"}]},{label:"休闲娱乐",child:[{label:"文化体验"},{label:"旅行探索"},{label:"运动健康"},{label:"游戏娱乐"},{label:"艺术爱好"},{label:"社交娱乐"}]},{label:"健康医疗",child:[{label:"日常保健"},{label:"疾病预防"},{label:"医疗服务"},{label:"康复护理"},{label:"心理健康"}]},{label:"家庭事务",child:[{label:"婚姻关系"},{label:"育儿教养"},{label:"养老赡养"},{label:"家庭财务"},{label:"家庭规划"}]},{label:"金融财务",child:[{label:"日常理财"},{label:"投资管理"},{label:"借贷信贷"},{label:"保险规划"},{label:"税务处理"}]},{label:"法律事务",child:[{label:"民事纠纷"},{label:"权益保障"},{label:"法律文书"},{label:"法律咨询"},{label:"公共事务"}]},{label:"科技应用",child:[{label:"数码设备"},{label:"网络应用"},{label:"软件工具"},{label:"信息安全"},{label:"新兴技术"}]},{label:"特殊需求",child:[{label:"跨文化沟通"},{label:"无障碍支持"},{label:"应急处理"},{label:"特殊场景"}]},{label:"其他",child:[]}]},67565:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ensureDbExists:()=>p,ensureDir:()=>g,getProjectRoot:()=>b,readJsonFile:()=>f,writeJsonFile:()=>h});var r=a(24330);a(60166);var l=a(92048),s=a.n(l),n=a(55315),o=a.n(n),i=a(19801),c=a.n(i),d=a(40618);let u="";async function b(){return u||(u=function(){if(process.resourcesPath){let e=String(s().readFileSync(o().join(process.resourcesPath,"root-path.txt")));if(e)return e}if(!process.versions||!process.versions.electron)return o().join(process.cwd(),"local-db");try{let{app:e}=a(66616);return o().join(e.getPath("userData"),"local-db")}catch(e){return console.error("Failed to get user data directory:",String(e),o().join(c().homedir(),".easy-dataset-db")),o().join(c().homedir(),".easy-dataset-db")}}()),u}async function p(){try{await s().promises.access(u)}catch(e){await s().promises.mkdir(u,{recursive:!0})}}async function f(e){try{await s().promises.access(e);let t=await s().promises.readFile(e,"utf8");return JSON.parse(t)}catch(e){return null}}async function h(e,t){let a=`${e}_${Date.now()}.tmp`;try{let r=JSON.stringify(t,null,2);await s().promises.writeFile(a,r,"utf8");try{let t=await s().promises.readFile(a,"utf8");JSON.parse(t),await s().promises.rename(a,e)}catch(e){throw await s().promises.unlink(a).catch(()=>{}),Error(`写入的JSON文件内容无效: ${e.message}`)}return t}catch(t){throw console.error(`写入JSON文件 ${e} 失败:`,t),t}finally{await s().promises.unlink(a).catch(()=>{})}}async function g(e){try{await s().promises.access(e)}catch(t){await s().promises.mkdir(e,{recursive:!0})}}(0,d.h)([b,p,f,h,g]),(0,r.j)("62c7fabcd2e3ee7caf7f462f293b892cc0b14386",b),(0,r.j)("ded1554cc91f298bb2ed3c9cb64c452d8ad4653b",p),(0,r.j)("88f51ddb916fc05f7c693302a1f7cc717fe6b538",f),(0,r.j)("7f894c53dded7fe7d9466fff5abe1a69b2db2354",h),(0,r.j)("332c9473f7cbbb0bc5ae43d3adcf4fc93f5d23bd",g)},4342:(e,t,a)=>{"use strict";a.d(t,{db:()=>l});var r=a(53524);let l=globalThis.prisma||new r.PrismaClient({log:["error"]})},6142:(e,t,a)=>{"use strict";a.r(t),a.d(t,{createProject:()=>b,deleteProject:()=>w,getProject:()=>h,getProjects:()=>f,getTaskConfig:()=>m,isExistByName:()=>p,updateProject:()=>g});var r=a(24330);a(60166);var l=a(92048),s=a.n(l),n=a(55315),o=a.n(n),i=a(67565);let c={textSplitMinLength:1500,textSplitMaxLength:2e3,questionGenerationLength:240,questionMaskRemovingProbability:60,huggingfaceToken:"",concurrencyLimit:5,visionConcurrencyLimit:5};var d=a(4342),u=a(42549);async function b(e){try{let t=(0,u.x0)(12),a=await (0,i.getProjectRoot)(),r=o().join(a,t);return await s().promises.mkdir(r,{recursive:!0}),await s().promises.mkdir(o().join(r,"files"),{recursive:!0}),await d.db.projects.create({data:{id:t,name:e.name,description:e.description,country:e.country}})}catch(e){throw console.error("Failed to create project in database"),e}}async function p(e){try{return await d.db.projects.count({where:{name:e}})>0}catch(e){throw console.error("Failed to get project by name in database"),e}}async function f(){try{return await d.db.projects.findMany({include:{_count:{select:{Datasets:!0,Questions:!0}}},orderBy:{createAt:"desc"}})}catch(e){throw console.error("Failed to get projects in database"),e}}async function h(e){try{return await d.db.projects.findUnique({where:{id:e}})}catch(e){throw console.error("Failed to get project by id in database"),e}}async function g(e,t){try{return delete t.projectId,await d.db.projects.update({where:{id:e},data:{...t}})}catch(e){throw console.error("Failed to update project in database"),e}}async function w(e){try{let t=await (0,i.getProjectRoot)(),a=o().join(t,e);return await d.db.projects.delete({where:{id:e}}),s().existsSync(a)&&await s().promises.rm(a,{recursive:!0}),!0}catch(e){return!1}}async function m(e){let t=await (0,i.getProjectRoot)(),a=o().join(t,e),r=o().join(a,"task-config.json");return await (0,i.readJsonFile)(r)||c}(0,a(40618).h)([b,p,f,h,g,w,m]),(0,r.j)("735bd40bd5e797892e593b8fddc2605b8d39c8fd",b),(0,r.j)("bd403a39d7d466f99a4386bb75622a1fd55c733f",p),(0,r.j)("2680d512486ca803a14da96551e1a84226778673",f),(0,r.j)("f9f198a77b5228a5545ccb8d1ddbac6437c363d5",h),(0,r.j)("a20b40853cd9266fb771e0109a9df77229473516",g),(0,r.j)("ed0d857f38cd63a93b7768748cc0f7b89e8627b9",w),(0,r.j)("8e0c652d3cc60dd97f2d2b56a785bf2d4b1ab3a5",m)},3800:(e,t,a)=>{"use strict";a.r(t),a.d(t,{batchSaveTags:()=>h,createTag:()=>c,deleteTag:()=>u,getTags:()=>n,updateTag:()=>d});var r=a(24330);a(60166);var l=a(4342),s=a(50121);async function n(e){try{let t=await o(e);return(0,s.h)(t)}catch(e){return[]}}async function o(e,t=null){let a=await l.db.tags.findMany({where:{parentId:t,projectId:e}});for(let t of a){let a=await i(t.id);t.questionCount=await l.db.questions.count({where:{label:{in:a},projectId:e}}),t.child=await o(e,t.id)}return a}async function i(e){let t=[],a=[e];for(;a.length>0;){let e=a.shift(),r=await l.db.tags.findUnique({where:{id:e}});if(r){t.push(r.label);let s=await l.db.tags.findMany({where:{parentId:e},select:{id:!0}});a.push(...s.map(e=>e.id))}}return t}async function c(e,t,a){try{let r=await l.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return console.log(`标签已存在: ${t}，返回现有标签`),r;let s={projectId:e,label:t};return a&&(s.parentId=a),await l.db.tags.create({data:s})}catch(r){if("P2002"===r.code&&r.meta?.target?.includes("projectId_label_parentId")){console.log(`标签创建冲突，尝试查找现有标签: ${t}`);let r=await l.db.tags.findFirst({where:{projectId:e,label:t,parentId:a||null}});if(r)return r}throw console.error("Error insert tags db:",r),r}}async function d(e,t){try{let a=await l.db.tags.findUnique({where:{id:t}});if(!a)throw Error(`标签不存在: ${t}`);let r=a.label,s=a.projectId,n=await l.db.tags.update({where:{id:t},data:{label:e}});return r!==e&&(console.log(`标签名称从 "${r}" 更新为 "${e}"，开始同步更新相关数据`),await l.db.questions.updateMany({where:{label:r,projectId:s},data:{label:e}}),console.log(`已更新问题表中的标签: ${r} -> ${e}`),await l.db.datasets.updateMany({where:{questionLabel:r,projectId:s},data:{questionLabel:e}}),console.log(`已更新数据集表中的标签: ${r} -> ${e}`)),n}catch(e){throw console.error("Error update tags db:",e),e}}async function u(e){try{console.log(`开始删除标签: ${e}`);let t=await l.db.tags.findUnique({where:{id:e}});if(!t)throw Error(`标签不存在: ${e}`);let a=await b(e,t.projectId);for(let e of(console.log(`找到 ${a.length} 个子标签需要删除`),a.reverse()))await f(e.label,e.projectId),await p(e.label,e.projectId),await l.db.tags.delete({where:{id:e.id}}),console.log(`删除子标签: ${e.id} (${e.label})`);return await f(t.label,t.projectId),await p(t.label,t.projectId),console.log(`删除主标签: ${e} (${t.label})`),await l.db.tags.delete({where:{id:e}})}catch(e){throw console.error("删除标签时出错:",e),e}}async function b(e,t){let a=[];async function r(e){let s=await l.db.tags.findMany({where:{parentId:e,projectId:t}});if(s.length>0)for(let e of(a.push(...s),s))await r(e.id)}return await r(e),a}async function p(e,t){try{await l.db.questions.deleteMany({where:{label:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关问题时出错:`,t),t}}async function f(e,t){try{await l.db.datasets.deleteMany({where:{questionLabel:e,projectId:t}})}catch(t){throw console.error(`删除标签 "${e}" 相关数据集时出错:`,t),t}}async function h(e,t){try{await l.db.tags.deleteMany({where:{projectId:e}}),await g(e,t)}catch(e){throw console.error("Error insert tags db:",e),e}}async function g(e,t,a=null){for(let r of t){let t=await l.db.tags.create({data:{projectId:e,label:r.label,parentId:a}});r.child&&r.child.length>0&&await g(e,r.child,t.id)}}(0,a(40618).h)([n,c,d,u,h]),(0,r.j)("9c860c7f33d6cdfc414e0665220b82b8ff3e3a4b",n),(0,r.j)("0cdb8d8fab75468109d75016bce34d382a0ee575",c),(0,r.j)("745145798d6802bd295e673536ce529c25576c3c",d),(0,r.j)("13033051c4c3f8fd416d53dfbef07392f105a5f3",u),(0,r.j)("1e89c922534cbdd6a59e5783551aafd799be4734",h)},50121:(e,t,a)=>{"use strict";function r(e,t){return e&&Array.isArray(t)?function t(a){for(let r of a){if(r.label===e)return r;if(r.child&&r.child.length>0){let e=t(r.child);if(e)return e}}return null}(t):null}a.d(t,{E:()=>r,h:()=>function e(t,a=0,r=0){return Array.isArray(t)?t.map((t,l)=>{let s;if(0===a){let e=function(e){let t=["","一","二","三","四","五","六","七","八","九"];if(e<1||e>99)return e.toString();if(e<=10)return["","一","二","三","四","五","六","七","八","九","十"][e];let a=Math.floor(e/10),r=e%10;return 1===a?0===r?"十":"十"+t[r]:t[a]+"十"+(0===r?"":t[r])}(l+1);s=`${e}、${t.label}`}else s=`${r+1}.${l+1} ${t.label}`;let n={...t,displayLabel:s,displayName:t.label};return t.child&&t.child.length>0&&(n.child=e(t.child,a+1,l)),t.children&&t.children.length>0&&(n.children=e(t.children,a+1,l)),n}):[]}})},66616:(e,t,a)=>{let r=a(92048),l=a(55315),s=l.join(__dirname,"path.txt");e.exports=function(){let e;if(r.existsSync(s)&&(e=r.readFileSync(s,"utf-8")),process.env.ELECTRON_OVERRIDE_DIST_PATH)return l.join(process.env.ELECTRON_OVERRIDE_DIST_PATH,e||"electron");if(e)return l.join(__dirname,"dist",e);throw Error("Electron failed to install correctly, please delete node_modules/electron and try installing again")}()},42549:(e,t,a)=>{"use strict";let r,l;a.d(t,{x0:()=>n});var s=a(6005);function n(e=21){var t;t=e|=0,!r||r.length<t?(r=Buffer.allocUnsafe(128*t),s.webcrypto.getRandomValues(r),l=0):l+t>r.length&&(s.webcrypto.getRandomValues(r),l=0),l+=t;let a="";for(let t=l-e;t<l;t++)a+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&r[t]];return a}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,5972,8341],()=>a(2070));module.exports=r})();