/**
 * 响应式适配系统 - 基于原项目flexible.js
 * 确保在不同屏幕尺寸下底部区域完全可见
 */

(function(win, lib) {
  var doc = win.document;
  var docEl = doc.documentElement;
  var flexible = lib.flexible || (lib.flexible = {});

  // 设置根字体大小的函数
  function refreshRem() {
    var width = docEl.getBoundingClientRect().width;
    
    // 最小1366px，最大适配2560px（与原项目保持一致）
    if (width < 1366) {
      width = 1366;
    } else if (width > 2560) {
      width = 2560;
    }
    
    // 设置成24等份，设计稿是1920px的，这样1rem就是80px
    var rem = width / 24;
    docEl.style.fontSize = rem + "px";
    flexible.rem = win.rem = rem;
    
    // 确保底部区域可见的动态调整
    adjustBottomVisibility();
  }

  // 动态调整底部区域可见性
  function adjustBottomVisibility() {
    var contentWrapper = doc.querySelector('.content-wrapper');
    var header = doc.querySelector('.header');
    
    if (contentWrapper && header) {
      var headerHeight = header.offsetHeight;
      var windowHeight = win.innerHeight;
      var availableHeight = windowHeight - headerHeight;
      
      // 设置内容区域的最大高度，确保底部可见
      contentWrapper.style.maxHeight = availableHeight + 'px';
      
      // 添加额外的底部padding确保最后的内容完全可见
      var lastSection = doc.querySelector('.dashboard-section:last-child');
      if (lastSection) {
        var currentRem = parseFloat(docEl.style.fontSize);
        var bottomPadding = Math.max(currentRem * 0.625, 40); // 至少40px
        contentWrapper.style.paddingBottom = bottomPadding + 'px';
      }
    }
  }

  // 监听窗口大小变化
  var resizeTimer;
  win.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(function() {
      refreshRem();
      // 重新初始化图表以适应新尺寸
      if (typeof initChartResponsive === 'function') {
        initChartResponsive();
      }
    }, 300);
  }, false);

  // 监听页面显示事件
  win.addEventListener('pageshow', function(e) {
    if (e.persisted) {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(refreshRem, 300);
    }
  }, false);

  // 页面加载完成后初始化
  if (doc.readyState === "complete") {
    refreshRem();
  } else {
    doc.addEventListener('DOMContentLoaded', function() {
      refreshRem();
    }, false);
  }

  // 暴露方法
  flexible.refreshRem = refreshRem;
  flexible.adjustBottomVisibility = adjustBottomVisibility;
  
  flexible.rem2px = function(d) {
    var val = parseFloat(d) * this.rem;
    if (typeof d === "string" && d.match(/rem$/)) {
      val += "px";
    }
    return val;
  };
  
  flexible.px2rem = function(d) {
    var val = parseFloat(d) / this.rem;
    if (typeof d === "string" && d.match(/px$/)) {
      val += "rem";
    }
    return val;
  };

})(window, window["lib"] || (window["lib"] = {}));

// 额外的底部可见性检查函数
function ensureBottomVisibility() {
  // 检查是否有内容被遮挡
  var contentWrapper = document.querySelector('.content-wrapper');
  var lastSection = document.querySelector('.dashboard-section:last-child');
  
  if (contentWrapper && lastSection) {
    var wrapperRect = contentWrapper.getBoundingClientRect();
    var lastSectionRect = lastSection.getBoundingClientRect();
    
    // 如果最后一个section的底部超出了可视区域
    if (lastSectionRect.bottom > wrapperRect.bottom) {
      var additionalPadding = lastSectionRect.bottom - wrapperRect.bottom + 20;
      var currentPadding = parseFloat(getComputedStyle(contentWrapper).paddingBottom) || 0;
      contentWrapper.style.paddingBottom = (currentPadding + additionalPadding) + 'px';
    }
  }
}

// 在页面加载完成后执行底部可见性检查
document.addEventListener('DOMContentLoaded', function() {
  setTimeout(ensureBottomVisibility, 1000);
});
